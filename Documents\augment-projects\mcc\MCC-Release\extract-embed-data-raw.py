#!/usr/bin/env python3
"""
Extract and analyze the exact embed_data_raw content from message ab872802
"""

import json

def extract_and_analyze_embed_data_raw():
    """Extract the exact embed_data_raw content and analyze it"""
    
    target_id = "ab872802"
    
    print("🔬 EXTRACTING EMBED_DATA_RAW FROM MESSAGE ab872802")
    print("=" * 70)
    
    try:
        with open("discord_queue.json", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        target_message = None
        for line in lines:
            if line.strip() and target_id in line:
                try:
                    target_message = json.loads(line.strip())
                    break
                except json.JSONDecodeError:
                    continue
        
        if not target_message:
            print(f"❌ Message {target_id} not found")
            return
        
        embed_data_raw = target_message.get('embed_data_raw', '')
        if not embed_data_raw:
            print(f"❌ No embed_data_raw field found")
            return
        
        print(f"✅ Found embed_data_raw field")
        print(f"📏 Length: {len(embed_data_raw)} characters")
        
        # Find ALL control characters
        control_chars = []
        for i, char in enumerate(embed_data_raw):
            char_code = ord(char)
            if char_code < 32 and char not in ['\n', '\r', '\t']:
                control_chars.append({
                    'position': i,
                    'char': repr(char),
                    'ord': char_code,
                    'hex': f"0x{char_code:02X}",
                    'description': get_control_char_description(char_code)
                })
        
        if control_chars:
            print(f"❌ FOUND {len(control_chars)} CONTROL CHARACTERS:")
            for ctrl in control_chars:
                print(f"   Position {ctrl['position']}: {ctrl['char']} (ord: {ctrl['ord']}, hex: {ctrl['hex']}) - {ctrl['description']}")
                
                # Show context around each control character
                start = max(0, ctrl['position'] - 10)
                end = min(len(embed_data_raw), ctrl['position'] + 10)
                context = embed_data_raw[start:end]
                print(f"      Context: {repr(context)}")
        else:
            print(f"✅ No control characters found in embed_data_raw")
        
        # Focus on position 94-95 (where the error occurs in isolated parsing)
        print(f"\n📍 DETAILED ANALYSIS OF POSITION 94-95:")
        if len(embed_data_raw) > 93:
            char_94 = embed_data_raw[93]
            char_code = ord(char_94)
            print(f"   Position 94: {repr(char_94)} (ord: {char_code}, hex: 0x{char_code:02X})")
            if char_code < 32 and char_94 not in ['\n', '\r', '\t']:
                print(f"      🚨 CONTROL CHARACTER FOUND AT POSITION 94!")
        
        if len(embed_data_raw) > 94:
            char_95 = embed_data_raw[94]
            char_code = ord(char_95)
            print(f"   Position 95: {repr(char_95)} (ord: {char_code}, hex: 0x{char_code:02X})")
            if char_code < 32 and char_95 not in ['\n', '\r', '\t']:
                print(f"      🚨 CONTROL CHARACTER FOUND AT POSITION 95!")
        
        # Show detailed context around position 94
        print(f"\n📍 CONTEXT AROUND POSITION 94:")
        start = max(0, 85)
        end = min(len(embed_data_raw), 105)
        context = embed_data_raw[start:end]
        
        print(f"   Range: {start}-{end}")
        print(f"   Text: {repr(context)}")
        
        # Character-by-character breakdown
        print(f"\n   CHARACTER-BY-CHARACTER BREAKDOWN:")
        for i, char in enumerate(context):
            pos = start + i
            char_code = ord(char)
            is_control = char_code < 32 and char not in ['\n', '\r', '\t']
            marker = " <<<< CONTROL CHAR" if is_control else ""
            print(f"     Pos {pos:3d}: {repr(char):>6} (ord: {char_code:3d}, hex: 0x{char_code:02X}){marker}")
        
        # Try to parse the embed_data_raw directly
        print(f"\n🧪 DIRECT JSON PARSING TEST:")
        try:
            parsed = json.loads(embed_data_raw)
            print(f"   ✅ Direct parsing: SUCCESS")
            
            # Look for player names in the parsed data
            embeds = parsed.get('embeds', [])
            if embeds:
                fields = embeds[0].get('fields', [])
                for field in fields:
                    value = field.get('value', '')
                    if 'points' in value:  # This looks like player data
                        print(f"   📊 Player data: {value[:100]}...")
                        
                        # Check each line for control characters
                        lines = value.split('\\n')
                        for line_num, line in enumerate(lines):
                            for char_pos, char in enumerate(line):
                                if ord(char) < 32 and char not in ['\n', '\r', '\t']:
                                    print(f"      ❌ Control char in line {line_num}, pos {char_pos}: {repr(char)}")
                                    print(f"         Line: {repr(line)}")
        
        except json.JSONDecodeError as e:
            print(f"   ❌ Direct parsing: FAILED")
            print(f"      Error: {e}")
            print(f"      Position: {getattr(e, 'pos', 'unknown')}")
            
            # Show the exact character at the error position
            error_pos = getattr(e, 'pos', None)
            if error_pos is not None and error_pos < len(embed_data_raw):
                error_char = embed_data_raw[error_pos]
                print(f"      Character at error position {error_pos}: {repr(error_char)} (ord: {ord(error_char)})")
        
        # Test our CleanControlCharactersFromSourceData equivalent
        print(f"\n🧪 TESTING CONTROL CHARACTER CLEANING:")
        cleaned_embed = clean_control_characters_python(embed_data_raw)
        
        if cleaned_embed != embed_data_raw:
            print(f"   🔧 Control characters found and cleaned")
            print(f"   📏 Original length: {len(embed_data_raw)}")
            print(f"   📏 Cleaned length: {len(cleaned_embed)}")
            
            # Try parsing the cleaned version
            try:
                parsed_cleaned = json.loads(cleaned_embed)
                print(f"   ✅ Cleaned version parses successfully!")
            except json.JSONDecodeError as e:
                print(f"   ❌ Cleaned version still fails: {e}")
        else:
            print(f"   ℹ️ No control characters detected by Python cleaning")
        
    except Exception as e:
        print(f"❌ Error in analysis: {e}")
        import traceback
        traceback.print_exc()

def clean_control_characters_python(text):
    """Python equivalent of CleanControlCharactersFromSourceData"""
    if not text:
        return text
    
    cleaned = []
    for char in text:
        char_code = ord(char)
        if char_code < 32 and char not in ['\n', '\r', '\t']:
            # Replace control characters with safe alternatives or remove them
            if char_code == 0:  # Null
                pass  # Skip null characters
            elif char_code == 7:  # Bell
                cleaned.append("[BELL]")
            elif char_code == 8:  # Backspace
                cleaned.append("[BS]")
            elif char_code == 11:  # Vertical tab
                cleaned.append(" ")
            elif char_code == 12:  # Form feed
                cleaned.append(" ")
            elif char_code == 27:  # Escape
                pass  # Skip escape characters
            else:
                pass  # Skip other control characters
        elif char_code == 127:  # DEL character
            pass  # Skip DEL character
        else:
            cleaned.append(char)
    
    return ''.join(cleaned)

def get_control_char_description(char_code):
    """Get description of control character"""
    descriptions = {
        0: "NULL", 1: "SOH", 2: "STX", 3: "ETX", 4: "EOT", 5: "ENQ", 6: "ACK", 7: "BEL (Bell)",
        8: "BS (Backspace)", 9: "TAB", 10: "LF", 11: "VT", 12: "FF", 13: "CR", 14: "SO", 15: "SI",
        16: "DLE", 17: "DC1", 18: "DC2", 19: "DC3", 20: "DC4", 21: "NAK", 22: "SYN", 23: "ETB",
        24: "CAN", 25: "EM", 26: "SUB", 27: "ESC (Escape)", 28: "FS", 29: "GS", 30: "RS", 31: "US",
        127: "DEL"
    }
    return descriptions.get(char_code, f"Unknown control character")

if __name__ == "__main__":
    extract_and_analyze_embed_data_raw()
