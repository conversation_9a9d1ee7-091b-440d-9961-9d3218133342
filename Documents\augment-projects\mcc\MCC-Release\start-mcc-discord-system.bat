@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM MCC Bot + Discord Bridge System Startup Script
REM Automatically starts both the MCC bot and Discord bridge with monitoring
REM ============================================================================

title MCC Discord System Launcher

REM Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

echo %CYAN%============================================================================%RESET%
echo %WHITE%                    MCC BOT + DISCORD BRIDGE LAUNCHER%RESET%
echo %CYAN%============================================================================%RESET%
echo.

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"
set "MCC_DIR=%SCRIPT_DIR%"

echo %BLUE%[INFO]%RESET% Script directory: %SCRIPT_DIR%
echo %BLUE%[INFO]%RESET% MCC directory: %MCC_DIR%
echo.

REM Change to the MCC-Release directory
cd /d "%MCC_DIR%"
if errorlevel 1 (
    echo %RED%[ERROR]%RESET% Failed to change to MCC directory: %MCC_DIR%
    echo %RED%[ERROR]%RESET% Please ensure this script is in the MCC-Release folder
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%RESET% Working directory set to: %CD%
echo.

REM ============================================================================
REM STEP 1: Check Prerequisites
REM ============================================================================

echo %YELLOW%[STEP 1]%RESET% Checking prerequisites...
echo.

REM Check if MinecraftClient.exe exists
if not exist "MinecraftClient.exe" (
    echo %RED%[ERROR]%RESET% MinecraftClient.exe not found in current directory
    echo %RED%[ERROR]%RESET% Expected location: %CD%\MinecraftClient.exe
    echo %RED%[ERROR]%RESET% Please ensure you're running this script from the MCC-Release folder
    pause
    exit /b 1
)
echo %GREEN%[FOUND]%RESET% MinecraftClient.exe

REM Check if multi-phase-bot.cs exists
if not exist "multi-phase-bot.cs" (
    echo %RED%[ERROR]%RESET% multi-phase-bot.cs not found in current directory
    echo %RED%[ERROR]%RESET% Expected location: %CD%\multi-phase-bot.cs
    pause
    exit /b 1
)
echo %GREEN%[FOUND]%RESET% multi-phase-bot.cs

REM Check for Discord bridge scripts (prefer simple WebSocket approach)
if exist "discord-bridge-simple.py" (
    echo %GREEN%[FOUND]%RESET% discord-bridge-simple.py (recommended - WebSocket-based)
    set "DISCORD_SCRIPT=discord-bridge-simple.py"
    set "DISCORD_TYPE=WebSocket"
) else if exist "discord-bridge-v3.py" (
    echo %GREEN%[FOUND]%RESET% discord-bridge-v3.py (WebSocket-based)
    set "DISCORD_SCRIPT=discord-bridge-v3.py"
    set "DISCORD_TYPE=WebSocket"
) else if exist "discord-bridge-v2.py" (
    echo %YELLOW%[FOUND]%RESET% discord-bridge-v2.py (HTTP-based - more complex)
    set "DISCORD_SCRIPT=discord-bridge-v2.py"
    set "DISCORD_TYPE=HTTP"
) else (
    echo %RED%[ERROR]%RESET% No Discord bridge script found
    echo %RED%[ERROR]%RESET% Expected: discord-bridge-simple.py, discord-bridge-v3.py, or discord-bridge-v2.py
    pause
    exit /b 1
)

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%RESET% Python not found in system PATH
    echo %RED%[ERROR]%RESET% Please install Python or add it to your PATH
    echo %YELLOW%[INFO]%RESET% You can download Python from: https://python.org
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set "PYTHON_VERSION=%%i"
echo %GREEN%[FOUND]%RESET% %PYTHON_VERSION%

echo.
echo %GREEN%[SUCCESS]%RESET% All prerequisites checked successfully!
echo.

REM ============================================================================
REM STEP 2: Check Discord Bridge Configuration
REM ============================================================================

echo %YELLOW%[STEP 2]%RESET% Checking Discord bridge configuration...
echo.

REM Check Discord bridge configuration
if "%DISCORD_TYPE%"=="WebSocket" (
    echo %GREEN%[ADVANTAGE]%RESET% Using WebSocket approach - no external configuration needed!
    echo %GREEN%[ADVANTAGE]%RESET% No HTTP endpoints, public IPs, or Discord app setup required

    REM Check if bot token is configured
    findstr /C:"YOUR_BOT_TOKEN_HERE" %DISCORD_SCRIPT% >nul
    if not errorlevel 1 (
        echo %YELLOW%[WARNING]%RESET% Discord bot token not configured in %DISCORD_SCRIPT%
        echo %YELLOW%[WARNING]%RESET% Button interactions will not work until token is set
        echo %YELLOW%[WARNING]%RESET% Edit %DISCORD_SCRIPT% and replace YOUR_BOT_TOKEN_HERE
        echo.
        echo %CYAN%[QUESTION]%RESET% Continue anyway? (Y/N)
        set /p "CONTINUE=>"
        if /i not "!CONTINUE!"=="Y" (
            echo %BLUE%[INFO]%RESET% Startup cancelled by user
            pause
            exit /b 0
        )
    ) else (
        echo %GREEN%[CONFIGURED]%RESET% Discord bot token appears to be set
    )
) else (
    echo %YELLOW%[COMPLEX]%RESET% Using HTTP approach - requires external configuration
    echo %YELLOW%[COMPLEX]%RESET% You'll need to configure Discord app's Interactions Endpoint URL
    echo %YELLOW%[COMPLEX]%RESET% Consider switching to discord-bridge-simple.py for easier setup
)

echo.

REM ============================================================================
REM STEP 3: Start MCC Bot
REM ============================================================================

echo %YELLOW%[STEP 3]%RESET% Starting MCC Bot...
echo.

echo %BLUE%[INFO]%RESET% Launching MinecraftClient.exe with multi-phase-bot.cs
echo %BLUE%[INFO]%RESET% Command: MinecraftClient.exe multi-phase-bot.cs
echo.

REM Start MCC bot in a new window
start "MCC Bot - Multi-Phase System" /d "%CD%" cmd /k "echo [MCC BOT WINDOW] && echo Starting MCC Bot with multi-phase-bot.cs... && echo. && MinecraftClient.exe multi-phase-bot.cs"

REM Wait a moment to see if the process started
timeout /t 2 /nobreak >nul

REM Check if MCC process is running
tasklist /fi "imagename eq MinecraftClient.exe" 2>nul | find /i "MinecraftClient.exe" >nul
if errorlevel 1 (
    echo %YELLOW%[WARNING]%RESET% MCC Bot process not detected (may still be starting)
    echo %YELLOW%[WARNING]%RESET% Check the MCC Bot window for any error messages
) else (
    echo %GREEN%[SUCCESS]%RESET% MCC Bot process started successfully
)

echo.
echo %BLUE%[INFO]%RESET% Waiting 5 seconds for MCC Bot initialization...
timeout /t 5 /nobreak >nul

REM ============================================================================
REM STEP 4: Start Discord Bridge
REM ============================================================================

echo %YELLOW%[STEP 4]%RESET% Starting Discord Bridge...
echo.

echo %BLUE%[INFO]%RESET% Launching %DISCORD_SCRIPT%
echo %BLUE%[INFO]%RESET% Command: python %DISCORD_SCRIPT%
echo.

REM Start Discord bridge in a new window
start "Discord Bridge - Enhanced Bot" /d "%CD%" cmd /k "echo [DISCORD BRIDGE WINDOW] && echo Starting Discord Bridge (%DISCORD_SCRIPT%)... && echo. && python %DISCORD_SCRIPT%"

REM Wait a moment to see if the process started
timeout /t 2 /nobreak >nul

REM Check if Python process is running the Discord bridge
tasklist /fi "imagename eq python.exe" 2>nul | find /i "python.exe" >nul
if errorlevel 1 (
    echo %YELLOW%[WARNING]%RESET% Discord Bridge process not detected (may still be starting)
    echo %YELLOW%[WARNING]%RESET% Check the Discord Bridge window for any error messages
) else (
    echo %GREEN%[SUCCESS]%RESET% Discord Bridge process started successfully
)

echo.

REM ============================================================================
REM STEP 5: System Status Summary
REM ============================================================================

echo %CYAN%============================================================================%RESET%
echo %WHITE%                           SYSTEM STATUS SUMMARY%RESET%
echo %CYAN%============================================================================%RESET%
echo.

echo %GREEN%[LAUNCHED]%RESET% MCC Bot (MinecraftClient.exe + multi-phase-bot.cs)
echo %GREEN%[LAUNCHED]%RESET% Discord Bridge (%DISCORD_SCRIPT%)
echo.

echo %BLUE%[MONITORING]%RESET% Both processes are running in separate windows
echo %BLUE%[MONITORING]%RESET% You can monitor logs in each respective window
echo.

echo %YELLOW%[FEATURES ACTIVE]%RESET%
echo   • Island data tracking with growth calculations
echo   • Player leaderboard monitoring
echo   • Discord message formatting with emojis
echo   • Enhanced control character protection
echo   • Historical data persistence
if "%DISCORD_TYPE%"=="WebSocket" (
    echo   • Button interactions with ephemeral responses
    echo   • Persistent Discord bot connection (WebSocket)
    echo   • No external configuration required
) else (
    echo   • HTTP interaction server (requires Discord app config)
    echo   • External endpoint configuration needed
)
echo.

echo %CYAN%[NEXT STEPS]%RESET%
echo   1. Check both terminal windows for startup messages
echo   2. Verify MCC bot connects to Minecraft server
echo   3. Verify Discord bridge connects to Discord API
echo   4. Test message formatting in Discord channels
if "%DISCORD_SCRIPT%"=="discord-bridge-v3.py" (
    echo   5. Test button interactions in Discord
)
echo.

echo %WHITE%[TROUBLESHOOTING]%RESET%
echo   • If MCC bot fails: Check credentials and server status
echo   • If Discord bridge fails: Check bot token and permissions
echo   • If formatting issues: Check recent messages in Discord
echo   • If buttons don't work: Verify Discord bot token is set
echo.

echo %GREEN%[SUCCESS]%RESET% MCC Discord System startup completed!
echo %BLUE%[INFO]%RESET% Both processes are now running independently
echo %BLUE%[INFO]%RESET% Close this window when you're done monitoring
echo.

echo %CYAN%============================================================================%RESET%
echo %WHITE%Press any key to close this launcher window...%RESET%
echo %CYAN%============================================================================%RESET%

pause >nul

REM ============================================================================
REM Cleanup and Exit
REM ============================================================================

echo.
echo %BLUE%[INFO]%RESET% Launcher window closing...
echo %BLUE%[INFO]%RESET% MCC Bot and Discord Bridge will continue running
echo %YELLOW%[NOTE]%RESET% To stop the system, close both terminal windows manually
echo.

exit /b 0
