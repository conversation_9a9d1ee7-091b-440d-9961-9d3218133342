#!/usr/bin/env python3
"""
Reproduce the specific control character error at position 91-92
"""

import json
import uuid
from datetime import datetime

def create_message_with_control_char_at_position_91():
    """Create a message that has a control character around position 91-92"""
    
    # The error occurs at character 91-92, so let's craft a message where
    # a control character appears exactly at that position
    
    # Start with a basic message structure
    base_message = {
        "id": "886ce2d9-control-test",
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB"
    }
    
    # Create embed data that will put us near position 91
    # Let's calculate: {"id":"886ce2d9-control-test","timestamp":"2025-08-03T23:39:00.000000Z","cycle_number":1,"channel_id":"1401451313216094383","channel_name":"LB","embed_data_raw":"
    # That's about 150+ characters already, so the embed_data_raw content starts around position 150+
    
    # Let's create a shorter message to get the control char at position 91
    short_message = {
        "id": "886ce2d9",
        "channel_id": "1401451313216094383",
        "embed_data_raw": ""  # We'll craft this to put control char at position 91
    }
    
    # Calculate current length
    temp_json = json.dumps(short_message, separators=(',', ':'))
    current_length = len(temp_json) - 2  # -2 for the empty embed_data_raw quotes
    
    print(f"Base message length (without embed_data_raw content): {current_length}")
    print(f"Need to add {91 - current_length} characters to reach position 91")
    
    # Create embed content that will place a control character at position 91
    target_position = 91 - current_length
    if target_position > 0:
        # Create content that puts a control character at the target position
        safe_content = "x" * (target_position - 1)  # Fill with safe characters
        control_char_content = safe_content + "\x08"  # Add backspace control character
        
        embed_data = {"test": control_char_content}
        embed_json = json.dumps(embed_data, separators=(',', ':'))
        
        short_message["embed_data_raw"] = embed_json
    else:
        # If we're already past position 91, create a simple test
        embed_data = {"test": "control\x08char"}
        short_message["embed_data_raw"] = json.dumps(embed_data, separators=(',', ':'))
    
    return short_message

def create_realistic_problematic_message():
    """Create a realistic message that might contain the problematic control character"""
    
    # Based on the console logs, the issue might be with Minecraft item names or player names
    # that contain special characters or formatting codes
    
    # Simulate a leaderboard message with problematic player/item names
    embed_data = {
        "embeds": [{
            "title": "🏆 Top 10 Players",
            "description": "Player rankings extracted at 23:34:10\n*Click buttons below to view different categories*",
            "color": 16766720,
            "timestamp": datetime.now().isoformat() + "Z",
            "fields": [{
                "name": "🏆 Top 12 Players:",
                "value": "#1: Nincompoopz 66 points - 50 GC\n#2: Player\x08Name 61 points - 40 GC\n#3: Test\x0BUser 60 points - 35 GC",
                "inline": False
            }],
            "footer": {
                "text": "Phase 3 • 23 categories available"
            }
        }],
        "components": [{
            "type": 1,
            "components": [
                {"type": 2, "style": 2, "label": "Top 12 Players:", "custom_id": "leaderboard_slot_4"}
            ]
        }]
    }
    
    # Convert to JSON string
    embed_json_str = json.dumps(embed_data, separators=(',', ':'))
    
    # Create the message
    message_data = {
        "id": "886ce2d9-realistic-test",
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": embed_json_str,
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create"
    }
    
    return message_data

def test_control_character_scenarios():
    """Test various scenarios that might cause the control character error"""
    
    print("🔧 Testing Control Character Error Scenarios")
    print("=" * 60)
    
    # Test 1: Targeted position test
    print("\n📍 Test 1: Control Character at Position 91")
    print("-" * 50)
    
    try:
        message1 = create_message_with_control_char_at_position_91()
        json1 = json.dumps(message1, separators=(',', ':'))
        
        print(f"Message length: {len(json1)}")
        print(f"Character at position 91: {repr(json1[90]) if len(json1) > 90 else 'N/A'}")
        print(f"Character at position 92: {repr(json1[91]) if len(json1) > 91 else 'N/A'}")
        
        # Try to parse it
        parsed1 = json.loads(json1)
        
        # Try to parse the embed_data_raw
        if 'embed_data_raw' in parsed1:
            embed_data = json.loads(parsed1['embed_data_raw'])
            print("✅ Test 1: All parsing successful")
        else:
            print("⚠️ Test 1: No embed_data_raw field")
            
    except json.JSONDecodeError as e:
        print(f"❌ Test 1: JSON error at position {e.pos}")
        print(f"Error message: {e}")
        if hasattr(e, 'pos') and e.pos < len(json1):
            error_char = json1[e.pos]
            print(f"Error character: {repr(error_char)} (ord: {ord(error_char)})")
    except Exception as e:
        print(f"❌ Test 1: Unexpected error: {e}")
    
    # Test 2: Realistic scenario
    print("\n🎮 Test 2: Realistic Minecraft Scenario")
    print("-" * 50)
    
    try:
        message2 = create_realistic_problematic_message()
        json2 = json.dumps(message2, separators=(',', ':'))
        
        print(f"Message length: {len(json2)}")
        
        # Check for control characters
        control_positions = []
        for i, char in enumerate(json2):
            if ord(char) < 32 and char not in ['\n', '\r', '\t']:
                control_positions.append((i, char, ord(char)))
        
        if control_positions:
            print(f"Found {len(control_positions)} control characters:")
            for pos, char, ord_val in control_positions[:5]:
                print(f"  Position {pos}: {repr(char)} (ord: {ord_val})")
        else:
            print("No control characters found")
        
        # Try to parse
        parsed2 = json.loads(json2)
        embed_data = json.loads(parsed2['embed_data_raw'])
        print("✅ Test 2: All parsing successful")
        
    except json.JSONDecodeError as e:
        print(f"❌ Test 2: JSON error at position {e.pos}")
        print(f"Error message: {e}")
        if hasattr(e, 'pos') and e.pos < len(json2):
            error_char = json2[e.pos]
            print(f"Error character: {repr(error_char)} (ord: {ord(error_char)})")
            
            # Show context
            start = max(0, e.pos - 10)
            end = min(len(json2), e.pos + 10)
            context = json2[start:end]
            print(f"Context: {repr(context)}")
    except Exception as e:
        print(f"❌ Test 2: Unexpected error: {e}")

def write_test_message_to_queue():
    """Write a test message to the queue to see if Discord bridge encounters the error"""
    
    print("\n📝 Writing Test Message to Queue")
    print("-" * 40)
    
    # Create a message that might trigger the error
    message = create_realistic_problematic_message()
    
    try:
        json_str = json.dumps(message, separators=(',', ':'))
        
        # Write to queue file
        with open("discord_queue.json", "a", encoding='utf-8') as f:
            f.write(json_str + "\n")
        
        print(f"✅ Test message written to queue")
        print(f"Message ID: {message['id']}")
        print(f"JSON length: {len(json_str)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to write test message: {e}")
        return False

if __name__ == "__main__":
    print("🔍 REPRODUCING CONTROL CHARACTER ERROR")
    print("=" * 70)
    
    test_control_character_scenarios()
    
    # Write test message to queue for Discord bridge to process
    success = write_test_message_to_queue()
    
    print("\n" + "=" * 70)
    print("📋 SUMMARY:")
    
    if success:
        print("✅ Test message created and written to queue")
        print("🔍 Monitor Discord bridge for control character errors")
        print("📍 Look for errors at position 91-92 in the logs")
    else:
        print("❌ Failed to create test message")
    
    print("\n💡 If Discord bridge shows control character error:")
    print("1. Note the exact position and character")
    print("2. Update SimpleJsonSerializer to filter that character")
    print("3. Add Minecraft text sanitization")
    print("4. Test with real MCC bot output")
