#!/usr/bin/env python3
"""
Test script to verify the island and leaderboard embed fixes
"""

import json
import uuid
from datetime import datetime

def create_test_island_message():
    """Create a test island message using the old embed_data format"""
    
    # Simulate the island embed JSON that CreateIslandDataEmbed() generates
    island_embed = {
        "embeds": [{
            "title": "🏝️ Top Islands Data",
            "description": "Island rankings extracted at 15:30:45",
            "color": 39423,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏝️ #1: Revenants (10 points)",
                    "value": "**All Time:** $1.04B\n**Weekly:** $242.3M\n**Growth:** Insufficient historical data",
                    "inline": False
                },
                {
                    "name": "🏝️ #2: NIP (9 points)",
                    "value": "**All Time:** $670.37M\n**Weekly:** $143.43M\n**Growth:** Insufficient historical data",
                    "inline": False
                }
            ],
            "footer": {
                "text": "Phase 2 • 2 islands tracked • Updated every 10 minutes",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
            }
        }]
    }
    
    # Create the message data structure (old format with embed_data)
    message_data = {
        "id": str(uuid.uuid4()),
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data": island_embed,  # Regular embed_data field
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    return message_data

def create_test_leaderboard_message():
    """Create a test leaderboard message using the new embed_data_raw format"""
    
    # Simulate the leaderboard embed JSON that CreateLeaderboardEmbed() generates
    leaderboard_embed = {
        "embeds": [{
            "title": "🏆 Top 10 Players",
            "description": "Player rankings extracted at 15:30:50\n*Click buttons below to view different categories*",
            "color": 16766720,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [{
                "name": "🏆 Top 12 Players:",
                "value": "#1: Nincompoopz 66 points - 50 GC\n#2: MostlyMissing 61 points - 40 GC\n#3: Jaeger1000 60 points - 35 GC",
                "inline": False
            }],
            "footer": {
                "text": "Phase 3 • 23 categories available",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
            }
        }],
        "components": [{
            "type": 1,
            "components": [
                {"type": 2, "style": 2, "label": "Top 12 Players:", "custom_id": "leaderboard_slot_4"},
                {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_slot_9"},
                {"type": 2, "style": 2, "label": "Scrolls", "custom_id": "leaderboard_slot_11"}
            ]
        }]
    }
    
    # Convert to JSON string (this is what the MCC bot does)
    embed_json_str = json.dumps(leaderboard_embed, separators=(',', ':'))
    
    # Create the message data structure (new format with embed_data_raw)
    message_data = {
        "id": str(uuid.uuid4()),
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": embed_json_str,  # Raw JSON string field
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create",
        "update_message_id": None,
        "supports_interactions": True,
        "interaction_handler": "HandleLeaderboardButtonInteraction"
    }
    
    return message_data

def test_message_formats():
    """Test both message formats"""
    
    print("🔧 Testing Island and Leaderboard Message Formats")
    print("=" * 60)
    
    # Test 1: Island message (old format)
    print("\n📊 Test 1: Island Message (embed_data format)")
    print("-" * 40)
    
    island_message = create_test_island_message()
    
    try:
        # Serialize the island message
        island_json = json.dumps(island_message, separators=(',', ':'))
        print("✅ Island message serialization successful")
        
        # Parse it back (simulate Discord bridge)
        parsed_island = json.loads(island_json)
        print("✅ Island message parsing successful")
        
        # Check for embed_data field
        if 'embed_data' in parsed_island:
            embed_data = parsed_island['embed_data']
            print("✅ embed_data field found")
            
            # Check for proper newlines in field values
            field_value = embed_data['embeds'][0]['fields'][0]['value']
            if '\\n' in field_value:
                print("⚠️ Found escaped newlines (\\n) - may display as literal text")
            elif '\n' in field_value:
                print("✅ Found actual newlines - will display properly")
            
        else:
            print("❌ embed_data field missing")
            
    except Exception as e:
        print(f"❌ Island message test failed: {e}")
        return False
    
    # Test 2: Leaderboard message (new format)
    print("\n🏆 Test 2: Leaderboard Message (embed_data_raw format)")
    print("-" * 40)
    
    leaderboard_message = create_test_leaderboard_message()
    
    try:
        # Serialize the leaderboard message
        leaderboard_json = json.dumps(leaderboard_message, separators=(',', ':'))
        print("✅ Leaderboard message serialization successful")
        
        # Parse it back (simulate Discord bridge)
        parsed_leaderboard = json.loads(leaderboard_json)
        print("✅ Leaderboard message parsing successful")
        
        # Check for embed_data_raw field
        if 'embed_data_raw' in parsed_leaderboard:
            embed_data_raw = parsed_leaderboard['embed_data_raw']
            print("✅ embed_data_raw field found")
            
            # Parse the raw JSON (simulate Discord bridge fix)
            embed_data = json.loads(embed_data_raw)
            print("✅ embed_data_raw parsing successful")
            
            # Check for interactive components
            if 'components' in embed_data:
                print(f"✅ Interactive components found ({len(embed_data['components'])} rows)")
            
        else:
            print("❌ embed_data_raw field missing")
            
    except Exception as e:
        print(f"❌ Leaderboard message test failed: {e}")
        return False
    
    # Test 3: Write to queue file and verify Discord bridge can process
    print("\n📝 Test 3: Writing to Queue File")
    print("-" * 40)
    
    try:
        with open("discord_queue.json", "w", encoding='utf-8') as f:
            f.write(island_json + "\n")
            f.write(leaderboard_json + "\n")
        
        print("✅ Messages written to discord_queue.json")
        
        # Verify file can be read back
        with open("discord_queue.json", "r", encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"✅ Queue file contains {len(lines)} messages")
        
        # Test parsing each line
        for i, line in enumerate(lines, 1):
            try:
                data = json.loads(line.strip())
                if 'embed_data' in data:
                    print(f"✅ Line {i}: Island message format valid")
                elif 'embed_data_raw' in data:
                    print(f"✅ Line {i}: Leaderboard message format valid")
                    # Test parsing the raw data
                    json.loads(data['embed_data_raw'])
                    print(f"✅ Line {i}: embed_data_raw content valid")
                else:
                    print(f"⚠️ Line {i}: Unknown message format")
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing failed - {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Queue file test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_message_formats()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS:")
    
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Island messages use embed_data format correctly")
        print("✅ Leaderboard messages use embed_data_raw format correctly") 
        print("✅ Discord bridge should be able to process both formats")
        print("✅ Newline handling should work properly")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Message formats need additional fixes")
