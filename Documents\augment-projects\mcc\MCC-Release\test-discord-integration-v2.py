#!/usr/bin/env python3
"""
Test Discord Integration v2.0
Generates test messages to verify the Discord bridge bot is working
"""

import json
import time
from datetime import datetime
from pathlib import Path
import uuid

# Configuration
DISCORD_QUEUE_FILE = Path("discord_queue.json")
ISTOP_CHANNEL_ID = "1401451296711507999"
LB_CHANNEL_ID = "1401451313216094383"

def create_test_island_embed():
    """Create test island data embed"""
    return {
        "embeds": [{
            "title": "🏝️ Top Islands Data - Test Update",
            "description": f"Test island rankings generated at {datetime.now().strftime('%H:%M:%S')}",
            "color": 39423,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏝️ #1: TestIsland1",
                    "value": f"**All Time:** $2.{97 + (datetime.now().minute % 10)}B\n**Weekly:** $1.2B\n**Today:** +${31 + (datetime.now().minute % 20)}.34M (+1.5%)",
                    "inline": True
                },
                {
                    "name": "🏝️ #2: TestIsland2",
                    "value": f"**All Time:** $1.{40 + (datetime.now().minute % 5)}B\n**Weekly:** $800M\n**Today:** +${15 + (datetime.now().minute % 10)}.2M (+1.1%)",
                    "inline": True
                },
                {
                    "name": "🏝️ #3: TestIsland3",
                    "value": f"**All Time:** ${599 + (datetime.now().minute % 50)}.93M\n**Weekly:** $400M\n**Today:** +${8 + (datetime.now().minute % 15)}.15M (+2.7%)",
                    "inline": True
                }
            ],
            "footer": {
                "text": "Phase 2 • 3 test islands • Generated by test script",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
            }
        }]
    }

def create_test_leaderboard_embed():
    """Create test leaderboard data embed"""
    return {
        "embeds": [{
            "title": "🏆 Leaderboard Rankings - Test Update",
            "description": f"Test player rankings generated at {datetime.now().strftime('%H:%M:%S')}",
            "color": 16766720,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏆 Mobs Killed",
                    "value": f"#1: TestPlayer1 ({1777730 + (datetime.now().minute * 100):,} mobs)\n#2: TestPlayer2 ({141602 + (datetime.now().minute * 50):,} mobs)\n#3: TestPlayer3 ({130897 + (datetime.now().minute * 25):,} mobs)",
                    "inline": True
                },
                {
                    "name": "🏆 Scrolls Completed",
                    "value": f"#1: TestPlayer4 ({34 + (datetime.now().minute % 5)} scrolls)\n#2: TestPlayer5 ({31 + (datetime.now().minute % 3)} scrolls)\n#3: TestPlayer6 ({25 + (datetime.now().minute % 2)} scrolls)",
                    "inline": True
                },
                {
                    "name": "🏆 Gold Shards",
                    "value": f"#1: TestPlayer7 ({13210 + (datetime.now().minute * 10):,} shards)\n#2: TestPlayer8 ({8945 + (datetime.now().minute * 5):,} shards)\n#3: TestPlayer9 ({7234 + (datetime.now().minute * 3):,} shards)",
                    "inline": True
                }
            ],
            "footer": {
                "text": "Phase 3 • 3 test categories • Generated by test script",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
            }
        }]
    }

def create_test_message(channel_id, channel_name, embed_data, cycle_number=999):
    """Create a test message in the correct format"""
    return {
        "id": str(uuid.uuid4()),
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "cycle_number": cycle_number,
        "channel_id": channel_id,
        "channel_name": channel_name,
        "embed_data": embed_data,
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }

def append_test_message(message):
    """Append test message to queue file"""
    message_json = json.dumps(message, separators=(',', ':'))
    with open(DISCORD_QUEUE_FILE, 'a', encoding='utf-8') as f:
        f.write(message_json + "\n")

def main():
    """Generate test messages"""
    print("🧪 Discord Integration v2.0 Test")
    print("=" * 50)
    print()
    
    # Check if bridge bot is running
    if not DISCORD_QUEUE_FILE.exists():
        print("📝 Creating discord_queue.json file...")
        DISCORD_QUEUE_FILE.touch()
    
    print(f"📁 Writing test messages to: {DISCORD_QUEUE_FILE}")
    print(f"🎯 Target channels:")
    print(f"   - ISTOP: {ISTOP_CHANNEL_ID}")
    print(f"   - LB: {LB_CHANNEL_ID}")
    print()
    
    # Generate test messages
    cycle_num = 999  # Use 999 to distinguish from real cycles
    
    print("📤 Generating test messages...")
    
    # 1. Test ISTOP channel - cycle start
    istop_start_embed = {
        "embeds": [{
            "title": f"🧪 Test Cycle #{cycle_num} - Start",
            "description": f"Test cycle started at {datetime.now().strftime('%H:%M:%S')}",
            "color": 3447003,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🧪 Test Status",
                    "value": "✅ Testing Discord integration v2.0\n✅ Verifying bridge bot functionality\n✅ Confirming message delivery",
                    "inline": False
                }
            ],
            "footer": {
                "text": "Test Script • Discord Integration v2.0",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/test-icon.png"
            }
        }]
    }
    
    istop_message = create_test_message(ISTOP_CHANNEL_ID, "ISTOP", istop_start_embed, cycle_num)
    append_test_message(istop_message)
    print(f"✅ Test message 1: ISTOP channel (cycle start)")
    
    time.sleep(1)
    
    # 2. Test LB channel - island data
    island_embed = create_test_island_embed()
    island_message = create_test_message(LB_CHANNEL_ID, "LB", island_embed, cycle_num)
    append_test_message(island_message)
    print(f"✅ Test message 2: LB channel (island data)")
    
    time.sleep(1)
    
    # 3. Test LB channel - leaderboard data
    leaderboard_embed = create_test_leaderboard_embed()
    leaderboard_message = create_test_message(LB_CHANNEL_ID, "LB", leaderboard_embed, cycle_num)
    append_test_message(leaderboard_message)
    print(f"✅ Test message 3: LB channel (leaderboard data)")
    
    time.sleep(1)
    
    # 4. Test ISTOP channel - cycle complete
    istop_complete_embed = {
        "embeds": [{
            "title": f"🧪 Test Cycle #{cycle_num} - Complete",
            "description": "Test cycle completed successfully",
            "color": 65280,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🧪 Test Results",
                    "value": "✅ ISTOP channel: Cycle notifications\n✅ LB channel: Island data embed\n✅ LB channel: Leaderboard data embed\n✅ All test messages generated",
                    "inline": False
                },
                {
                    "name": "📊 Test Summary",
                    "value": f"Messages: 4\nChannels: 2\nTest ID: {cycle_num}",
                    "inline": True
                }
            ],
            "footer": {
                "text": "Test Script • Discord Integration v2.0",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/test-icon.png"
            }
        }]
    }
    
    istop_complete_message = create_test_message(ISTOP_CHANNEL_ID, "ISTOP", istop_complete_embed, cycle_num)
    append_test_message(istop_complete_message)
    print(f"✅ Test message 4: ISTOP channel (cycle complete)")
    
    print()
    print("🎯 Test Summary:")
    print(f"✅ Generated 4 test messages")
    print(f"✅ Written to {DISCORD_QUEUE_FILE}")
    print(f"✅ Bridge bot should process these within 2 seconds")
    print()
    print("🔍 Check Discord channels for test messages:")
    print(f"   - ISTOP channel: 2 messages (start + complete)")
    print(f"   - LB channel: 2 messages (islands + leaderboards)")
    print()
    print("📊 If messages appear in Discord, the integration is working!")
    print("🔧 If no messages appear, check the bridge bot console for errors.")

if __name__ == "__main__":
    main()
