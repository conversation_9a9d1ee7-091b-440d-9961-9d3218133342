#!/usr/bin/env python3
"""
Test Real Data Extraction from embed_data_raw
Creates a test message with real player data in embed_data_raw to test the fallback extraction
"""

import json
import uuid
from datetime import datetime

def create_real_data_test_message():
    """Create a test message with real player data in embed_data_raw (like current MCC bot)"""
    
    message_id = f"real-data-test-{str(uuid.uuid4())[:8]}"
    
    # Create embed_data_raw with the REAL player data from your console logs
    embed_data_raw = {
        "embeds": [{
            "title": "🏆 Top 10 Players",
            "description": "Real data extraction test\\n*Click buttons below to view different categories*",
            "color": 16766720,
            "timestamp": datetime.now().isoformat() + "Z",
            "fields": [{
                "name": "🏆 Top 12 Players:",
                "value": "#1: Nincompoopz 71 points - 50 GC\\n#2: Roleeb 65 points - 40 GC\\n#3: <PERSON><PERSON><PERSON> 59 points - 35 GC\\n#4: YY<PERSON>Y<PERSON> 55 points - 30 GC\\n#5: Jaxair 50 points - 25 GC\\n#6: WektorTR 45 points - 20 GC\\n#7: mattep83 40 points - 15 GC\\n#8: agrajagagrajag 35 points - 10 GC\\n#9: Farrnado 30 points - 10 GC\\n#10: Briz_ 25 points - 5 GC",
                "inline": False
            }],
            "footer": {
                "text": "Real Data Extraction Test • From embed_data_raw • No leaderboard_categories",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
            }
        }],
        "components": [{
            "type": 1,
            "components": [
                {"type": 2, "style": 2, "label": "Scrolls", "custom_id": "leaderboard_slot_11"},
                {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_slot_9"},
                {"type": 2, "style": 2, "label": "XP", "custom_id": "leaderboard_slot_31"},
                {"type": 2, "style": 2, "label": "PvP", "custom_id": "leaderboard_slot_35"},
                {"type": 2, "style": 2, "label": "Runes", "custom_id": "leaderboard_slot_13"}
            ]
        }]
    }
    
    # Create the test message (like current MCC bot - NO leaderboard_categories field)
    test_message = {
        "id": message_id,
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 999,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": json.dumps(embed_data_raw, separators=(',', ':')),
        # NOTE: NO leaderboard_categories field - this simulates current MCC bot
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create",
        "supports_interactions": True,
        "interaction_handler": "HandleLeaderboardButtonInteraction"
    }
    
    return message_id, test_message

def test_embed_data_parsing():
    """Test parsing of embed_data_raw to extract real player data"""
    
    print("🧪 TESTING EMBED_DATA_RAW PARSING")
    print("=" * 60)
    
    # Create test embed data
    embed_data_raw = json.dumps({
        "embeds": [{
            "fields": [{
                "name": "🏆 Top 12 Players:",
                "value": "#1: Nincompoopz 71 points - 50 GC\\n#2: Roleeb 65 points - 40 GC\\n#3: Andtarski 59 points - 35 GC\\n#4: YYtheYY 55 points - 30 GC\\n#5: Jaxair 50 points - 25 GC\\n#6: WektorTR 45 points - 20 GC\\n#7: mattep83 40 points - 15 GC\\n#8: agrajagagrajag 35 points - 10 GC\\n#9: Farrnado 30 points - 10 GC\\n#10: Briz_ 25 points - 5 GC"
            }]
        }]
    })
    
    print("📝 Raw embed data:")
    print(f"Field value: {json.loads(embed_data_raw)['embeds'][0]['fields'][0]['value']}")
    
    # Parse the data
    embed_data = json.loads(embed_data_raw)
    fields = embed_data['embeds'][0]['fields']
    
    for field in fields:
        field_name = field.get('name', '')
        field_value = field.get('value', '')
        
        if 'Top' in field_name and 'Players' in field_name and '#1:' in field_value:
            print(f"\n✅ Found player data field: {field_name}")
            
            # Parse the player data
            lines = field_value.replace('\\n', '\n').split('\n')
            players = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('#') and ':' in line:
                    try:
                        parts = line.split(':', 1)
                        if len(parts) == 2:
                            rank_str = parts[0].replace('#', '').strip()
                            player_info = parts[1].strip()
                            
                            rank = int(rank_str)
                            
                            # Extract player name (everything before the points)
                            words = player_info.split()
                            if len(words) >= 3:
                                # Find where the points start
                                points_index = -1
                                for i, word in enumerate(words):
                                    if word.isdigit() and i + 1 < len(words) and words[i + 1] == 'points':
                                        points_index = i
                                        break
                                
                                if points_index > 0:
                                    player_name = ' '.join(words[:points_index])
                                    points_value = ' '.join(words[points_index:])
                                else:
                                    player_name = words[0]
                                    points_value = ' '.join(words[1:])
                            else:
                                player_name = player_info
                                points_value = "unknown points"
                            
                            players.append({
                                "rank": rank,
                                "name": player_name,
                                "value": points_value
                            })
                            
                            print(f"   #{rank}: {player_name} - {points_value}")
                            
                    except Exception as e:
                        print(f"⚠️ Error parsing line '{line}': {e}")
            
            print(f"\n📊 Successfully parsed {len(players)} players")
            return players
    
    return []

def main():
    """Run real data extraction test"""
    
    print("🚀 REAL DATA EXTRACTION TEST")
    print("=" * 70)
    
    # Test 1: Parse embed data
    parsed_players = test_embed_data_parsing()
    
    # Test 2: Create test message
    print(f"\n📝 CREATING REAL DATA TEST MESSAGE")
    print("=" * 60)
    
    message_id, test_message = create_real_data_test_message()
    
    # Write to queue file
    with open("discord_queue.json", "a", encoding='utf-8') as f:
        f.write(json.dumps(test_message, separators=(',', ':')) + "\n")
    
    print(f"✅ Real data test message created: {message_id}")
    print(f"📤 Message added to Discord queue for processing")
    
    # Summary
    print(f"\n" + "=" * 70)
    print("📊 REAL DATA EXTRACTION TEST SUMMARY:")
    print(f"✅ Embed data parsing: {'PASSED' if parsed_players else 'FAILED'}")
    print(f"✅ Players parsed: {len(parsed_players)}")
    print(f"✅ Test message created: CREATED")
    
    print(f"\n🎯 EXPECTED BEHAVIOR:")
    print(f"When you click the 'Scrolls' button, you should see:")
    print(f"• 🥇 #1: Nincompoopz - 71 points - 50 GC")
    print(f"• 🥈 #2: Roleeb - 65 points - 40 GC")
    print(f"• 🥉 #3: Andtarski - 59 points - 35 GC")
    print(f"• 🏅 #4: YYtheYY - 55 points - 30 GC")
    print(f"• 🏅 #5: Jaxair - 50 points - 25 GC")
    print(f"• ... and 5 more REAL players (not fake ones)")
    
    print(f"\n📱 TESTING INSTRUCTIONS:")
    print(f"1. The Discord bot is running and will process this message")
    print(f"2. Go to Discord and find the new leaderboard message")
    print(f"3. Click the 'Scrolls' button")
    print(f"4. Verify it shows ALL 10 real players:")
    print(f"   • Nincompoopz, Roleeb, Andtarski, YYtheYY, Jaxair")
    print(f"   • WektorTR, mattep83, agrajagagrajag, Farrnado, Briz_")
    print(f"5. NO fake names like 'ScrollMaster', 'ScrollSeeker', etc.")
    
    print(f"\n🎉 REAL DATA EXTRACTION TEST COMPLETE!")
    print(f"This tests the fallback extraction from embed_data_raw!")

if __name__ == "__main__":
    main()
