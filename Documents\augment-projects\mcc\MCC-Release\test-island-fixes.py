#!/usr/bin/env python3
"""
Test Island Data Fixes - Verify the fixes are working correctly
"""

import json
import os

def test_current_queue():
    """Test the current discord_queue.json file for island data"""
    print("🧪 Testing Island Data Fixes")
    print("=" * 50)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} lines in queue file")
        
        island_messages = 0
        leaderboard_messages = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # Test JSON parsing
                message_data = json.loads(line)
                
                channel_name = message_data.get('channel_name', 'Unknown')
                
                if channel_name == 'LB':
                    # Check if it's island data or leaderboard data
                    embed_data = message_data.get('embed_data', {})
                    embeds = embed_data.get('embeds', [])
                    
                    if embeds:
                        title = embeds[0].get('title', '')
                        
                        if 'Islands' in title:
                            island_messages += 1
                            print(f"✅ Line {i}: Island Data Message")
                            
                            # Check for issues
                            fields = embeds[0].get('fields', [])
                            for field in fields:
                                name = field.get('name', '')
                                value = field.get('value', '')
                                inline = field.get('inline', True)
                                
                                # Check for corrupted characters
                                if '������' in name or '������' in value:
                                    print(f"   ⚠️ Corrupted characters found in: {name}")
                                
                                # Check for missing data
                                if 'Not available' in value or 'Extracting' in value:
                                    print(f"   ⚠️ Missing data in: {name}")
                                
                                # Check inline setting
                                if inline:
                                    print(f"   ⚠️ Still using inline=true for: {name}")
                                else:
                                    print(f"   ✅ Using inline=false for: {name}")
                        
                        elif 'Leaderboard' in title:
                            leaderboard_messages += 1
                            print(f"✅ Line {i}: Leaderboard Data Message")
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
        
        print()
        print("📊 Summary:")
        print(f"   🏝️ Island messages: {island_messages}")
        print(f"   🏆 Leaderboard messages: {leaderboard_messages}")
        
        if island_messages > 0:
            print("✅ Island data messages found - fixes should be visible")
        else:
            print("⚠️ No island data messages found - may need to wait for next cycle")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    print("🔧 Island Data Fixes Verification")
    print("=" * 60)
    print()
    
    test_current_queue()
    
    print()
    print("Expected fixes:")
    print("1. ✅ Corrupted Unicode characters (������) should be removed")
    print("2. ✅ Discord embeds should use inline=false (full width)")
    print("3. ✅ Weekly data should be extracted (or show 'Extracting...')")
    print("4. ✅ Debug logging should help identify lore parsing issues")
    print("5. ✅ Historical data tracking should start working after multiple cycles")

if __name__ == "__main__":
    main()
