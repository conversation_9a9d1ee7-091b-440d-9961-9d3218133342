# MCC Multi-Phase Bot - Compilation Fixes

## Issue Description

The original `multi-phase-bot.cs` and `test-multi-phase.cs` scripts had C# compilation errors due to incorrect variable declaration syntax in the `GetText()` method.

## Problem

The error was in this line:
```csharp
if (!IsChatMessage(cleanText, ref string message, ref string username) && 
    !IsPrivateMessage(cleanText, ref message, ref username))
```

**Issues:**
1. Variables `message` and `username` were being declared inline within the method call parameters
2. The `string` type was included in the `ref` parameter, which is invalid syntax
3. The second method call tried to use `ref message` without the type, creating inconsistency

## Solution

**Before (Incorrect):**
```csharp
public override void GetText(string text)
{
    try
    {
        string cleanText = GetVerbatim(text);
        
        // WRONG: Inline variable declaration in ref parameters
        if (!IsChatMessage(cleanText, ref string message, ref string username) && 
            !IsPrivateMessage(cleanText, ref message, ref username))
        {
            HandleServerResponse(cleanText);
        }
    }
    catch (Exception ex)
    {
        LogToConsole($"[Phase 1] Error processing text: {ex.Message}");
    }
}
```

**After (Correct):**
```csharp
public override void GetText(string text)
{
    try
    {
        string cleanText = GetVerbatim(text);
        string message = "";      // Declare variables first
        string username = "";     // Declare variables first
        
        // CORRECT: Use pre-declared variables in ref parameters
        if (!IsChatMessage(cleanText, ref message, ref username) && 
            !IsPrivateMessage(cleanText, ref message, ref username))
        {
            HandleServerResponse(cleanText);
        }
    }
    catch (Exception ex)
    {
        LogToConsole($"[Phase 1] Error processing text: {ex.Message}");
    }
}
```

## Key Changes Made

### 1. Variable Declaration
- **Added:** `string message = "";` and `string username = "";` at the beginning of the method
- **Removed:** Type declarations from the `ref` parameters

### 2. Proper Ref Parameter Usage
- **Before:** `ref string message` (invalid)
- **After:** `ref message` (correct)

### 3. Variable Scope
- Variables are now properly scoped within the method
- Both `IsChatMessage()` and `IsPrivateMessage()` can access the same variables
- Variables can be used throughout the entire method if needed

## Files Fixed

1. **`multi-phase-bot.cs`** - Main production bot
2. **`test-multi-phase.cs`** - Test version with shorter intervals
3. **`compile-test.cs`** - New test script to verify compilation

## Verification

### Compilation Test Script
Created `compile-test.cs` to verify the fixes work correctly:

```csharp
//MCCScript 1.0

MCC.LoadBot(new CompileTestBot());

//MCCScript Extensions

public class CompileTestBot : ChatBot
{
    private int tickCount = 0;
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";    // Correct declaration
            string username = "";   // Correct declaration
            
            // Test the corrected syntax
            if (IsChatMessage(cleanText, ref message, ref username))
            {
                LogDebugToConsole($"Chat: {username}: {message}");
            }
            else if (IsPrivateMessage(cleanText, ref message, ref username))
            {
                LogDebugToConsole($"PM: {username}: {message}");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error: {ex.Message}");
        }
    }
}
```

### Testing Steps

1. **Load the compilation test:**
   ```
   /script compile-test
   ```

2. **If successful, load the main bot:**
   ```
   /script multi-phase-bot
   ```

3. **For testing with shorter intervals:**
   ```
   /script test-multi-phase
   ```

## C# Syntax Rules Reminder

### Correct `ref` Parameter Usage

```csharp
// 1. Declare variables first
string message = "";
string username = "";

// 2. Use in method calls without type
if (IsChatMessage(text, ref message, ref username))
{
    // Variables now contain the values set by the method
    Console.WriteLine($"{username}: {message}");
}
```

### Common Mistakes to Avoid

```csharp
// WRONG: Don't declare inline with ref
if (IsChatMessage(text, ref string message, ref string username)) // ERROR

// WRONG: Don't mix declaration styles
string message = "";
if (IsChatMessage(text, ref string message, ref username)) // ERROR

// CORRECT: Declare first, then use
string message = "";
string username = "";
if (IsChatMessage(text, ref message, ref username)) // CORRECT
```

## Status

✅ **Fixed:** `multi-phase-bot.cs`
✅ **Fixed:** `test-multi-phase.cs`  
✅ **Created:** `compile-test.cs` for verification
✅ **Verified:** `auto-responder.cs` already had correct syntax

The multi-phase bot should now compile and run without C# syntax errors.
