//MCCScript 1.0

/* Test script to verify leaderboard parsing patterns */

using System.Text.RegularExpressions;

MCC.LoadBot(new LeaderboardParsingTest());

public class LeaderboardParsingTest : ChatBot
{
    public override void Initialize()
    {
        LogToConsole("=== LEADERBOARD PARSING TEST ===");
        
        // Test data from inventory2.txt
        string[] testLoreLines = {
            "{\"extra\":[{\"color\":\"gray\",\"underlined\":false,\"text\":\" \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"#F0C741\",\"underlined\":false,\"text\":\" #1: \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"white\",\"text\":\"minidomo \",\"italic\":false},{\"color\":\"gray\",\"text\":\"(1,777,730 mobs) \",\"italic\":false},{\"color\":\"aqua\",\"text\":\"+10 points\",\"italic\":false}],\"text\":\"\"}",
            "{\"extra\":[{\"color\":\"gray\",\"underlined\":false,\"text\":\" \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"#DEDEDE\",\"underlined\":false,\"text\":\" #2: \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"white\",\"text\":\"DaSimsGamer \",\"italic\":false},{\"color\":\"gray\",\"text\":\"(139,995 mobs) \",\"italic\":false},{\"color\":\"aqua\",\"text\":\"+9 points\",\"italic\":false}],\"text\":\"\"}",
            "{\"extra\":[{\"color\":\"gray\",\"underlined\":false,\"text\":\" \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"#F0C741\",\"underlined\":false,\"text\":\" #1: \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"white\",\"text\":\"Farrnado \",\"italic\":false},{\"color\":\"gray\",\"text\":\"(31 scrolls) \",\"italic\":false},{\"color\":\"aqua\",\"text\":\"+10 points\",\"italic\":false}],\"text\":\"\"}",
            "{\"extra\":[{\"color\":\"gray\",\"underlined\":false,\"text\":\" \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"#F0C741\",\"underlined\":false,\"text\":\" #1: \",\"bold\":false,\"strikethrough\":false,\"obfuscated\":false,\"italic\":false},{\"color\":\"white\",\"text\":\"Sup3rSweZy \",\"italic\":false},{\"color\":\"gray\",\"text\":\"(105) \",\"italic\":false},{\"color\":\"aqua\",\"text\":\"+10 points\",\"italic\":false}],\"text\":\"\"}"
        };
        
        LogToConsole("Testing leaderboard parsing patterns...");
        
        for (int i = 0; i < testLoreLines.Length; i++)
        {
            LogToConsole($"\n--- Test {i + 1} ---");
            LogToConsole($"Input: {testLoreLines[i].Substring(0, Math.Min(100, testLoreLines[i].Length))}...");
            
            var result = ParsePlayerRanking(testLoreLines[i]);
            if (result != null)
            {
                LogToConsole($"✅ SUCCESS:");
                LogToConsole($"   Rank: #{result.Rank}");
                LogToConsole($"   Player: {result.PlayerName}");
                LogToConsole($"   Value: {result.Value}");
                LogToConsole($"   Points: +{result.Points}");
            }
            else
            {
                LogToConsole($"❌ FAILED to parse");
            }
        }
        
        LogToConsole("\n=== PARSING TEST COMPLETE ===");
    }
    
    private PlayerRanking ParsePlayerRanking(string loreLine)
    {
        try
        {
            if (!loreLine.Contains("#") || !loreLine.Contains(":"))
                return null;
                
            // Extract rank number
            var rankMatch = Regex.Match(loreLine, @"#(\d+):");
            if (!rankMatch.Success)
                return null;
                
            int rank = int.Parse(rankMatch.Groups[1].Value);
            
            // Extract player name using multiple patterns
            string playerName = "";
            
            // Pattern 1: Look for player name after rank and before value in parentheses
            var namePattern1 = Regex.Match(loreLine, @"#\d+:\s*"",""text"":""([^""]+)\s*""");
            if (namePattern1.Success)
            {
                playerName = namePattern1.Groups[1].Value.Trim();
            }
            else
            {
                // Pattern 2: Look for white text after rank pattern
                var namePattern2 = Regex.Match(loreLine, @"""color"":""white"",""text"":""([^""]+)\s*""");
                if (namePattern2.Success)
                {
                    playerName = namePattern2.Groups[1].Value.Trim();
                }
                else
                {
                    // Pattern 3: Fallback - extract any text before parentheses
                    var namePattern3 = Regex.Match(loreLine, @"""text"":""([^""]+)"".*?\(");
                    if (namePattern3.Success)
                    {
                        string candidate = namePattern3.Groups[1].Value.Trim();
                        // Skip if it's just rank info or symbols
                        if (!candidate.Contains("#") && !candidate.Contains(":") && candidate.Length > 1)
                        {
                            playerName = candidate;
                        }
                    }
                }
            }
            
            // Extract value (content in parentheses)
            var valueMatch = Regex.Match(loreLine, @"\(([^)]+)\)");
            string value = valueMatch.Success ? valueMatch.Groups[1].Value : "";
            
            // Extract points
            var pointsMatch = Regex.Match(loreLine, @"\+(\d+)\s*points");
            int points = pointsMatch.Success ? int.Parse(pointsMatch.Groups[1].Value) : 0;
            
            if (!string.IsNullOrEmpty(playerName))
            {
                return new PlayerRanking
                {
                    Rank = rank,
                    PlayerName = playerName,
                    Value = value,
                    Points = points,
                    RawLoreLine = loreLine
                };
            }
            
            return null;
        }
        catch (Exception ex)
        {
            LogToConsole($"Error parsing player ranking: {ex.Message}");
            return null;
        }
    }
}

public class PlayerRanking
{
    public int Rank { get; set; }
    public string PlayerName { get; set; } = "";
    public string Value { get; set; } = "";
    public int Points { get; set; }
    public string RawLoreLine { get; set; } = "";
}
