#!/usr/bin/env python3
"""
Test 10-Minute Discord Integration Cycle
Simulates MCC multi-phase bot generating Discord embeds every 10 minutes
"""

import json
import time
from datetime import datetime
from pathlib import Path

# Configuration
LOG_FILE = Path("inventory2.txt")
ISTOP_CHANNEL = "1401451296711507999"
LB_CHANNEL = "1401451313216094383"

def generate_test_embeds():
    """Generate test Discord embeds similar to what MCC bot would create"""
    current_time = datetime.now()
    timestamp = current_time.strftime("%H:%M:%S")
    
    # Test embed 1: ISTOP Channel - Bot Status
    istop_embed = {
        "embeds": [{
            "title": "🔄 Multi-Phase Bot - 10 Minute Cycle",
            "description": f"Automated data collection cycle executed at {timestamp}",
            "color": 3447003,  # Blue
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "📊 Cycle Status",
                    "value": "✅ Phase 1: Command executed\n✅ Phase 2: Island data extracted\n✅ Phase 3: Leaderboard data extracted",
                    "inline": False
                },
                {
                    "name": "⏰ Next Cycle",
                    "value": f"Scheduled for {(current_time.hour * 60 + current_time.minute + 10) // 60:02d}:{(current_time.minute + 10) % 60:02d}",
                    "inline": True
                },
                {
                    "name": "🎯 Data Quality",
                    "value": "All phases completed successfully",
                    "inline": True
                }
            ],
            "footer": {
                "text": "Multi-Phase Bot • 10-Minute Automation",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/bot-icon.png"
            }
        }]
    }
    
    # Test embed 2: LB Channel - Island Data
    island_embed = {
        "embeds": [{
            "title": "🏝️ Top Islands Data - Live Update",
            "description": f"Island rankings extracted at {timestamp}",
            "color": 39423,  # Green
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏝️ #1: Revenants",
                    "value": f"**All Time:** $2.{97 + (current_time.minute % 10)}B\n**Weekly:** $1.2B\n**Today:** +${31 + (current_time.minute % 20)}.34M (+1.5%)",
                    "inline": True
                },
                {
                    "name": "🏝️ #2: ████NIP████",
                    "value": f"**All Time:** $1.{40 + (current_time.minute % 5)}B\n**Weekly:** $800M\n**Today:** +${15 + (current_time.minute % 10)}.2M (+1.1%)",
                    "inline": True
                },
                {
                    "name": "🏝️ #3: MindControl",
                    "value": f"**All Time:** ${599 + (current_time.minute % 50)}.93M\n**Weekly:** $400M\n**Today:** +${8 + (current_time.minute % 15)}.15M (+2.7%)",
                    "inline": True
                }
            ],
            "footer": {
                "text": f"Phase 2 • 5 islands tracked • Updated every 10 minutes",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
            }
        }]
    }
    
    # Test embed 3: LB Channel - Leaderboard Data
    leaderboard_embed = {
        "embeds": [{
            "title": "🏆 Leaderboard Rankings - Live Update",
            "description": f"Player rankings extracted at {timestamp}",
            "color": 16766720,  # Orange
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏆 Mobs Killed",
                    "value": f"#1: minidomo ({1777730 + (current_time.minute * 100):,} mobs)\n#2: DaSimsGamer ({141602 + (current_time.minute * 50):,} mobs)\n#3: JustABanana777 ({130897 + (current_time.minute * 25):,} mobs)",
                    "inline": True
                },
                {
                    "name": "🏆 Scrolls Completed",
                    "value": f"#1: Jaxair ({34 + (current_time.minute % 5)} scrolls)\n#2: Farrnado ({31 + (current_time.minute % 3)} scrolls)\n#3: Roleeb ({25 + (current_time.minute % 2)} scrolls)",
                    "inline": True
                },
                {
                    "name": "🏆 Gold Shards",
                    "value": f"#1: FlactioON ({13210 + (current_time.minute * 10):,} shards)\n#2: MostlyMissing ({8945 + (current_time.minute * 5):,} shards)\n#3: LastRez ({7234 + (current_time.minute * 3):,} shards)",
                    "inline": True
                }
            ],
            "footer": {
                "text": f"Phase 3 • 24 categories tracked • Updated every 10 minutes",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
            }
        }]
    }
    
    return [
        (ISTOP_CHANNEL, istop_embed),
        (LB_CHANNEL, island_embed),
        (LB_CHANNEL, leaderboard_embed)
    ]

def append_discord_embeds_to_log(embeds):
    """Append Discord embeds to log file in MCC format"""
    current_time = datetime.now().strftime("%H:%M:%S")
    
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(f"\n[MCC] [MultiPhaseBot] === 10-MINUTE CYCLE EXECUTION at {current_time} ===\n")
        f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Discord integration: Simulation mode\n")
        
        for channel_id, embed_data in embeds:
            # Write channel line
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Sending Discord embed to channel {channel_id}\n")
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Channel: {channel_id}\n")
            
            # Write content line
            embed_json = json.dumps(embed_data, separators=(',', ':'))
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Content: {embed_json}\n")
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] ✅ Message delivered successfully\n")
            f.write("\n")
        
        f.write(f"[MCC] [MultiPhaseBot] === 10-MINUTE CYCLE COMPLETED at {current_time} ===\n")
        f.write(f"[MCC] [MultiPhaseBot] Next cycle scheduled for {datetime.now().strftime('%H:%M:%S')}\n")
        f.write("\n")

def simulate_10_minute_cycles(num_cycles=3, interval_seconds=30):
    """Simulate multiple 10-minute cycles for testing"""
    print(f"🧪 Starting 10-Minute Cycle Simulation")
    print(f"📊 Will simulate {num_cycles} cycles with {interval_seconds}s intervals")
    print(f"📁 Writing to log file: {LOG_FILE}")
    print(f"🔄 Discord bridge bot should detect and process each cycle")
    print()
    
    for cycle in range(1, num_cycles + 1):
        print(f"🔄 Cycle {cycle}/{num_cycles} - {datetime.now().strftime('%H:%M:%S')}")
        
        # Generate fresh embeds with updated data
        embeds = generate_test_embeds()
        
        # Append to log file
        append_discord_embeds_to_log(embeds)
        
        print(f"✅ Generated {len(embeds)} Discord embeds")
        print(f"📤 ISTOP Channel: 1 message")
        print(f"📤 LB Channel: 2 messages")
        print(f"📁 Appended to {LOG_FILE}")
        
        if cycle < num_cycles:
            print(f"⏰ Waiting {interval_seconds}s for next cycle...")
            print()
            time.sleep(interval_seconds)
        else:
            print("🎯 Simulation complete!")
            print()
    
    print("📊 Summary:")
    print(f"✅ Simulated {num_cycles} 10-minute cycles")
    print(f"✅ Generated {num_cycles * 3} Discord embeds total")
    print(f"✅ All embeds written to {LOG_FILE}")
    print("🔍 Check Discord bridge bot logs for processing confirmation")
    print("📱 Check Discord channels for actual message delivery")

def main():
    """Main function"""
    print("🧪 MCC Multi-Phase Bot - 10-Minute Cycle Test")
    print("=" * 60)
    print()
    
    # Check if bridge bot is running
    bridge_log = Path("discord_bridge.log")
    if bridge_log.exists():
        with open(bridge_log, 'r') as f:
            lines = f.readlines()
            if lines and "Starting to monitor log file" in lines[-5:]:
                print("✅ Discord bridge bot appears to be running")
            else:
                print("⚠️ Discord bridge bot may not be running")
                print("💡 Start it with: start-discord-bridge.bat")
    else:
        print("⚠️ No bridge bot log found")
        print("💡 Start Discord bridge bot first: start-discord-bridge.bat")
    
    print()
    
    # Ask user for simulation parameters
    try:
        cycles = int(input("Enter number of cycles to simulate (default 3): ") or "3")
        interval = int(input("Enter interval between cycles in seconds (default 30): ") or "30")
    except ValueError:
        cycles = 3
        interval = 30
    
    print()
    
    # Run simulation
    simulate_10_minute_cycles(cycles, interval)

if __name__ == "__main__":
    main()
