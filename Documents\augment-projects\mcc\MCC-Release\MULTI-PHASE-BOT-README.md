# Multi-Phase Bot Documentation

## Overview

The Multi-Phase Bot is a sophisticated MCC C# script designed to handle complex server automation tasks in multiple phases. Phase 1 implements periodic `/istop` command execution with intelligent skyblock fallback handling.

## Phase 1: Skyblock Detection and Setup

### Features

- **Periodic Task Execution**: Runs every 10 minutes (600 seconds)
- **Smart Command Detection**: Monitors server responses to detect command availability
- **Skyblock Fallback**: Automatically switches to skyblock world if `/istop` is not available
- **Robust Error Handling**: Includes retry logic and error recovery
- **State Management**: Uses proper state machine for reliable operation
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

### How It Works

1. **Idle State**: <PERSON><PERSON> waits for the next scheduled task (every 10 minutes)
2. **Send /istop**: Executes the `/istop` command
3. **Monitor Response**: Watches for server response
4. **Handle Failure**: If "Unknown command" is detected:
   - Sends `/skyblock` command
   - Waits 3-4 seconds (40 ticks) for skyblock to load
   - Retries `/istop` command
5. **Success Handling**: If `/istop` works, schedules next task
6. **Error Recovery**: Handles timeouts and failures gracefully

### State Machine

```
Idle -> WaitingForIstopResponse -> [Success] -> Idle
                                -> [Failure] -> ProcessingSkyblockLoad -> WaitingForIstopRetry -> [Success/Failure] -> Idle
                                                                                                -> [Max Retries] -> Error -> Idle
```

## Configuration

### Key Constants (in multi-phase-bot.cs)

```csharp
private const int TASK_INTERVAL_SECONDS = 600;  // 10 minutes between tasks
private const int SKYBLOCK_LOAD_WAIT_TICKS = 40; // 4 seconds wait for skyblock load
private const int MAX_RETRY_ATTEMPTS = 3;        // Maximum retry attempts
```

### Timeouts

- **Command Response Timeout**: 10 seconds
- **Error State Recovery**: 60 seconds
- **Skyblock Load Wait**: 40 ticks (4 seconds)

## Usage

### 1. Basic Usage

```
/script multi-phase-bot
```

This loads and starts the bot with default Phase 1 behavior.

### 2. Testing

For testing purposes, use the test version with shorter intervals:

```
/script test-multi-phase
```

The test version runs every 30 seconds instead of 10 minutes.

### 3. Manual Control

The bot includes methods for manual control (currently commented out):

- `EnablePhase(int phase)` - Enable a specific phase
- `DisablePhase(int phase)` - Disable a specific phase  
- `GetStatus()` - Display current bot status

## Logging Output

The bot provides comprehensive logging:

```
=== Multi-Phase Bot Initialized ===
Phase 1 Enabled: True
Task Interval: 600 seconds
Skyblock Load Wait: 40 ticks
Next task scheduled for: 14:25:30
=====================================

[Phase 1] Starting periodic task at 14:15:30
[Phase 1] Sending /istop command
[Phase 1] Server response: Unknown command. Type "/help" for help.
[Phase 1] /istop command not recognized, trying /skyblock
[Phase 1] /istop failed, sending /skyblock command
[Phase 1] Skyblock load wait complete (40 ticks), retrying /istop
[Phase 1] Sending /istop command
[Phase 1] /istop command successful after skyblock load
[Phase 1] Next task scheduled for: 14:25:30
```

## Error Handling

### Timeout Handling
- Commands that don't receive responses within 10 seconds are considered successful
- This prevents the bot from getting stuck waiting for responses

### Retry Logic
- If `/istop` fails after skyblock load, the bot will retry up to 3 times
- After max retries, the bot enters error state for 60 seconds before recovering

### Exception Handling
- All command sending is wrapped in try-catch blocks
- Exceptions are logged and trigger error state

## Extending for Future Phases

The bot is designed for easy expansion:

### Adding Phase 2

1. Add phase 2 variables:
```csharp
private bool phase2Enabled = false;
```

2. Add phase 2 handler in Update():
```csharp
if (phase2Enabled) HandlePhase2(currentTime);
```

3. Implement HandlePhase2 method:
```csharp
private void HandlePhase2(DateTime currentTime)
{
    // Phase 2 logic here
}
```

4. Update EnablePhase/DisablePhase methods to include phase 2

### Phase Structure Template

```csharp
private void HandlePhaseX(DateTime currentTime)
{
    // Phase-specific state management
    // Use LogPhase(X, "message") for consistent logging
    // Handle errors with LogPhaseError(X, "error", exception)
}
```

## Best Practices

### 1. State Management
- Always use the state machine pattern for complex logic
- Set timeouts for all waiting states
- Log state changes for debugging

### 2. Error Handling
- Wrap all server commands in try-catch blocks
- Implement retry logic with maximum attempt limits
- Provide recovery mechanisms from error states

### 3. Logging
- Use consistent logging prefixes for each phase
- Log all important state changes and actions
- Include timestamps and relevant data in logs

### 4. Timing
- Use the Update() method for timing instead of threads
- Count ticks for precise timing (10 ticks = 1 second approximately)
- Implement reasonable timeouts to prevent hanging

## Troubleshooting

### Bot Not Starting Tasks
- Check if Phase 1 is enabled
- Verify the next task time in logs
- Ensure bot is properly initialized

### Commands Not Working
- Check server response logs
- Verify the exact error message format
- Test commands manually first

### Bot Stuck in Error State
- Check for exceptions in logs
- Verify network connectivity
- Restart the bot if necessary

### Skyblock Loading Issues
- Increase SKYBLOCK_LOAD_WAIT_TICKS if skyblock takes longer to load
- Check if `/skyblock` command is available on the server
- Monitor server responses for skyblock-specific messages

## Files

- `multi-phase-bot.cs` - Main production bot (10-minute intervals)
- `test-multi-phase.cs` - Test version (30-second intervals)
- `MULTI-PHASE-BOT-README.md` - This documentation file

## Support

For issues or questions:
1. Check the console logs for error messages
2. Verify server command availability manually
3. Test with the shorter-interval test version first
4. Review the state machine logic for your specific use case
