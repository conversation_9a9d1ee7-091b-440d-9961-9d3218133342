#!/usr/bin/env python3
"""
MCC Discord Bridge Bot - External Discord Integration
Monitors MCC multi-phase bot logs and sends embeds to actual Discord channels
"""

import asyncio
import json
import re
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import aiohttp
import aiofiles
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Configuration file
CONFIG_FILE = Path("discord-bridge-config.json")

def load_config() -> Dict[str, Any]:
    """Load configuration from JSON file"""
    try:
        with open(CONFIG_FILE, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading config: {e}")
        # Fallback configuration
        return {
            "discord": {
                "bot_token": "MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.Gd7AJ8.w0YR7GFuBpMgbu5LqXys7iA1b-W3w7fIbC2wO4",
                "channels": {
                    "istop": "1401451296711507999",
                    "lb": "1401451313216094383"
                },
                "api_base": "https://discord.com/api/v10"
            },
            "monitoring": {
                "mcc_log_file": "inventory2.txt",
                "processed_messages_file": "processed_messages.json",
                "bridge_log_file": "discord_bridge.log"
            },
            "rate_limiting": {
                "delay_between_calls": 1.0,
                "max_retries": 3,
                "retry_delay": 5.0
            }
        }

# Load configuration
config = load_config()

# Extract configuration values
DISCORD_BOT_TOKEN = config["discord"]["bot_token"]
ISTOP_CHANNEL_ID = config["discord"]["channels"]["istop"]
LB_CHANNEL_ID = config["discord"]["channels"]["lb"]
DISCORD_API_BASE = config["discord"]["api_base"]
MCC_LOG_FILE = Path(config["monitoring"]["mcc_log_file"])
PROCESSED_MESSAGES_FILE = Path(config["monitoring"]["processed_messages_file"])
RATE_LIMIT_DELAY = config["rate_limiting"]["delay_between_calls"]
MAX_RETRIES = config["rate_limiting"]["max_retries"]
RETRY_DELAY = config["rate_limiting"]["retry_delay"]

# Logging setup with Windows console encoding fix
import sys
import io

# Fix Windows console encoding for emojis
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config["monitoring"]["bridge_log_file"], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DiscordBridge:
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.processed_messages = self.load_processed_messages()
        self.last_api_call = 0.0
        
    def load_processed_messages(self) -> set:
        """Load previously processed message IDs to avoid duplicates"""
        try:
            if PROCESSED_MESSAGES_FILE.exists():
                with open(PROCESSED_MESSAGES_FILE, 'r') as f:
                    data = json.load(f)
                    return set(data.get('processed', []))
        except Exception as e:
            logger.warning(f"Could not load processed messages: {e}")
        return set()
    
    def save_processed_messages(self):
        """Save processed message IDs to file"""
        try:
            with open(PROCESSED_MESSAGES_FILE, 'w') as f:
                json.dump({'processed': list(self.processed_messages)}, f)
        except Exception as e:
            logger.error(f"Could not save processed messages: {e}")
    
    async def init_session(self):
        """Initialize aiohttp session"""
        if not self.session:
            headers = {
                'Authorization': f'Bot {DISCORD_BOT_TOKEN}',
                'Content-Type': 'application/json',
                'User-Agent': 'MCC-Discord-Bridge/1.0'
            }
            self.session = aiohttp.ClientSession(headers=headers)
            logger.info("Discord API session initialized")
    
    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("Discord API session closed")
    
    async def rate_limit_wait(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_api_call
        if time_since_last < RATE_LIMIT_DELAY:
            wait_time = RATE_LIMIT_DELAY - time_since_last
            await asyncio.sleep(wait_time)
        self.last_api_call = time.time()
    
    async def send_discord_message(self, channel_id: str, embed_data: Dict[Any, Any]) -> bool:
        """Send message to Discord channel with retry logic"""
        if not self.session:
            await self.init_session()
        
        url = f"{DISCORD_API_BASE}/channels/{channel_id}/messages"
        
        for attempt in range(MAX_RETRIES):
            try:
                await self.rate_limit_wait()
                
                async with self.session.post(url, json=embed_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        message_id = result.get('id', 'unknown')
                        logger.info(f"✅ Successfully sent message {message_id} to channel {channel_id}")
                        return True
                    elif response.status == 429:  # Rate limited
                        retry_after = float(response.headers.get('Retry-After', RETRY_DELAY))
                        logger.warning(f"Rate limited, waiting {retry_after}s before retry")
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        error_text = await response.text()
                        logger.error(f"Discord API error {response.status}: {error_text}")
                        if response.status < 500:  # Client error, don't retry
                            return False
                        
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed: {e}")
                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(RETRY_DELAY)
                    continue
                return False
        
        logger.error(f"Failed to send message to channel {channel_id} after {MAX_RETRIES} attempts")
        return False
    
    def parse_mcc_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse MCC log line for Discord embed content"""
        # Pattern to match Discord embed content in MCC logs
        # [MCC] [MultiPhaseBot] [Phase 4] Channel: 1401451296711507999
        # [MCC] [MultiPhaseBot] [Phase 4] Content: {"embeds":[...]}
        
        channel_match = re.search(r'\[Phase 4\] Channel: (\d+)', line)
        if channel_match:
            return {'type': 'channel', 'channel_id': channel_match.group(1)}
        
        content_match = re.search(r'\[Phase 4\] Content: (.+)', line)
        if content_match:
            try:
                embed_json = content_match.group(1)
                embed_data = json.loads(embed_json)
                return {'type': 'content', 'embed_data': embed_data}
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse embed JSON: {e}")
                return None
        
        return None
    
    async def process_log_file(self, file_path: Path):
        """Process the entire log file for Discord messages"""
        logger.info(f"Processing log file: {file_path}")
        
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                lines = await f.readlines()
            
            current_channel = None
            message_count = 0
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                
                parsed = self.parse_mcc_log_line(line)
                if not parsed:
                    continue
                
                if parsed['type'] == 'channel':
                    current_channel = parsed['channel_id']
                    logger.debug(f"Found channel ID: {current_channel}")
                
                elif parsed['type'] == 'content' and current_channel:
                    # Create unique message ID based on line number and content hash
                    message_id = f"{file_path.name}_{i}_{hash(line)}"
                    
                    if message_id in self.processed_messages:
                        logger.debug(f"Skipping already processed message: {message_id}")
                        continue
                    
                    embed_data = parsed['embed_data']
                    logger.info(f"📤 Sending Discord message to channel {current_channel}")
                    logger.debug(f"Embed data: {json.dumps(embed_data, indent=2)}")
                    
                    success = await self.send_discord_message(current_channel, embed_data)
                    if success:
                        self.processed_messages.add(message_id)
                        message_count += 1
                        logger.info(f"✅ Message {message_count} sent successfully")
                    else:
                        logger.error(f"❌ Failed to send message to channel {current_channel}")
                    
                    current_channel = None  # Reset for next message
            
            logger.info(f"Processed {message_count} Discord messages from log file")
            self.save_processed_messages()
            
        except Exception as e:
            logger.error(f"Error processing log file: {e}")
    
    async def monitor_log_file(self, file_path: Path):
        """Monitor log file for new Discord messages"""
        logger.info(f"Starting to monitor log file: {file_path}")
        
        if not file_path.exists():
            logger.error(f"Log file does not exist: {file_path}")
            return
        
        # Process existing content first
        await self.process_log_file(file_path)
        
        # Set up file watcher for new content
        class LogFileHandler(FileSystemEventHandler):
            def __init__(self, bridge):
                self.bridge = bridge
                self.last_size = file_path.stat().st_size if file_path.exists() else 0
            
            def on_modified(self, event):
                if event.src_path == str(file_path):
                    asyncio.create_task(self.handle_file_change())
            
            async def handle_file_change(self):
                try:
                    current_size = file_path.stat().st_size
                    if current_size > self.last_size:
                        # File has grown, read new content
                        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                            await f.seek(self.last_size)
                            new_lines = await f.readlines()
                        
                        current_channel = None
                        for line in new_lines:
                            line = line.strip()
                            if not line:
                                continue
                            
                            parsed = self.bridge.parse_mcc_log_line(line)
                            if not parsed:
                                continue
                            
                            if parsed['type'] == 'channel':
                                current_channel = parsed['channel_id']
                            elif parsed['type'] == 'content' and current_channel:
                                message_id = f"{file_path.name}_{time.time()}_{hash(line)}"
                                if message_id not in self.bridge.processed_messages:
                                    embed_data = parsed['embed_data']
                                    logger.info(f"📤 New Discord message detected for channel {current_channel}")
                                    success = await self.bridge.send_discord_message(current_channel, embed_data)
                                    if success:
                                        self.bridge.processed_messages.add(message_id)
                                        self.bridge.save_processed_messages()
                                current_channel = None
                        
                        self.last_size = current_size
                except Exception as e:
                    logger.error(f"Error handling file change: {e}")
        
        event_handler = LogFileHandler(self)
        observer = Observer()
        observer.schedule(event_handler, str(file_path.parent), recursive=False)
        observer.start()
        
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            logger.info("Stopping file monitor...")
        finally:
            observer.stop()
            observer.join()

async def main():
    """Main function"""
    logger.info("🚀 Starting MCC Discord Bridge Bot")
    logger.info(f"Monitoring log file: {MCC_LOG_FILE}")
    logger.info(f"Target channels: ISTOP={ISTOP_CHANNEL_ID}, LB={LB_CHANNEL_ID}")
    
    bridge = DiscordBridge()
    
    try:
        await bridge.init_session()
        await bridge.monitor_log_file(MCC_LOG_FILE)
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        await bridge.close_session()
        logger.info("🛑 MCC Discord Bridge Bot stopped")

if __name__ == "__main__":
    asyncio.run(main())
