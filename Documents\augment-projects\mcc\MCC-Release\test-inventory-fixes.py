#!/usr/bin/env python3
"""
Test Inventory Management Fixes - Verify the inventory state management is working correctly
"""

import json
import os

def test_current_queue():
    """Test the current discord_queue.json file for proper island vs leaderboard data"""
    print("🧪 Testing Inventory Management Fixes")
    print("=" * 60)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} lines in queue file")
        
        island_messages = 0
        leaderboard_messages = 0
        corrupted_island_messages = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # Test JSON parsing
                message_data = json.loads(line)
                
                channel_name = message_data.get('channel_name', 'Unknown')
                
                if channel_name == 'LB':
                    # Check if it's island data or leaderboard data
                    embed_data = message_data.get('embed_data', {})
                    embeds = embed_data.get('embeds', [])
                    
                    if embeds:
                        title = embeds[0].get('title', '')
                        
                        if 'Islands' in title:
                            island_messages += 1
                            print(f"✅ Line {i}: Island Data Message")
                            
                            # Check for issues
                            fields = embeds[0].get('fields', [])
                            has_leaderboard_data = False
                            
                            for field in fields:
                                name = field.get('name', '')
                                value = field.get('value', '')
                                
                                # Check if island data contains leaderboard category names
                                if any(keyword in name.lower() for keyword in [
                                    'runes identified', 'pouches opened', 'votes', 
                                    'quests completed', 'experience gained', 'mobs killed',
                                    'blocks placed', 'fish caught'
                                ]):
                                    has_leaderboard_data = True
                                    print(f"   ❌ WRONG DATA: Found leaderboard category in island data: {name}")
                                
                                # Check for proper island names
                                if any(keyword in name.lower() for keyword in [
                                    'revenants', 'nip', 'minecontrol', 'island #'
                                ]):
                                    print(f"   ✅ CORRECT DATA: Found island name: {name}")
                            
                            if has_leaderboard_data:
                                corrupted_island_messages += 1
                        
                        elif 'Leaderboard' in title:
                            leaderboard_messages += 1
                            print(f"✅ Line {i}: Leaderboard Data Message")
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
        
        print()
        print("📊 Analysis Results:")
        print(f"   🏝️ Island messages: {island_messages}")
        print(f"   🏆 Leaderboard messages: {leaderboard_messages}")
        print(f"   ❌ Corrupted island messages: {corrupted_island_messages}")
        
        if corrupted_island_messages > 0:
            print()
            print("⚠️ INVENTORY MANAGEMENT ISSUE DETECTED!")
            print("   The island data contains leaderboard category names.")
            print("   This indicates the bot is processing the wrong inventory.")
            print("   Expected: Island names like 'Revenants', 'NIP', 'MineControl'")
            print("   Found: Leaderboard categories like 'Runes Identified', 'Votes'")
        else:
            print()
            print("✅ INVENTORY MANAGEMENT WORKING CORRECTLY!")
            print("   Island data contains proper island names, not leaderboard categories.")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    print("🔧 Inventory Management Fixes Verification")
    print("=" * 70)
    print()
    
    test_current_queue()
    
    print()
    print("Expected fixes:")
    print("1. ✅ Leaderboard inventory should be closed after Phase 3")
    print("2. ✅ Island data should contain actual island names (Revenants, NIP, etc.)")
    print("3. ✅ Island data should NOT contain leaderboard categories")
    print("4. ✅ Inventory validation should prevent wrong data extraction")
    print("5. ✅ Inventory cleanup should occur at cycle start")
    print()
    print("If you still see leaderboard categories in island data:")
    print("- Restart the MCC bot to apply the inventory management fixes")
    print("- Wait for a complete 10-minute cycle to see the improvements")
    print("- Check MCC console logs for inventory validation messages")

if __name__ == "__main__":
    main()
