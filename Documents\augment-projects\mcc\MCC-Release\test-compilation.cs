//MCCScript 1.0

/* Simple compilation test to verify syntax is correct */

MCC.LoadBot(new CompilationTestBot());

//MCCScript Extensions

public class CompilationTestBot : ChatBot
{
    public override void Initialize()
    {
        LogToConsole("=== Compilation Test Bot ===");
        LogToConsole("If you see this message, the compilation is working!");
        LogToConsole("Testing basic if/else syntax...");
        
        // Test basic if/else
        bool testCondition = true;
        if (testCondition)
        {
            LogToConsole("✅ Basic if statement works");
        }
        else
        {
            LogToConsole("❌ This should not appear");
        }
        
        // Test nested if/else
        int testValue = 5;
        if (testValue > 0)
        {
            LogToConsole("✅ Nested if works");
            if (testValue == 5)
            {
                LogToConsole("✅ Nested if-else works");
            }
            else
            {
                LogToConsole("❌ This should not appear");
            }
        }
        else
        {
            LogToConsole("❌ This should not appear");
        }
        
        LogToConsole("Compilation test complete!");
    }
    
    public override void GetText(string text)
    {
        // Test if/else in GetText
        if (text.Contains("test"))
        {
            LogToConsole("Test message detected");
        }
        else if (text.Contains("hello"))
        {
            LogToConsole("Hello message detected");
        }
        else
        {
            // Do nothing for other messages
        }
    }
    
    public override void Update()
    {
        // Minimal update
    }
}
