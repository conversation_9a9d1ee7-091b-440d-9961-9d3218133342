//MCCScript 1.0

/* Test script to verify <PERSON><PERSON><PERSON> escaping fix */

using System;
using System.Collections.Generic;

MCC.LoadBot(new JsonEscapingTest());

public class JsonEscapingTest : ChatBot
{
    public override void Initialize()
    {
        LogToConsole("=== JSON ESCAPING TEST ===");
        
        // Test the problematic case: JSON string containing quotes
        string embedJson = "{\"embeds\":[{\"title\":\"🏆 Top 10 Players\",\"description\":\"Player rankings extracted at 22:44:13\\n*Click buttons below to view different categories*\",\"color\":16766720}]}";
        
        LogToConsole($"Original embed JSON: {embedJson}");
        
        // Create test message data similar to WriteLeaderboardEmbedToFile
        var messageData = new
        {
            id = "test-id-123",
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
            cycle_number = 1,
            channel_id = "1401451313216094383",
            channel_name = "LB",
            embed_data_raw = embedJson,
            status = "pending",
            created_at = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            message_type = "leaderboard",
            action = "create",
            update_message_id = (string)null,
            supports_interactions = true,
            interaction_handler = "HandleLeaderboardButtonInteraction"
        };
        
        // Test the JSON serialization
        string result = SimpleJsonSerializer(messageData);
        LogToConsole("=== SERIALIZED RESULT ===");
        LogToConsole(result);
        
        // Test if it's valid JSON by checking for unescaped quotes
        bool hasUnescapedQuotes = CheckForUnescapedQuotes(result);
        LogToConsole($"=== TEST RESULT ===");
        LogToConsole($"Contains unescaped quotes: {hasUnescapedQuotes}");
        LogToConsole(hasUnescapedQuotes ? "❌ TEST FAILED" : "✅ TEST PASSED");
        
        LogToConsole("=== TEST COMPLETE ===");
    }
    
    private bool CheckForUnescapedQuotes(string json)
    {
        // Simple check for the specific pattern that was causing issues
        // Look for quote followed by opening brace (the problematic pattern)
        return json.Contains("\":\"{\") || json.Contains("\":{\"");
    }
    
    private string SimpleJsonSerializer(object obj)
    {
        if (obj == null) return "null";

        var type = obj.GetType();

        if (type == typeof(string))
        {
            // Properly escape JSON string values
            string str = obj.ToString();
            str = str.Replace("\\", "\\\\")  // Escape backslashes first
                     .Replace("\"", "\\\"")  // Escape quotes
                     .Replace("\n", "\\n")   // Escape newlines
                     .Replace("\r", "\\r")   // Escape carriage returns
                     .Replace("\t", "\\t");  // Escape tabs
            return $"\"{str}\"";
        }

        if (type == typeof(int) || type == typeof(long) || type == typeof(double) || type == typeof(float))
            return obj.ToString();

        if (type == typeof(bool))
            return obj.ToString().ToLower();

        if (obj is System.Collections.IEnumerable && !(obj is string))
        {
            var items = new List<string>();
            foreach (var item in (System.Collections.IEnumerable)obj)
            {
                items.Add(SimpleJsonSerializer(item));
            }
            return $"[{string.Join(",", items.ToArray())}]";
        }

        // Handle anonymous objects and complex types
        var properties = type.GetProperties();
        var jsonPairs = new List<string>();

        foreach (var prop in properties)
        {
            var value = prop.GetValue(obj, null);
            var jsonValue = SimpleJsonSerializer(value);
            jsonPairs.Add($"\"{prop.Name}\":{jsonValue}");
        }

        return $"{{{string.Join(",", jsonPairs.ToArray())}}}";
    }
}
