#!/usr/bin/env python3
"""
Test the JSON newline escaping fix for message ab872802
"""

import json

def test_newline_json_fix():
    """Test that our JSON newline escaping fix resolves the issue"""
    
    print("🧪 TESTING JSON NEWLINE ESCAPING FIX")
    print("=" * 60)
    
    # The exact problematic string from message ab872802
    # Context: ":52:04\n*Click button" at position 91
    problematic_desc = "Player rankings extracted at 01:52:04\n*Click buttons below to view different categories*"
    
    print(f"📝 Problematic description: {repr(problematic_desc)}")
    print(f"📍 Newline at position: {problematic_desc.find(chr(10))}")
    
    # Test 1: Direct JSON parsing (should fail)
    print(f"\n🧪 TEST 1: Direct JSON with literal newline (should fail)")
    try:
        bad_json = f'{{"description": "{problematic_desc}"}}'
        parsed = json.loads(bad_json)
        print(f"   ❌ Unexpected success!")
    except json.JSONDecodeError as e:
        print(f"   ✅ Expected failure: {e}")
        print(f"   📍 Error position: {getattr(e, 'pos', 'unknown')}")
    
    # Test 2: With proper newline escaping (should succeed)
    print(f"\n🧪 TEST 2: With proper newline escaping (should succeed)")
    fixed_desc = problematic_desc.replace('\n', '\\n')
    print(f"   Fixed description: {repr(fixed_desc)}")
    
    try:
        good_json = f'{{"description": "{fixed_desc}"}}'
        parsed = json.loads(good_json)
        print(f"   ✅ Success! Parsed: {repr(parsed['description'])}")
    except json.JSONDecodeError as e:
        print(f"   ❌ Unexpected failure: {e}")
    
    # Test 3: Full embed simulation
    print(f"\n🧪 TEST 3: Full embed with fixed newlines")
    
    embed_data = {
        "embeds": [{
            "title": "🏆 Top 10 Players",
            "description": fixed_desc,
            "color": 16766720,
            "timestamp": "2025-08-04T08:52:04.741Z",
            "fields": [{
                "name": "🏆 Top 12 Players:",
                "value": "#1: MostlyMissing 78 points - 50 GC\\n#2: Jaeger1000 73 points - 40 GC\\n#3: Nincompoopz 67 points - 35 GC",
                "inline": False
            }],
            "footer": {
                "text": "Phase 3 • 23 categories available",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
            }
        }],
        "components": [{
            "type": 1,
            "components": [
                {"type": 2, "style": 2, "label": "Top 12 Players:", "custom_id": "leaderboard_slot_4"},
                {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_slot_9"}
            ]
        }]
    }
    
    try:
        full_json = json.dumps(embed_data, separators=(',', ':'))
        print(f"   ✅ Full embed JSON creation: SUCCESS!")
        print(f"   📏 JSON length: {len(full_json)}")
        
        # Verify it parses back correctly
        reparsed = json.loads(full_json)
        print(f"   ✅ Full embed re-parsing: SUCCESS!")
        
        # Check the description specifically
        desc = reparsed['embeds'][0]['description']
        print(f"   📊 Final description: {repr(desc)}")
        
    except Exception as e:
        print(f"   ❌ Full embed test failed: {e}")

def simulate_csharp_fix():
    """Simulate our C# SimpleJsonSerializer fix"""
    
    print(f"\n🔧 SIMULATING C# SIMPLEJSONSERIALIZER FIX")
    print("=" * 60)
    
    def csharp_string_escape(text):
        """Simulate the fixed C# string escaping"""
        # Step 1: Handle backslashes first
        text = text.replace("\\", "\\\\")
        
        # Step 2: Handle quotes
        text = text.replace('"', '\\"')
        
        # Step 3: Handle newlines (THE FIX)
        text = text.replace('\n', '\\n')
        
        return f'"{text}"'
    
    # Test the problematic string
    original = "Player rankings extracted at 01:52:04\n*Click buttons below to view different categories*"
    
    print(f"📝 Original string: {repr(original)}")
    
    escaped = csharp_string_escape(original)
    print(f"🔧 C# escaped: {escaped}")
    
    # Test in JSON context
    test_json = f'{{"description": {escaped}}}'
    print(f"📄 Full JSON: {test_json}")
    
    try:
        parsed = json.loads(test_json)
        print(f"✅ C# fix simulation: SUCCESS!")
        print(f"📊 Parsed result: {repr(parsed['description'])}")
    except json.JSONDecodeError as e:
        print(f"❌ C# fix simulation: FAILED - {e}")

def test_message_ab872802_fix():
    """Test the specific fix for message ab872802"""
    
    print(f"\n🎯 TESTING SPECIFIC FIX FOR MESSAGE ab872802")
    print("=" * 60)
    
    # Recreate the exact problematic embed_data_raw content structure
    # Based on the analysis, the issue is in the description field
    
    embed_structure = {
        "embeds": [{
            "title": "🏆 Top 10 Players",
            "description": "Player rankings extracted at 01:52:04\\n*Click buttons below to view different categories*",  # Fixed
            "color": 16766720,
            "timestamp": "2025-08-04T08:52:04.741Z",
            "fields": [{
                "name": "🏆 Top 12 Players:",
                "value": "#1: MostlyMissing 78 points - 50 GC\\n#2: Jaeger1000 73 points - 40 GC\\n#3: Nincompoopz 67 points - 35 GC\\n#4: Lil_Bouncy21 44 points - 30 GC\\n#5: Cataclysm69 40 points - 25 GC\\n#6: DaSimsGamer 29 points - 20 GC\\n#7: roughjoke95 25 points - 15 GC\\n#8: Ketchup47 23 points - 10 GC\\n#9: LogicalPlays 23 points - 10 GC\\n#10: gleeked1 23 points - 5 GC",
                "inline": False
            }],
            "footer": {
                "text": "Phase 3 • 23 categories available",
                "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
            }
        }],
        "components": [
            {
                "type": 1,
                "components": [
                    {"type": 2, "style": 2, "label": "Top 12 Players:", "custom_id": "leaderboard_slot_4"},
                    {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_slot_9"},
                    {"type": 2, "style": 2, "label": "Scrolls Completed ◆ ", "custom_id": "leaderboard_slot_11"},
                    {"type": 2, "style": 2, "label": "Runes", "custom_id": "leaderboard_slot_13"},
                    {"type": 2, "style": 2, "label": "Blocks", "custom_id": "leaderboard_slot_15"}
                ]
            }
        ]
    }
    
    try:
        # Generate the JSON (this simulates what our fixed C# code should produce)
        fixed_json = json.dumps(embed_structure, separators=(',', ':'))
        print(f"✅ Fixed embed_data_raw generation: SUCCESS!")
        print(f"📏 Length: {len(fixed_json)} characters")
        
        # Test parsing (this simulates what the Discord bridge will do)
        reparsed = json.loads(fixed_json)
        print(f"✅ Discord bridge parsing: SUCCESS!")
        
        # Check for the specific position that was problematic
        desc = reparsed['embeds'][0]['description']
        print(f"📊 Description: {repr(desc)}")
        
        # Find where the newline would be
        newline_pos = fixed_json.find('\\n')
        if newline_pos != -1:
            print(f"📍 First \\n at position: {newline_pos}")
            context_start = max(0, newline_pos - 10)
            context_end = min(len(fixed_json), newline_pos + 10)
            context = fixed_json[context_start:context_end]
            print(f"📍 Context: {repr(context)}")
        
    except Exception as e:
        print(f"❌ Message ab872802 fix test failed: {e}")

if __name__ == "__main__":
    test_newline_json_fix()
    simulate_csharp_fix()
    test_message_ab872802_fix()
    
    print(f"\n" + "=" * 60)
    print(f"📋 FINAL SUMMARY:")
    print(f"🎯 ROOT CAUSE: Literal newlines in JSON strings")
    print(f"🔧 SOLUTION: Proper newline escaping in SimpleJsonSerializer")
    print(f"✅ FIX IMPLEMENTED: Line 2209 in multi-phase-bot.cs")
    print(f"🚀 EXPECTED RESULT: No more 'Invalid control character at position 91' errors")
    print(f"📝 NEXT STEP: Test with real MCC bot to verify fix works")
