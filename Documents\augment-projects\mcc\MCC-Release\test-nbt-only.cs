//MCCScript 1.0

/* Simplified NBT parsing test to isolate compilation issues */

MCC.LoadBot(new NBTOnlyTestBot());

//MCCScript Extensions

public class NBTOnlyTestBot : ChatBot
{
    private bool inventoryHandlingEnabled = false;
    
    public override void Initialize()
    {
        LogToConsole("=== NBT Only Test Bot ===");
        LogToConsole("Testing NBT parsing without full bot complexity");
        
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** WARNING: Inventory handling disabled ***");
        }
        
        LogToConsole("Run /istop to test NBT parsing");
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            
            if (cleanText.Contains("Inventory") && cleanText.Contains("opened"))
            {
                LogToConsole("*** INVENTORY OPENED - TESTING NBT PARSING ***");
                TestNBTParsing();
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in GetText: {ex.Message}");
        }
    }
    
    private void TestNBTParsing()
    {
        try
        {
            LogToConsole("*** STARTING NBT PARSING TEST ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("Cannot test - inventory handling disabled");
                return;
            }
            
            var inventories = GetInventories();
            LogToConsole($"Found {inventories.Count} inventories");
            
            if (inventories.Count == 0)
            {
                LogToConsole("No inventories found");
                return;
            }
            
            // Test inventory #2 first, then fallback
            Container targetInventory = null;
            if (inventories.ContainsKey(2))
            {
                targetInventory = inventories[2];
                LogToConsole("*** USING INVENTORY #2 ***");
            }
            else if (inventories.Count > 0)
            {
                targetInventory = inventories.Values.First();
                LogToConsole("*** USING FIRST AVAILABLE INVENTORY ***");
            }
            
            if (targetInventory != null)
            {
                TestInventoryItems(targetInventory);
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in NBT parsing test: {ex.Message}");
            LogToConsole($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private void TestInventoryItems(Container inventory)
    {
        try
        {
            LogToConsole($"*** TESTING INVENTORY WITH {inventory.Items.Count} ITEMS ***");
            
            int[] testSlots = { 13, 21, 23, 29, 31 };
            
            foreach (int slot in testSlots)
            {
                if (inventory.Items.ContainsKey(slot))
                {
                    var item = inventory.Items[slot];
                    LogToConsole($"*** TESTING SLOT {slot} ***");
                    LogToConsole($"Type: {item.Type}");
                    LogToConsole($"Display: '{item.DisplayName}'");
                    LogToConsole($"NBT null: {item.NBT == null}");
                    LogToConsole($"NBT count: {item.NBT?.Count ?? 0}");
                    
                    if (item.NBT != null && item.NBT.Count > 0)
                    {
                        LogToConsole("*** TESTING NBT PARSING ***");
                        TestItemNBT(item.NBT);
                    }
                    else
                    {
                        LogToConsole("No NBT data to test");
                    }
                    
                    LogToConsole($"*** END SLOT {slot} ***");
                    break; // Test only first found item
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing inventory items: {ex.Message}");
        }
    }
    
    private void TestItemNBT(Dictionary<string, object> nbt)
    {
        try
        {
            LogToConsole($"*** NBT HAS {nbt.Count} ENTRIES ***");
            
            foreach (var kvp in nbt)
            {
                LogToConsole($"NBT Key: '{kvp.Key}' = Type: {kvp.Value?.GetType().Name ?? "null"}");
                
                if (kvp.Key == "display" && kvp.Value is Dictionary<string, object> displayDict)
                {
                    LogToConsole("*** FOUND DISPLAY COMPOUND ***");
                    TestDisplayCompound(displayDict);
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing NBT: {ex.Message}");
        }
    }
    
    private void TestDisplayCompound(Dictionary<string, object> display)
    {
        try
        {
            LogToConsole($"*** DISPLAY HAS {display.Count} ENTRIES ***");
            
            foreach (var kvp in display)
            {
                LogToConsole($"Display Key: '{kvp.Key}' = Type: {kvp.Value?.GetType().Name ?? "null"}");
                
                if (kvp.Key == "Lore" && kvp.Value is List<object> loreList)
                {
                    LogToConsole("*** FOUND LORE LIST ***");
                    TestLoreList(loreList);
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing display compound: {ex.Message}");
        }
    }
    
    private void TestLoreList(List<object> lore)
    {
        try
        {
            LogToConsole($"*** LORE HAS {lore.Count} ENTRIES ***");
            
            for (int i = 0; i < lore.Count; i++)
            {
                var loreItem = lore[i];
                LogToConsole($"Lore[{i}]: '{loreItem?.ToString() ?? "null"}'");
                
                // Test simple value extraction
                if (loreItem != null)
                {
                    string loreText = loreItem.ToString();
                    if (loreText.Contains("$") || loreText.Contains("point"))
                    {
                        LogToConsole($"*** POTENTIAL VALUE IN LORE[{i}]: {loreText} ***");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing lore list: {ex.Message}");
        }
    }
    
    public override void Update()
    {
        // Minimal update
    }
}
