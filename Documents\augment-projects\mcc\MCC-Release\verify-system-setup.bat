@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM MCC Discord System Setup Verification Script
REM Checks all prerequisites and configuration before system startup
REM ============================================================================

title MCC Discord System Setup Verification

REM Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

echo %CYAN%============================================================================%RESET%
echo %WHITE%                    MCC DISCORD SYSTEM SETUP VERIFICATION%RESET%
echo %CYAN%============================================================================%RESET%
echo.

set "ERRORS=0"
set "WARNINGS=0"

REM ============================================================================
REM Check File Prerequisites
REM ============================================================================

echo %YELLOW%[CHECK 1]%RESET% Verifying required files...
echo.

REM Check MCC files
if exist "MinecraftClient.exe" (
    echo %GREEN%✓%RESET% MinecraftClient.exe found
) else (
    echo %RED%✗%RESET% MinecraftClient.exe NOT FOUND
    set /a ERRORS+=1
)

if exist "multi-phase-bot.cs" (
    echo %GREEN%✓%RESET% multi-phase-bot.cs found
) else (
    echo %RED%✗%RESET% multi-phase-bot.cs NOT FOUND
    set /a ERRORS+=1
)

REM Check Discord bridge files
if exist "discord-bridge-v3.py" (
    echo %GREEN%✓%RESET% discord-bridge-v3.py found (recommended)
    set "DISCORD_SCRIPT=discord-bridge-v3.py"
) else (
    if exist "discord-bridge-v2.py" (
        echo %YELLOW%⚠%RESET% discord-bridge-v2.py found (fallback)
        echo %YELLOW%  %RESET% Consider using discord-bridge-v3.py for full button support
        set "DISCORD_SCRIPT=discord-bridge-v2.py"
        set /a WARNINGS+=1
    ) else (
        echo %RED%✗%RESET% No Discord bridge script found
        set /a ERRORS+=1
    )
)

REM Check batch scripts
if exist "start-mcc-discord-system.bat" (
    echo %GREEN%✓%RESET% start-mcc-discord-system.bat found
) else (
    echo %YELLOW%⚠%RESET% start-mcc-discord-system.bat not found
    set /a WARNINGS+=1
)

if exist "stop-mcc-discord-system.bat" (
    echo %GREEN%✓%RESET% stop-mcc-discord-system.bat found
) else (
    echo %YELLOW%⚠%RESET% stop-mcc-discord-system.bat not found
    set /a WARNINGS+=1
)

echo.

REM ============================================================================
REM Check System Prerequisites
REM ============================================================================

echo %YELLOW%[CHECK 2]%RESET% Verifying system prerequisites...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%✗%RESET% Python not found in PATH
    echo %RED%  %RESET% Install Python from https://python.org
    set /a ERRORS+=1
) else (
    for /f "tokens=*" %%i in ('python --version 2^>^&1') do set "PYTHON_VERSION=%%i"
    echo %GREEN%✓%RESET% !PYTHON_VERSION! found
)

REM Check Python packages
if not errorlevel 1 (
    echo %BLUE%[INFO]%RESET% Checking Python packages...
    
    python -c "import aiohttp" >nul 2>&1
    if errorlevel 1 (
        echo %YELLOW%⚠%RESET% aiohttp package not found
        echo %YELLOW%  %RESET% Install with: pip install aiohttp
        set /a WARNINGS+=1
    ) else (
        echo %GREEN%✓%RESET% aiohttp package available
    )
    
    if "%DISCORD_SCRIPT%"=="discord-bridge-v3.py" (
        python -c "import discord" >nul 2>&1
        if errorlevel 1 (
            echo %RED%✗%RESET% discord.py package not found (required for v3)
            echo %RED%  %RESET% Install with: pip install discord.py
            set /a ERRORS+=1
        ) else (
            echo %GREEN%✓%RESET% discord.py package available
        )
    )
)

echo.

REM ============================================================================
REM Check Configuration
REM ============================================================================

echo %YELLOW%[CHECK 3]%RESET% Verifying configuration...
echo.

REM Check Discord bot token (if using v3)
if "%DISCORD_SCRIPT%"=="discord-bridge-v3.py" (
    findstr /C:"YOUR_BOT_TOKEN_HERE" discord-bridge-v3.py >nul
    if not errorlevel 1 (
        echo %RED%✗%RESET% Discord bot token not configured
        echo %RED%  %RESET% Edit discord-bridge-v3.py and set DISCORD_BOT_TOKEN
        set /a ERRORS+=1
    ) else (
        echo %GREEN%✓%RESET% Discord bot token appears to be configured
    )
)

REM Check for existing data files
if exist "island_historical_data.json" (
    echo %GREEN%✓%RESET% Historical data file exists (good for growth tracking)
) else (
    echo %YELLOW%⚠%RESET% No historical data file (will be created on first run)
    set /a WARNINGS+=1
)

if exist "discord_queue.json" (
    echo %GREEN%✓%RESET% Discord queue file exists
) else (
    echo %BLUE%[INFO]%RESET% Discord queue file will be created automatically
)

echo.

REM ============================================================================
REM Check Network/Permissions
REM ============================================================================

echo %YELLOW%[CHECK 4]%RESET% Verifying network and permissions...
echo.

REM Check if we can write to current directory
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    del test_write.tmp >nul 2>&1
    echo %GREEN%✓%RESET% Write permissions in current directory
) else (
    echo %RED%✗%RESET% Cannot write to current directory
    echo %RED%  %RESET% Run as administrator or check folder permissions
    set /a ERRORS+=1
)

REM Check internet connectivity (basic)
ping -n 1 8.8.8.8 >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠%RESET% Internet connectivity check failed
    echo %YELLOW%  %RESET% Ensure internet connection for Discord API
    set /a WARNINGS+=1
) else (
    echo %GREEN%✓%RESET% Internet connectivity available
)

echo.

REM ============================================================================
REM Summary and Recommendations
REM ============================================================================

echo %CYAN%============================================================================%RESET%
echo %WHITE%                           VERIFICATION SUMMARY%RESET%
echo %CYAN%============================================================================%RESET%
echo.

if %ERRORS% equ 0 (
    if %WARNINGS% equ 0 (
        echo %GREEN%[EXCELLENT]%RESET% All checks passed! System ready to start.
        echo.
        echo %GREEN%✓%RESET% No errors found
        echo %GREEN%✓%RESET% No warnings
        echo %GREEN%✓%RESET% All prerequisites met
        echo %GREEN%✓%RESET% Configuration appears correct
        echo.
        echo %CYAN%[READY TO START]%RESET% Run: start-mcc-discord-system.bat
    ) else (
        echo %YELLOW%[GOOD]%RESET% System ready with minor warnings (%WARNINGS% warnings)
        echo.
        echo %GREEN%✓%RESET% No critical errors
        echo %YELLOW%⚠%RESET% %WARNINGS% warning(s) - see details above
        echo.
        echo %CYAN%[READY TO START]%RESET% Run: start-mcc-discord-system.bat
        echo %YELLOW%[RECOMMENDED]%RESET% Address warnings for optimal performance
    )
) else (
    echo %RED%[ISSUES FOUND]%RESET% System NOT ready (%ERRORS% errors, %WARNINGS% warnings)
    echo.
    echo %RED%✗%RESET% %ERRORS% critical error(s) must be fixed
    if %WARNINGS% gtr 0 (
        echo %YELLOW%⚠%RESET% %WARNINGS% warning(s) should be addressed
    )
    echo.
    echo %RED%[CANNOT START]%RESET% Fix errors before running start-mcc-discord-system.bat
)

echo.

REM ============================================================================
REM Quick Fix Suggestions
REM ============================================================================

if %ERRORS% gtr 0 (
    echo %YELLOW%[QUICK FIXES]%RESET%
    echo.
    
    if not exist "MinecraftClient.exe" (
        echo %BLUE%•%RESET% Download MCC from: https://github.com/MCCTeam/Minecraft-Console-Client
    )
    
    if not exist "multi-phase-bot.cs" (
        echo %BLUE%•%RESET% Ensure multi-phase-bot.cs is in the same folder as this script
    )
    
    python --version >nul 2>&1
    if errorlevel 1 (
        echo %BLUE%•%RESET% Install Python: https://python.org/downloads/
        echo %BLUE%•%RESET% Make sure to check "Add Python to PATH" during installation
    )
    
    if "%DISCORD_SCRIPT%"=="discord-bridge-v3.py" (
        python -c "import discord" >nul 2>&1
        if errorlevel 1 (
            echo %BLUE%•%RESET% Install discord.py: pip install discord.py
        )
        
        findstr /C:"YOUR_BOT_TOKEN_HERE" discord-bridge-v3.py >nul 2>&1
        if not errorlevel 1 (
            echo %BLUE%•%RESET% Configure Discord bot token in discord-bridge-v3.py
            echo %BLUE%•%RESET% Get token from: https://discord.com/developers/applications
        )
    )
    
    echo.
)

echo %CYAN%============================================================================%RESET%
echo %WHITE%Press any key to close this verification window...%RESET%
echo %CYAN%============================================================================%RESET%

pause >nul

exit /b %ERRORS%
