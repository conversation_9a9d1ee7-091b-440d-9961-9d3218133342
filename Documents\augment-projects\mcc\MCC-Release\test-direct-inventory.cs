//MCCScript 1.0

/* Test script for direct inventory API access
 * This script tests the enhanced inventory detection and direct API access
 * without using any inventory commands
 */

MCC.LoadBot(new DirectInventoryTestBot());

//MCCScript Extensions

public class DirectInventoryTestBot : ChatBot
{
    private int messageCount = 0;
    private bool inventoryHandlingEnabled = false;
    private readonly int[] islandDataSlots = { 13, 21, 23, 29, 31 };
    
    public override void Initialize()
    {
        LogToConsole("=== Direct Inventory API Test Bot ===");
        LogToConsole("*** TESTING ENHANCED INVENTORY DETECTION ***");
        
        // Check if inventory handling is enabled
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** WARNING: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
            LogToConsole("Phase 2 will not work without inventory handling enabled");
        }
        else
        {
            LogToConsole("✅ Inventory handling is enabled - Phase 2 ready!");
        }
        
        LogToConsole("This bot will:");
        LogToConsole("1. Detect inventory opening messages");
        LogToConsole("2. Use direct MCC inventory API access");
        LogToConsole("3. Log comprehensive inventory details");
        LogToConsole("4. Extract island data from specific slots");
        LogToConsole("Try running /istop to test the complete flow");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            messageCount++;
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Check for inventory messages specifically
            if (cleanText.Contains("Inventory"))
            {
                LogToConsole($"[MSG #{messageCount}] *** INVENTORY MESSAGE DETECTED ***");
                LogToConsole($"[MSG #{messageCount}] Raw: '{text}'");
                LogToConsole($"[MSG #{messageCount}] Clean: '{cleanText}'");
                
                bool isChatMessage = IsChatMessage(cleanText, ref message, ref username);
                bool isPrivateMessage = IsPrivateMessage(cleanText, ref message, ref username);
                
                LogToConsole($"[MSG #{messageCount}] Chat: {isChatMessage}, PM: {isPrivateMessage}");
                
                if (cleanText.Contains("opened"))
                {
                    LogToConsole("[INVENTORY] *** INVENTORY OPENED DETECTED ***");
                    HandleInventoryOpened(cleanText);
                }
                else if (cleanText.Contains("closed"))
                {
                    LogToConsole("[INVENTORY] *** INVENTORY CLOSED DETECTED ***");
                    HandleInventoryClosed(cleanText);
                }
                else if (cleanText.Contains("content"))
                {
                    LogToConsole("[INVENTORY] *** INVENTORY CONTENT DETECTED ***");
                    LogToConsole("[INVENTORY] Note: Using direct API, no content parsing needed");
                }
                else
                {
                    LogToConsole($"[INVENTORY] Other inventory message: {cleanText}");
                }
            }
            
            // Log /istop related messages
            if (cleanText.ToLower().Contains("istop") || cleanText.ToLower().Contains("island"))
            {
                LogToConsole($"[ISTOP] Message #{messageCount}: '{cleanText}'");
            }
            
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error processing message #{messageCount}: {ex.Message}");
        }
    }
    
    private void HandleInventoryOpened(string inventoryMessage)
    {
        try
        {
            LogToConsole("[INVENTORY] *** PROCESSING INVENTORY OPENED EVENT ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("[INVENTORY] ❌ Inventory handling is disabled - cannot access inventory data");
                LogToConsole("[INVENTORY] Enable InventoryHandling = true in MinecraftClient.ini");
                return;
            }
            
            LogToConsole("[INVENTORY] ✅ Inventory handling is enabled, accessing inventories...");
            
            // Get all open inventories using direct MCC API
            var inventories = GetInventories();
            LogToConsole($"[INVENTORY] *** FOUND {inventories.Count} OPEN INVENTORIES ***");
            
            if (inventories.Count > 0)
            {
                LogComprehensiveInventoryDetails(inventories);
                ExtractIslandDataFromInventories(inventories);
            }
            else
            {
                LogToConsole("[INVENTORY] ❌ No open inventories found");
                LogToConsole("[INVENTORY] This could indicate:");
                LogToConsole("[INVENTORY] 1. Inventory opened and closed too quickly");
                LogToConsole("[INVENTORY] 2. Timing issue with inventory detection");
                LogToConsole("[INVENTORY] 3. Different inventory handling behavior");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[INVENTORY] ❌ Error handling inventory opened: {ex.Message}");
            LogToConsole($"[INVENTORY] Stack trace: {ex.StackTrace}");
        }
    }
    
    private void HandleInventoryClosed(string inventoryMessage)
    {
        LogToConsole("[INVENTORY] *** INVENTORY CLOSED - NO ACTION NEEDED ***");
        LogToConsole("[INVENTORY] MCC handles inventory closing automatically");
    }
    
    private void LogComprehensiveInventoryDetails(Dictionary<int, Container> inventories)
    {
        try
        {
            LogToConsole("[INVENTORY] *** COMPREHENSIVE INVENTORY ANALYSIS ***");
            
            int inventoryIndex = 0;
            foreach (var kvp in inventories)
            {
                var inventoryId = kvp.Key;
                var inventory = kvp.Value;
                inventoryIndex++;
                
                LogToConsole($"[INVENTORY] === INVENTORY #{inventoryIndex} (ID: {inventoryId}) ===");
                LogToConsole($"[INVENTORY] Type: {inventory.Type}");
                LogToConsole($"[INVENTORY] Total Items: {inventory.Items.Count}");
                LogToConsole($"[INVENTORY] Window ID: {inventory.WindowID}");
                
                if (inventory.Items.Count == 0)
                {
                    LogToConsole($"[INVENTORY] ❌ No items found in inventory #{inventoryIndex}");
                }
                else
                {
                    LogToConsole($"[INVENTORY] --- ALL ITEMS IN INVENTORY #{inventoryIndex} ---");
                    
                    foreach (var itemKvp in inventory.Items.OrderBy(x => x.Key))
                    {
                        var slot = itemKvp.Key;
                        var item = itemKvp.Value;
                        
                        LogToConsole($"[INVENTORY] Slot {slot:D2}: {item.Type} x{item.Count}");
                        LogToConsole($"[INVENTORY]   Display: '{item.DisplayName}'");
                        LogToConsole($"[INVENTORY]   NBT: {(item.NBT == null || item.NBT.Count == 0 ? "None" : $"{item.NBT.Count} entries")}");
                        
                        // Special attention to island data slots
                        if (islandDataSlots.Contains(slot))
                        {
                            LogToConsole($"[INVENTORY]   *** ISLAND DATA SLOT {slot} ***");
                            string cleanName = ExtractCleanName(item.DisplayName);
                            LogToConsole($"[INVENTORY]   Potential Island: '{cleanName}'");
                        }
                    }
                }
                
                LogToConsole($"[INVENTORY] === END INVENTORY #{inventoryIndex} ===");
            }
            
            LogToConsole("[INVENTORY] *** END COMPREHENSIVE INVENTORY ANALYSIS ***");
        }
        catch (Exception ex)
        {
            LogToConsole($"[INVENTORY] Error logging inventory details: {ex.Message}");
        }
    }
    
    private void ExtractIslandDataFromInventories(Dictionary<int, Container> inventories)
    {
        try
        {
            LogToConsole("[ISLAND] *** EXTRACTING ISLAND DATA ***");
            
            var inventory = inventories.Values.First();
            LogToConsole($"[ISLAND] Processing inventory with {inventory.Items.Count} items");
            
            int foundIslands = 0;
            foreach (int slot in islandDataSlots)
            {
                LogToConsole($"[ISLAND] --- CHECKING SLOT {slot} ---");
                
                if (inventory.Items.ContainsKey(slot))
                {
                    var item = inventory.Items[slot];
                    LogToConsole($"[ISLAND] ✅ Found item in slot {slot}");
                    LogToConsole($"[ISLAND] Type: {item.Type}");
                    LogToConsole($"[ISLAND] Display: '{item.DisplayName}'");
                    
                    string islandName = ExtractCleanName(item.DisplayName);
                    if (!string.IsNullOrEmpty(islandName))
                    {
                        foundIslands++;
                        LogToConsole($"[ISLAND] ✅ Island #{foundIslands}: '{islandName}'");
                    }
                    else
                    {
                        LogToConsole($"[ISLAND] ❌ Could not extract island name from '{item.DisplayName}'");
                    }
                }
                else
                {
                    LogToConsole($"[ISLAND] ❌ Slot {slot} is empty");
                }
            }
            
            LogToConsole($"[ISLAND] *** EXTRACTION COMPLETE: {foundIslands} islands found ***");
        }
        catch (Exception ex)
        {
            LogToConsole($"[ISLAND] Error extracting island data: {ex.Message}");
        }
    }
    
    private string ExtractCleanName(string displayName)
    {
        if (string.IsNullOrEmpty(displayName))
            return "";
            
        // Remove Minecraft formatting codes
        string cleanName = displayName;
        while (cleanName.Contains("§") && cleanName.Length > cleanName.IndexOf("§") + 1)
        {
            int formatIndex = cleanName.IndexOf("§");
            if (formatIndex < cleanName.Length - 1)
            {
                cleanName = cleanName.Remove(formatIndex, 2);
            }
            else
            {
                cleanName = cleanName.Remove(formatIndex, 1);
            }
        }
        
        return cleanName.Trim();
    }
    
    public override void Update()
    {
        static int updateCount = 0;
        updateCount++;
        
        // Log status every 300 ticks (approximately 30 seconds)
        if (updateCount % 300 == 0)
        {
            LogToConsole($"[STATUS] Bot running - {messageCount} messages processed");
            LogToConsole($"[STATUS] Inventory handling: {inventoryHandlingEnabled}");
            
            if (inventoryHandlingEnabled)
            {
                try
                {
                    var inventories = GetInventories();
                    LogToConsole($"[STATUS] Currently {inventories.Count} inventories open");
                }
                catch (Exception ex)
                {
                    LogToConsole($"[STATUS] Error checking inventories: {ex.Message}");
                }
            }
        }
    }
}
