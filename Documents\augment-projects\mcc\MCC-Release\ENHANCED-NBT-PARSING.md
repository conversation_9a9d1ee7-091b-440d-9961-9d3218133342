# Enhanced NBT Parsing - Island Data Extraction

## Overview

The MCC Multi-Phase Bot has been significantly enhanced to extract detailed island ranking data from NBT lore information. The bot now focuses on inventory #2, targets specific slots (13, 21, 23, 29, 31), and performs comprehensive NBT parsing to extract actual island statistics.

## Key Enhancements Made

### **1. Inventory #2 Prioritization**

**Before (Generic inventory selection):**
```csharp
var inventory = inventories.Values.First();  // Used any inventory
```

**After (Inventory #2 focus):**
```csharp
Container targetInventory = null;
if (inventories.ContainsKey(2))
{
    targetInventory = inventories[2];
    LogToConsole($"[Phase 2] *** USING INVENTORY #2 FOR ISLAND DATA (PREFERRED) ***");
}
else if (inventories.Count > 0)
{
    targetInventory = inventories.Values.First();
    LogToConsole($"[Phase 2] *** INVENTORY #2 NOT FOUND, USING INVENTORY #{inventoryId} AS FALLBACK ***");
}
```

### **2. Enhanced Island Name Extraction**

**Before (Basic cleaning):**
```csharp
string cleanName = displayName.Replace("§", "").Trim();
return cleanName;
```

**After (Ranking-aware parsing):**
```csharp
// Parse ranking format like "#1: Revenants (0 points)"
private string ExtractIslandNameFromRanking(string rankingText)
{
    if (rankingText.Contains("#") && rankingText.Contains(":"))
    {
        int colonIndex = rankingText.IndexOf(":");
        string afterColon = rankingText.Substring(colonIndex + 1).Trim();
        
        // Remove parenthetical information like "(0 points)"
        int openParen = afterColon.IndexOf("(");
        if (openParen > 0)
        {
            afterColon = afterColon.Substring(0, openParen).Trim();
        }
        
        return afterColon;  // Returns clean island name like "Revenants"
    }
}
```

### **3. Ranking Extraction**

**New Feature:** Extract ranking numbers from display names:
```csharp
private int ExtractRankingFromDisplayName(string displayName)
{
    string cleanName = RemoveMinecraftFormatting(displayName);
    
    // Look for pattern "#X:" at the beginning
    if (cleanName.StartsWith("#"))
    {
        int colonIndex = cleanName.IndexOf(":");
        if (colonIndex > 1)
        {
            string rankingText = cleanName.Substring(1, colonIndex - 1).Trim();
            if (int.TryParse(rankingText, out int ranking))
            {
                return ranking;  // Returns 1 for "#1: Revenants"
            }
        }
    }
    
    return 0;
}
```

### **4. Comprehensive NBT Parsing**

**Enhanced NBT Structure Analysis:**
```csharp
private NBTParseResult ParseValuesFromNBT(Dictionary<string, object> nbt)
{
    LogToConsole($"[Phase 2] *** PARSING NBT DATA WITH {nbt.Count} ENTRIES ***");
    
    // Log all NBT keys for debugging
    foreach (var kvp in nbt)
    {
        LogToConsole($"[Phase 2] NBT Key: '{kvp.Key}' = Type: {kvp.Value?.GetType().Name ?? "null"}");
        LogToConsole($"[Phase 2] NBT Value: {kvp.Value}");
    }
    
    // Look for display compound which typically contains Name and Lore
    if (nbt.ContainsKey("display") && nbt["display"] is Dictionary<string, object> displayCompound)
    {
        LogToConsole($"[Phase 2] *** FOUND DISPLAY COMPOUND ***");
        result = ParseDisplayCompound(displayCompound);
    }
}
```

**Display Compound Parsing:**
```csharp
private NBTParseResult ParseDisplayCompound(Dictionary<string, object> displayCompound)
{
    LogToConsole($"[Phase 2] *** PARSING DISPLAY COMPOUND WITH {displayCompound.Count} ENTRIES ***");
    
    // Look for Lore in the display compound
    if (displayCompound.ContainsKey("Lore"))
    {
        LogToConsole($"[Phase 2] *** FOUND LORE IN DISPLAY COMPOUND ***");
        var loreData = displayCompound["Lore"];
        result = ExtractValuesFromLoreData(loreData);
    }
}
```

### **5. Advanced Lore Data Extraction**

**Handles Multiple Lore Formats:**
```csharp
private NBTParseResult ExtractValuesFromLoreData(object loreData)
{
    if (loreData is List<object> loreList)
    {
        LogToConsole($"[Phase 2] *** PROCESSING LORE AS LIST WITH {loreList.Count} ENTRIES ***");
        
        for (int i = 0; i < loreList.Count; i++)
        {
            var item = loreList[i];
            string itemText = item.ToString();
            LogToConsole($"[Phase 2] Lore line {i + 1}: '{itemText}'");
            
            var itemResult = ParseValuesFromString(itemText);
            if (itemResult.HasValues)
            {
                LogToConsole($"[Phase 2] ✅ Found values in lore line {i + 1}");
                result.Merge(itemResult);
            }
        }
    }
    else if (loreData is object[] loreArray)
    {
        // Handle array format
    }
    else if (loreData is Dictionary<string, object> loreDict)
    {
        // Handle dictionary format
    }
}
```

### **6. Enhanced Value Pattern Recognition**

**Multiple Pattern Detection:**
```csharp
private NBTParseResult ParseValuesFromString(string text)
{
    // Pattern 1: "All-Time: $1.78B", "Weekly: $979.33M", "Today: +$50.69M (+5.5%)"
    if (cleanText.Contains("all-time") || cleanText.Contains("total"))
    {
        result.AllTime = ExtractValueFromText(text, new[] { "all-time:", "total:" });
    }
    
    // Pattern 2: Look for money values with $ signs
    if (cleanText.Contains("$") && string.IsNullOrEmpty(result.AllTime))
    {
        string moneyValue = ExtractMoneyValue(text);  // Uses regex: @"[\+\-]?\$[\d,]+\.?\d*[KMBkmb]?"
    }
    
    // Pattern 3: Look for point values
    if (cleanText.Contains("point"))
    {
        string pointValue = ExtractPointValue(text);  // Uses regex: @"[\d,]+\s*points?"
    }
    
    // Pattern 4: Look for numeric values with units (K, M, B)
    string numericValue = ExtractNumericValue(text);  // Uses regex: @"[\d,]+\.?\d*[KMBkmb]"
}
```

### **7. Enhanced Data Structure**

**Updated IslandData Class:**
```csharp
public class IslandData
{
    public string Name { get; set; } = "";
    public string AllTimeValue { get; set; } = "";
    public string WeeklyValue { get; set; } = "";
    public string TodayChange { get; set; } = "";
    public int Slot { get; set; }
    public int Ranking { get; set; }  // ← NEW: Stores ranking number
    public DateTime LastUpdated { get; set; }

    public override string ToString()
    {
        return $"#{Ranking}: {Name} | {AllTimeValue} | {WeeklyValue} | {TodayChange}";
    }
}
```

### **8. Enhanced Reporting**

**Improved Data Display:**
```csharp
private void ProcessIslandDataChanges()
{
    LogToConsole("=== Phase 2 Island Data Report ===");
    LogToConsole($"Extracted at: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
    LogToConsole($"Total islands found: {currentIslandData.Count}");
    LogToConsole("");
    LogToConsole("Rank | Island Name     | All-Time Value | Weekly Value  | Today's Change");
    LogToConsole("-----|-----------------|----------------|---------------|---------------");

    // Sort by ranking (if available) or by slot
    var sortedIslands = currentIslandData.Values
        .OrderBy(i => i.Ranking > 0 ? i.Ranking : i.Slot)
        .ToList();

    foreach (var island in sortedIslands)
    {
        string rankDisplay = island.Ranking > 0 ? $"#{island.Ranking}" : $"S{island.Slot}";
        LogToConsole($"{rankDisplay,-4} | {island.Name,-15} | {island.AllTimeValue,-14} | {island.WeeklyValue,-13} | {island.TodayChange}");
    }
    
    // Show data quality summary
    int islandsWithValues = currentIslandData.Values.Count(i => 
        !string.IsNullOrEmpty(i.AllTimeValue) && i.AllTimeValue != "Unknown");
    LogToConsole($"Data Quality: {islandsWithValues}/{currentIslandData.Count} islands have extracted values");
}
```

## Expected Output

### **1. Enhanced Inventory Detection**
```
[Phase 2] *** USING INVENTORY #2 FOR ISLAND DATA (PREFERRED) ***
[Phase 2] Inventory #2 has 45 items
[Phase 2] *** PARSING ISLAND DATA FROM INVENTORY ITEMS ***
[Phase 2] Checking island data slots: 13, 21, 23, 29, 31
```

### **2. Detailed Item Analysis**
```
[Phase 2] --- CHECKING SLOT 13 ---
[Phase 2] *** FOUND ITEM IN SLOT 13 ***
[Phase 2] Type: PlayerHead
[Phase 2] Display Name: '§a#1: Revenants (0 points)'
[Phase 2] NBT Data: 2 NBT entries
[Phase 2] Extracting ranking from: '#1: Revenants (0 points)'
[Phase 2] Extracted ranking: #1
[Phase 2] Parsing ranking text: '#1: Revenants (0 points)'
[Phase 2] Extracted island name from ranking: 'Revenants'
```

### **3. Comprehensive NBT Parsing**
```
[Phase 2] *** PARSING NBT DATA WITH 2 ENTRIES ***
[Phase 2] NBT Key: 'display' = Type: Dictionary`2
[Phase 2] *** FOUND DISPLAY COMPOUND ***
[Phase 2] *** PARSING DISPLAY COMPOUND WITH 2 ENTRIES ***
[Phase 2] Display Key: 'Name' = Type: String
[Phase 2] Display Key: 'Lore' = Type: List`1
[Phase 2] *** FOUND LORE IN DISPLAY COMPOUND ***
[Phase 2] *** PROCESSING LORE AS LIST WITH 3 ENTRIES ***
[Phase 2] Lore line 1: 'All-Time Value: $1.78B'
[Phase 2] Lore line 2: 'Weekly Value: $979.33M'
[Phase 2] Lore line 3: 'Today: +$50.69M (+5.5%)'
[Phase 2] ✅ Found values in lore line 1
[Phase 2] ✅ Found values in lore line 2
[Phase 2] ✅ Found values in lore line 3
```

### **4. Enhanced Data Report**
```
=== Phase 2 Island Data Report ===
Extracted at: 2024-01-15 21:19:05
Total islands found: 5

Rank | Island Name     | All-Time Value | Weekly Value  | Today's Change
-----|-----------------|----------------|---------------|---------------
#1   | Revenants       | $1.78B         | $979.33M      | +$50.69M (+5.5%)
#2   | NIP             | $973.68M       | $446.74M      | +$18.07M (+4.2%)
#3   | FakeDuo         | $892.45M       | $398.12M      | -$12.34M (-3.1%)
#4   | IslandName4     | $756.23M       | $289.67M      | +$8.91M (+1.2%)
#5   | IslandName5     | $634.78M       | $234.56M      | +$15.43M (+2.4%)
================================================================
Data Quality: 5/5 islands have extracted values
```

## Testing

### **1. Enhanced NBT Test Script**
```bash
/script test-enhanced-nbt-parsing
```

This comprehensive test will:
- ✅ Focus on inventory #2 specifically
- ✅ Test all island data slots (13, 21, 23, 29, 31)
- ✅ Show detailed NBT structure analysis
- ✅ Test ranking and name extraction
- ✅ Verify lore parsing capabilities

### **2. Production Bot**
```bash
/script multi-phase-bot
```

The enhanced production bot with comprehensive NBT parsing.

## Files Updated

- ✅ **`multi-phase-bot.cs`** - Enhanced with comprehensive NBT parsing and ranking extraction
- ✅ **`test-enhanced-nbt-parsing.cs`** - Specialized test for NBT parsing verification
- ✅ **`ENHANCED-NBT-PARSING.md`** - This documentation

## Key Benefits

### **1. Accurate Data Extraction**
- ✅ **Inventory #2 Focus**: Targets the correct inventory for island data
- ✅ **Ranking Preservation**: Extracts and displays island rankings
- ✅ **Clean Names**: Properly extracts island names from ranking format
- ✅ **Value Parsing**: Multiple pattern recognition for various value formats

### **2. Comprehensive NBT Analysis**
- ✅ **Structure Logging**: Shows complete NBT structure for debugging
- ✅ **Lore Processing**: Handles list, array, and dictionary lore formats
- ✅ **Pattern Recognition**: Multiple regex patterns for value extraction
- ✅ **Fallback Methods**: Multiple approaches for value detection

### **3. Enhanced Reporting**
- ✅ **Ranked Display**: Shows islands in proper ranking order
- ✅ **Data Quality**: Reports success rate of value extraction
- ✅ **Detailed Logging**: Comprehensive debugging information
- ✅ **Change Tracking**: Compares with previous data extractions

The enhanced NBT parsing should now successfully extract detailed island ranking data including values, points, and statistics from the `/istop` GUI! 🎯
