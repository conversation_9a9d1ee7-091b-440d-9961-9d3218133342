Python 3.12.6
Creating virtual environment...
Activating virtual environment...
Installing dependencies...
Collecting aiohttp>=3.8.0 (from -r requirements.txt (line 1))
  Downloading aiohttp-3.12.15-cp312-cp312-win_amd64.whl.metadata (7.9 kB)
Collecting aiofiles>=23.0.0 (from -r requirements.txt (line 2))
  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Collecting watchdog>=3.0.0 (from -r requirements.txt (line 3))
  Downloading watchdog-6.0.0-py3-none-win_amd64.whl.metadata (44 kB)
Collecting aiohappyeyeballs>=2.5.0 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Using cached aiohappyeyeballs-2.6.1-py3-none-any.whl.metadata (5.9 kB)
Collecting aiosignal>=1.4.0 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Downloading aiosignal-1.4.0-py3-none-any.whl.metadata (3.7 kB)
Collecting attrs>=17.3.0 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Using cached attrs-25.3.0-py3-none-any.whl.metadata (10 kB)
Collecting frozenlist>=1.1.1 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Downloading frozenlist-1.7.0-cp312-cp312-win_amd64.whl.metadata (19 kB)
Collecting multidict<7.0,>=4.5 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Downloading multidict-6.6.3-cp312-cp312-win_amd64.whl.metadata (5.4 kB)
Collecting propcache>=0.2.0 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Downloading propcache-0.3.2-cp312-cp312-win_amd64.whl.metadata (12 kB)
Collecting yarl<2.0,>=1.17.0 (from aiohttp>=3.8.0->-r requirements.txt (line 1))
  Downloading yarl-1.20.1-cp312-cp312-win_amd64.whl.metadata (76 kB)
Collecting typing-extensions>=4.2 (from aiosignal>=1.4.0->aiohttp>=3.8.0->-r requirements.txt (line 1))
  Using cached typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Collecting idna>=2.0 (from yarl<2.0,>=1.17.0->aiohttp>=3.8.0->-r requirements.txt (line 1))
  Using cached idna-3.10-py3-none-any.whl.metadata (10 kB)
Downloading aiohttp-3.12.15-cp312-cp312-win_amd64.whl (450 kB)
Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)
Downloading watchdog-6.0.0-py3-none-win_amd64.whl (79 kB)
Using cached aiohappyeyeballs-2.6.1-py3-none-any.whl (15 kB)
Downloading aiosignal-1.4.0-py3-none-any.whl (7.5 kB)
Using cached attrs-25.3.0-py3-none-any.whl (63 kB)
Downloading frozenlist-1.7.0-cp312-cp312-win_amd64.whl (43 kB)
Downloading multidict-6.6.3-cp312-cp312-win_amd64.whl (45 kB)
Downloading propcache-0.3.2-cp312-cp312-win_amd64.whl (41 kB)
Downloading yarl-1.20.1-cp312-cp312-win_amd64.whl (86 kB)
Using cached idna-3.10-py3-none-any.whl (70 kB)
Using cached typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Installing collected packages: watchdog, typing-extensions, propcache, multidict, idna, frozenlist, attrs, aiohappyeyeballs, aiofiles, yarl, aiosignal, aiohttp
Successfully installed aiofiles-24.1.0 aiohappyeyeballs-2.6.1 aiohttp-3.12.15 aiosignal-1.4.0 attrs-25.3.0 frozenlist-1.7.0 idna-3.10 multidict-6.6.3 propcache-0.3.2 typing-extensions-4.14.1 watchdog-6.0.0 yarl-1.20.1

[notice] A new release of pip is available: 24.2 -> 25.2
[notice] To update, run: python.exe -m pip install --upgrade pip

========================================
Starting Discord Bridge Bot...
========================================
Press Ctrl+C to stop the bot

--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 314, in main
    logger.info("🚀 Starting MCC Discord Bridge Bot")
Message: '🚀 Starting MCC Discord Bridge Bot'
Arguments: ()
2025-08-03 01:53:28,337 - INFO - 🚀 Starting MCC Discord Bridge Bot
2025-08-03 01:53:28,432 - INFO - Monitoring log file: inventory2.txt
2025-08-03 01:53:28,432 - INFO - Target channels: ISTOP=1401451296711507999, LB=1401451313216094383
2025-08-03 01:53:28,432 - INFO - Discord API session initialized
2025-08-03 01:53:28,432 - INFO - Starting to monitor log file: inventory2.txt
2025-08-03 01:53:28,433 - INFO - Processing log file: inventory2.txt
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4e4' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 222, in process_log_file
    logger.info(f"📤 Sending Discord message to channel {current_channel}")
Message: '📤 Sending Discord message to channel 1401451296711507999'
Arguments: ()
2025-08-03 01:53:28,450 - INFO - 📤 Sending Discord message to channel 1401451296711507999
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 225, in process_log_file
    success = await self.send_discord_message(current_channel, embed_data)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 144, in send_discord_message
    logger.info(f"✅ Successfully sent message {message_id} to channel {channel_id}")
Message: '✅ Successfully sent message 1401488192049254451 to channel 1401451296711507999'
Arguments: ()
2025-08-03 01:53:28,867 - INFO - ✅ Successfully sent message 1401488192049254451 to channel 1401451296711507999
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 229, in process_log_file
    logger.info(f"✅ Message {message_count} sent successfully")
Message: '✅ Message 1 sent successfully'
Arguments: ()
2025-08-03 01:53:28,869 - INFO - ✅ Message 1 sent successfully
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4e4' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 222, in process_log_file
    logger.info(f"📤 Sending Discord message to channel {current_channel}")
Message: '📤 Sending Discord message to channel 1401451296711507999'
Arguments: ()
2025-08-03 01:53:28,871 - INFO - 📤 Sending Discord message to channel 1401451296711507999
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 225, in process_log_file
    success = await self.send_discord_message(current_channel, embed_data)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 144, in send_discord_message
    logger.info(f"✅ Successfully sent message {message_id} to channel {channel_id}")
Message: '✅ Successfully sent message 1401488196222582794 to channel 1401451296711507999'
Arguments: ()
2025-08-03 01:53:29,693 - INFO - ✅ Successfully sent message 1401488196222582794 to channel 1401451296711507999
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 229, in process_log_file
    logger.info(f"✅ Message {message_count} sent successfully")
Message: '✅ Message 2 sent successfully'
Arguments: ()
2025-08-03 01:53:29,695 - INFO - ✅ Message 2 sent successfully
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4e4' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 222, in process_log_file
    logger.info(f"📤 Sending Discord message to channel {current_channel}")
Message: '📤 Sending Discord message to channel 1401451296711507999'
Arguments: ()
2025-08-03 01:53:29,698 - INFO - 📤 Sending Discord message to channel 1401451296711507999
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 225, in process_log_file
    success = await self.send_discord_message(current_channel, embed_data)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 144, in send_discord_message
    logger.info(f"✅ Successfully sent message {message_id} to channel {channel_id}")
Message: '✅ Successfully sent message 1401488200945631252 to channel 1401451296711507999'
Arguments: ()
2025-08-03 01:53:30,819 - INFO - ✅ Successfully sent message 1401488200945631252 to channel 1401451296711507999
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\logging\__init__.py", line 1163, in emit
    stream.write(msg + self.terminator)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 33: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 332, in <module>
    asyncio.run(main())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 674, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\windows_events.py", line 322, in run_forever
    super().run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 641, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\base_events.py", line 1986, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\events.py", line 88, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 322, in main
    await bridge.monitor_log_file(MCC_LOG_FILE)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 250, in monitor_log_file
    await self.process_log_file(file_path)
  File "C:\Users\<USER>\Documents\augment-projects\mcc\MCC-Release\discord-bridge-bot.py", line 229, in process_log_file
    logger.info(f"✅ Message {message_count} sent successfully")
Message: '✅ Message 3 sent successfully'
Arguments: ()
2025-08-03 01:53:30,820 - INFO - ✅ Message 3 sent successfully
2025-08-03 01:53:30,822 - ERROR - Failed to parse embed JSON: Unterminated string starting at: line 1 column 210 (char 209)
2025-08-03 01:53:30,823 - ERROR - Failed to parse embed JSON: Unterminated string starting at: line 1 column 202 (char 201)
2025-08-03 01:53:30,823 - ERROR - Failed to parse embed JSON: Unterminated string starting at: line 1 column 79 (char 78)
2025-08-03 01:53:30,823 - INFO - Processed 3 Discord messages from log file

[MCC] [MultiPhaseBot] === 10-MINUTE CYCLE EXECUTION at 02:35:00 ===
[MCC] [MultiPhaseBot] [Phase 4] Discord integration: Simulation mode
[MCC] [MultiPhaseBot] [Phase 4] Sending Discord embed to channel 1401451296711507999
[MCC] [MultiPhaseBot] [Phase 4] Channel: 1401451296711507999
[MCC] [MultiPhaseBot] [Phase 4] Content: {"embeds":[{"title":"🔄 Multi-Phase Bot - 10 Minute Cycle","description":"Automated data collection cycle executed at 02:35:00","color":3447003,"timestamp":"2025-08-03T07:35:00.000Z","fields":[{"name":"📊 Cycle Status","value":"✅ Phase 1: Command executed\n✅ Phase 2: Island data extracted\n✅ Phase 3: Leaderboard data extracted","inline":false},{"name":"⏰ Next Cycle","value":"Scheduled for 02:45","inline":true},{"name":"🎯 Data Quality","value":"All phases completed successfully","inline":true}],"footer":{"text":"Multi-Phase Bot • 10-Minute Automation","icon_url":"https://cdn.discordapp.com/attachments/123456789/bot-icon.png"}}]}
[MCC] [MultiPhaseBot] [Phase 4] ✅ Message delivered successfully

[MCC] [MultiPhaseBot] [Phase 4] Sending Discord embed to channel 1401451313216094383
[MCC] [MultiPhaseBot] [Phase 4] Channel: 1401451313216094383
[MCC] [MultiPhaseBot] [Phase 4] Content: {"embeds":[{"title":"🏝️ Top Islands Data - Live Update","description":"Island rankings extracted at 02:35:00","color":39423,"timestamp":"2025-08-03T07:35:00.000Z","fields":[{"name":"🏝️ #1: Revenants","value":"**All Time:** $2.102B\n**Weekly:** $1.2B\n**Today:** +$51.34M (+1.5%)","inline":true},{"name":"🏝️ #2: ████NIP████","value":"**All Time:** $1.45B\n**Weekly:** $800M\n**Today:** +$25.2M (+1.1%)","inline":true},{"name":"🏝️ #3: MindControl","value":"**All Time:** $634.93M\n**Weekly:** $400M\n**Today:** +$23.15M (+2.7%)","inline":true}],"footer":{"text":"Phase 2 • 5 islands tracked • Updated every 10 minutes","icon_url":"https://cdn.discordapp.com/attachments/123456789/island-icon.png"}}]}
[MCC] [MultiPhaseBot] [Phase 4] ✅ Message delivered successfully

[MCC] [MultiPhaseBot] [Phase 4] Sending Discord embed to channel 1401451313216094383
[MCC] [MultiPhaseBot] [Phase 4] Channel: 1401451313216094383
[MCC] [MultiPhaseBot] [Phase 4] Content: {"embeds":[{"title":"🏆 Leaderboard Rankings - Live Update","description":"Player rankings extracted at 02:35:00","color":16766720,"timestamp":"2025-08-03T07:35:00.000Z","fields":[{"name":"🏆 Mobs Killed","value":"#1: minidomo (1,781,230 mobs)\n#2: DaSimsGamer (143,352 mobs)\n#3: JustABanana777 (131,772 mobs)","inline":true},{"name":"🏆 Scrolls Completed","value":"#1: Jaxair (37 scrolls)\n#2: Farrnado (33 scrolls)\n#3: Roleeb (26 scrolls)","inline":true},{"name":"🏆 Gold Shards","value":"#1: FlactioON (13,560 shards)\n#2: MostlyMissing (9,120 shards)\n#3: LastRez (7,339 shards)","inline":true}],"footer":{"text":"Phase 3 • 24 categories tracked • Updated every 10 minutes","icon_url":"https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"}}]}
[MCC] [MultiPhaseBot] [Phase 4] ✅ Message delivered successfully

[MCC] [MultiPhaseBot] === 10-MINUTE CYCLE COMPLETED at 02:35:00 ===
