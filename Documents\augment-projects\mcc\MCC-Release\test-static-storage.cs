//MCCScript 1.0

/* Test script for static data storage in ChatBot classes
 * This demonstrates the correct approach for data persistence
 * when MCC variables are not available in ChatBot context
 */

MCC.LoadBot(new StaticStorageTestBot());

//MCCScript Extensions

public class StaticStorageTestBot : ChatBot
{
    // === INSTANCE DATA ===
    private int instanceCounter = 0;
    private DateTime startTime = DateTime.Now;
    
    // === STATIC DATA (persists across bot instances) ===
    private static int staticCounter = 0;
    private static Dictionary<string, string> staticData = new Dictionary<string, string>();
    private static DateTime staticStartTime = DateTime.MinValue;
    private static bool staticInitialized = false;
    
    public override void Initialize()
    {
        LogToConsole("=== Static Storage Test Bot ===");
        LogToConsole("Testing data persistence without MCC variables");
        
        // Initialize static data if first run
        if (!staticInitialized)
        {
            staticStartTime = DateTime.Now;
            staticData["first_run"] = staticStartTime.ToString("yyyy-MM-dd HH:mm:ss");
            staticData["bot_version"] = "1.0";
            staticInitialized = true;
            LogToConsole("Static data initialized for first time");
        }
        else
        {
            LogToConsole($"Static data already exists from: {staticData["first_run"]}");
        }
        
        // Increment static counter
        staticCounter++;
        LogToConsole($"This is bot instance #{staticCounter}");
        LogToConsole("===================================");
    }
    
    public override void Update()
    {
        instanceCounter++;
        
        // Update static data every 50 ticks
        if (instanceCounter % 50 == 0)
        {
            staticData["last_update"] = DateTime.Now.ToString("HH:mm:ss");
            staticData["total_updates"] = (GetStaticUpdateCount() + 1).ToString();
            
            LogDebugToConsole($"Instance #{staticCounter} - Update #{instanceCounter} - Total: {staticData["total_updates"]}");
        }
        
        // Show status every 100 ticks
        if (instanceCounter % 100 == 0)
        {
            ShowStatus();
        }
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            if (IsChatMessage(cleanText, ref message, ref username))
            {
                // Store chat statistics in static data
                int chatCount = GetStaticChatCount() + 1;
                staticData["chat_count"] = chatCount.ToString();
                staticData["last_chat_user"] = username;
                staticData["last_chat_time"] = DateTime.Now.ToString("HH:mm:ss");
                
                LogDebugToConsole($"Chat #{chatCount} from {username}: {message}");
                
                // Respond to test commands
                if (message.ToLower().Contains("static test"))
                {
                    SendText($"Static Test Response: Bot #{staticCounter}, Chat #{chatCount}, Started: {staticData["first_run"]}");
                }
                else if (message.ToLower().Contains("status"))
                {
                    ShowStatus();
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in GetText: {ex.Message}");
            
            // Track errors in static data
            int errorCount = GetStaticErrorCount() + 1;
            staticData["error_count"] = errorCount.ToString();
            staticData["last_error"] = ex.Message;
        }
    }
    
    private void ShowStatus()
    {
        LogToConsole("=== Static Storage Test Status ===");
        LogToConsole($"Bot Instance: #{staticCounter}");
        LogToConsole($"Instance Updates: {instanceCounter}");
        LogToConsole($"Static Initialized: {staticInitialized}");
        LogToConsole($"First Run: {staticData.GetValueOrDefault("first_run", "Unknown")}");
        LogToConsole($"Last Update: {staticData.GetValueOrDefault("last_update", "Never")}");
        LogToConsole($"Total Updates: {staticData.GetValueOrDefault("total_updates", "0")}");
        LogToConsole($"Chat Count: {staticData.GetValueOrDefault("chat_count", "0")}");
        LogToConsole($"Last Chat User: {staticData.GetValueOrDefault("last_chat_user", "None")}");
        LogToConsole($"Error Count: {staticData.GetValueOrDefault("error_count", "0")}");
        LogToConsole("=================================");
    }
    
    // Helper methods to safely get numeric values from static data
    private int GetStaticUpdateCount()
    {
        if (staticData.ContainsKey("total_updates") && int.TryParse(staticData["total_updates"], out int count))
            return count;
        return 0;
    }
    
    private int GetStaticChatCount()
    {
        if (staticData.ContainsKey("chat_count") && int.TryParse(staticData["chat_count"], out int count))
            return count;
        return 0;
    }
    
    private int GetStaticErrorCount()
    {
        if (staticData.ContainsKey("error_count") && int.TryParse(staticData["error_count"], out int count))
            return count;
        return 0;
    }
    
    // Method to demonstrate data persistence across bot reloads
    public void SimulateDataPersistence()
    {
        LogToConsole("=== Data Persistence Simulation ===");
        LogToConsole("This data will persist even if the bot is reloaded:");
        
        foreach (var kvp in staticData)
        {
            LogToConsole($"  {kvp.Key}: {kvp.Value}");
        }
        
        LogToConsole($"Static counter: {staticCounter}");
        LogToConsole("===================================");
    }
}

// Extension method for safe dictionary access
public static class DictionaryExtensions
{
    public static TValue GetValueOrDefault<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key, TValue defaultValue)
    {
        return dictionary.ContainsKey(key) ? dictionary[key] : defaultValue;
    }
}
