#!/usr/bin/env python3
"""
Simple Discord Bridge Bot - Focused on Core Functionality
Monitors MCC log file and sends Discord embeds
"""

import asyncio
import json
import re
import time
import aiohttp
from pathlib import Path

# Configuration
DISCORD_BOT_TOKEN = "MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.Gd7AJ8.w0YR7GFuBpMgbu5LqXys7iA1b-W3w7fIbC2wO4"
ISTOP_CHANNEL_ID = "1401451296711507999"
LB_CHANNEL_ID = "1401451313216094383"
DISCORD_API_BASE = "https://discord.com/api/v10"
MCC_LOG_FILE = Path("inventory2.txt")
PROCESSED_MESSAGES_FILE = Path("processed_messages.json")

class SimpleDiscordBridge:
    def __init__(self):
        self.session = None
        self.processed_messages = self.load_processed_messages()
        self.last_file_size = 0
        
    def load_processed_messages(self):
        """Load processed message IDs"""
        try:
            if PROCESSED_MESSAGES_FILE.exists():
                with open(PROCESSED_MESSAGES_FILE, 'r') as f:
                    data = json.load(f)
                    return set(data.get('processed', []))
        except Exception as e:
            print(f"Could not load processed messages: {e}")
        return set()
    
    def save_processed_messages(self):
        """Save processed message IDs"""
        try:
            with open(PROCESSED_MESSAGES_FILE, 'w') as f:
                json.dump({'processed': list(self.processed_messages)}, f)
        except Exception as e:
            print(f"Could not save processed messages: {e}")
    
    async def init_session(self):
        """Initialize HTTP session"""
        if not self.session:
            headers = {
                'Authorization': f'Bot {DISCORD_BOT_TOKEN}',
                'Content-Type': 'application/json',
                'User-Agent': 'MCC-Discord-Bridge/1.0'
            }
            self.session = aiohttp.ClientSession(headers=headers)
            print("Discord API session initialized")
    
    async def close_session(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def send_discord_message(self, channel_id, embed_data):
        """Send message to Discord"""
        if not self.session:
            await self.init_session()
        
        url = f"{DISCORD_API_BASE}/channels/{channel_id}/messages"
        
        try:
            async with self.session.post(url, json=embed_data) as response:
                if response.status == 200:
                    result = await response.json()
                    message_id = result.get('id', 'unknown')
                    print(f"✅ Successfully sent message {message_id} to channel {channel_id}")
                    return True
                elif response.status == 429:
                    retry_after = float(response.headers.get('Retry-After', 5))
                    print(f"Rate limited, waiting {retry_after}s")
                    await asyncio.sleep(retry_after)
                    return False
                else:
                    error_text = await response.text()
                    print(f"Discord API error {response.status}: {error_text}")
                    return False
        except Exception as e:
            print(f"Error sending Discord message: {e}")
            return False
    
    def parse_log_line(self, line):
        """Parse MCC log line for Discord content"""
        # Look for channel ID
        channel_match = re.search(r'\[Phase 4\] Channel: (\d+)', line)
        if channel_match:
            return {'type': 'channel', 'channel_id': channel_match.group(1)}
        
        # Look for embed content
        content_match = re.search(r'\[Phase 4\] Content: (.+)', line)
        if content_match:
            try:
                embed_json = content_match.group(1)
                embed_data = json.loads(embed_json)
                return {'type': 'content', 'embed_data': embed_data}
            except json.JSONDecodeError as e:
                print(f"Failed to parse embed JSON: {e}")
                return None
        
        return None
    
    async def process_new_content(self):
        """Process new content in log file"""
        if not MCC_LOG_FILE.exists():
            print(f"Log file {MCC_LOG_FILE} not found")
            return
        
        try:
            current_size = MCC_LOG_FILE.stat().st_size
            if current_size <= self.last_file_size:
                return  # No new content
            
            # Read new content
            with open(MCC_LOG_FILE, 'r', encoding='utf-8') as f:
                f.seek(self.last_file_size)
                new_lines = f.readlines()
            
            self.last_file_size = current_size
            
            # Process new lines
            current_channel = None
            message_count = 0
            
            for i, line in enumerate(new_lines):
                line = line.strip()
                if not line:
                    continue
                
                parsed = self.parse_log_line(line)
                if not parsed:
                    continue
                
                if parsed['type'] == 'channel':
                    current_channel = parsed['channel_id']
                    print(f"Found channel ID: {current_channel}")
                
                elif parsed['type'] == 'content' and current_channel:
                    # Create unique message ID
                    message_id = f"{MCC_LOG_FILE.name}_{time.time()}_{hash(line)}"
                    
                    if message_id in self.processed_messages:
                        continue
                    
                    embed_data = parsed['embed_data']
                    print(f"Sending Discord message to channel {current_channel}")
                    
                    success = await self.send_discord_message(current_channel, embed_data)
                    if success:
                        self.processed_messages.add(message_id)
                        message_count += 1
                        print(f"Message {message_count} sent successfully")
                    
                    current_channel = None  # Reset for next message
                    await asyncio.sleep(1)  # Rate limiting
            
            if message_count > 0:
                self.save_processed_messages()
                print(f"Processed {message_count} new Discord messages")
            
        except Exception as e:
            print(f"Error processing new content: {e}")
    
    async def monitor_log_file(self):
        """Monitor log file for changes"""
        print(f"Starting to monitor log file: {MCC_LOG_FILE}")
        
        if MCC_LOG_FILE.exists():
            self.last_file_size = MCC_LOG_FILE.stat().st_size
            print(f"Initial file size: {self.last_file_size} bytes")
        
        while True:
            try:
                await self.process_new_content()
                await asyncio.sleep(2)  # Check every 2 seconds
            except KeyboardInterrupt:
                print("Stopping file monitor...")
                break
            except Exception as e:
                print(f"Error in file monitoring: {e}")
                await asyncio.sleep(5)

async def main():
    """Main function"""
    print("🚀 Starting Simple MCC Discord Bridge Bot")
    print(f"Monitoring log file: {MCC_LOG_FILE}")
    print(f"Target channels: ISTOP={ISTOP_CHANNEL_ID}, LB={LB_CHANNEL_ID}")
    print("Press Ctrl+C to stop")
    print()
    
    bridge = SimpleDiscordBridge()
    
    try:
        await bridge.init_session()
        await bridge.monitor_log_file()
    except KeyboardInterrupt:
        print("Received interrupt signal, shutting down...")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        await bridge.close_session()
        print("Simple Discord Bridge Bot stopped")

if __name__ == "__main__":
    asyncio.run(main())
