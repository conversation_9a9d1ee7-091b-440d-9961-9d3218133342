@echo off
echo ========================================
echo FORCE RESTART DISCORD INTEGRATION SYSTEM
echo ========================================
echo.

echo This script will:
echo 1. Clear all corrupted JSON data
echo 2. Reset processed message tracking
echo 3. Provide restart instructions for both bots
echo.

echo Step 1: Backing up current data...
if exist "discord_queue.json" (
    copy discord_queue.json discord_queue_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.json >nul 2>&1
    echo ✅ Queue file backed up
)

if exist "processed_discord_messages.json" (
    copy processed_discord_messages.json processed_messages_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.json >nul 2>&1
    echo ✅ Processed messages backed up
)

echo.
echo Step 2: Clearing all corrupted data...
echo. > discord_queue.json
echo ✅ Queue file cleared

if exist "processed_discord_messages.json" (
    del processed_discord_messages.json
    echo ✅ Processed messages cleared
)

if exist "discord_bridge_v2.log" (
    del discord_bridge_v2.log
    echo ✅ Bridge bot log cleared
)

echo.
echo ========================================
echo DATA CLEARED SUCCESSFULLY!
echo ========================================
echo.
echo CRITICAL: You must now restart BOTH bots in the correct order:
echo.
echo 1. STOP Discord Bridge Bot (Ctrl+C in its window)
echo 2. STOP MCC Bot (if running)
echo.
echo 3. START Discord Bridge Bot:
echo    start-discord-bridge-v2.bat
echo    (Wait for "Monitoring for new Discord messages...")
echo.
echo 4. START MCC Bot with NEW CODE:
echo    /script multi-phase-bot
echo    (This will load the fixed JSON generation code)
echo.
echo 5. VERIFY SUCCESS:
echo    - Bridge bot should show "Successfully sent message" logs
echo    - Discord channels should receive messages
echo    - No more JSON parsing errors
echo.
echo Expected timeline:
echo - 0:10 seconds: First MCC cycle starts
echo - 0:15 seconds: First Discord messages appear
echo - 10:00 minutes: Regular cycle with real SkyBlock data
echo.
echo ========================================
echo RESTART BOTH BOTS NOW!
echo ========================================
pause
