//MCCScript 1.0

/* Test script for Multi-Phase Bot with Phase 2
 * This script demonstrates Phase 2 island data extraction
 * with shorter intervals for testing purposes.
 */

MCC.LoadBot(new TestPhase2Bot());

//MCCScript Extensions

public class TestPhase2Bot : ChatBot
{
    // === TEST CONFIGURATION ===
    private const int TASK_INTERVAL_SECONDS = 60; // 1 minute for testing
    private const string UNKNOWN_COMMAND_RESPONSE = "Unknown command. Type \"/help\" for help.";
    
    // === STATE MANAGEMENT ===
    private enum BotState
    {
        Idle,
        WaitingForIstopResponse,
        WaitingForInventoryOpen,
        ExtractingIslandData,
        WaitingForInventoryClose,
        Error
    }
    
    private BotState currentState = BotState.Idle;
    private DateTime nextTaskRun = DateTime.Now.AddSeconds(10); // Start after 10 seconds
    private DateTime stateChangeTime = DateTime.Now;
    private int tickCounter = 0;
    
    // === PHASE 2 DATA ===
    private readonly int[] islandDataSlots = { 13, 21, 23, 29, 31 };
    private Dictionary<string, IslandData> currentIslandData = new Dictionary<string, IslandData>();
    
    public override void Initialize()
    {
        LogToConsole("=== TEST Phase 2 Bot Initialized ===");
        LogToConsole("*** TESTING PHASE 2 ISLAND DATA EXTRACTION ***");
        LogToConsole($"Task Interval: {TASK_INTERVAL_SECONDS} seconds (TEST MODE)");
        LogToConsole($"First task scheduled for: {nextTaskRun:HH:mm:ss}");
        LogToConsole("==============================================");
    }
    
    public override void Update()
    {
        tickCounter++;
        DateTime currentTime = DateTime.Now;
        
        switch (currentState)
        {
            case BotState.Idle:
                if (nextTaskRun <= currentTime)
                {
                    LogToConsole($"[TEST] Starting /istop task at {currentTime:HH:mm:ss}");
                    SendIstopCommand();
                }
                break;
                
            case BotState.WaitingForIstopResponse:
                if ((currentTime - stateChangeTime).TotalSeconds > 10)
                {
                    LogToConsole("[TEST] Timeout waiting for /istop, assuming success and waiting for inventory");
                    ChangeState(BotState.WaitingForInventoryOpen);
                }
                break;
                
            case BotState.WaitingForInventoryOpen:
                if ((currentTime - stateChangeTime).TotalSeconds > 15)
                {
                    LogToConsole("[TEST] Timeout waiting for inventory, skipping Phase 2");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
                
            case BotState.ExtractingIslandData:
                if ((currentTime - stateChangeTime).TotalSeconds > 10)
                {
                    LogToConsole("[TEST] Timeout during data extraction, closing inventory");
                    CloseInventory();
                }
                break;
                
            case BotState.WaitingForInventoryClose:
                if ((currentTime - stateChangeTime).TotalSeconds > 10)
                {
                    LogToConsole("[TEST] Timeout waiting for inventory close, assuming closed");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
                
            case BotState.Error:
                if ((currentTime - stateChangeTime).TotalSeconds > 30)
                {
                    LogToConsole("[TEST] Recovering from error state");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
        }
    }
    
    private void SendIstopCommand()
    {
        try
        {
            LogToConsole("[TEST] Sending /istop command");
            SendText("/istop");
            ChangeState(BotState.WaitingForIstopResponse);
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error sending /istop: {ex.Message}");
            ChangeState(BotState.Error);
        }
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Log all server responses for testing
            if (!IsChatMessage(cleanText, ref message, ref username) && 
                !IsPrivateMessage(cleanText, ref message, ref username))
            {
                LogToConsole($"[TEST] Server response: {cleanText}");
                HandleServerResponse(cleanText);
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error processing text: {ex.Message}");
        }
    }
    
    private void HandleServerResponse(string response)
    {
        if (currentState == BotState.WaitingForIstopResponse)
        {
            if (response.Contains(UNKNOWN_COMMAND_RESPONSE))
            {
                LogToConsole("[TEST] /istop not recognized, skipping to inventory wait");
                ChangeState(BotState.WaitingForInventoryOpen);
            }
            else if (response.Contains("istop") || response.Contains("stop"))
            {
                LogToConsole("[TEST] /istop successful, waiting for inventory GUI");
                ChangeState(BotState.WaitingForInventoryOpen);
            }
        }
        else if (currentState == BotState.WaitingForInventoryOpen)
        {
            if (response.Contains("Inventory # 1 opened:"))
            {
                LogToConsole("[TEST] Inventory GUI opened! Extracting data...");
                ChangeState(BotState.ExtractingIslandData);
                ExtractIslandData();
            }
        }
        else if (currentState == BotState.ExtractingIslandData)
        {
            if (response.Contains("Inventory # 1 content:"))
            {
                LogToConsole("[TEST] Got inventory content, parsing data...");
                ParseInventoryData(response);
            }
        }
        else if (currentState == BotState.WaitingForInventoryClose)
        {
            if (response.Contains("Inventory # 1 closed"))
            {
                LogToConsole("[TEST] Inventory closed, Phase 2 complete!");
                DisplayResults();
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
    }
    
    private void ExtractIslandData()
    {
        try
        {
            LogToConsole("[TEST] Requesting inventory list...");
            SendText("/inventory 1 list");
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error requesting inventory: {ex.Message}");
            CloseInventory();
        }
    }
    
    private void CloseInventory()
    {
        try
        {
            LogToConsole("[TEST] Closing inventory...");
            SendText("/inventory 1 close");
            ChangeState(BotState.WaitingForInventoryClose);
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error closing inventory: {ex.Message}");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }
    
    private void ParseInventoryData(string inventoryData)
    {
        try
        {
            LogToConsole("[TEST] Parsing inventory data...");
            currentIslandData.Clear();
            
            string[] lines = inventoryData.Split('\n');
            LogToConsole($"[TEST] Got {lines.Length} lines of inventory data");
            
            foreach (int slot in islandDataSlots)
            {
                LogToConsole($"[TEST] Checking slot {slot}...");
                // Simple parsing for testing - look for slot data
                foreach (string line in lines)
                {
                    if (line.Contains($"Slot #{slot}:"))
                    {
                        LogToConsole($"[TEST] Found data in slot {slot}: {line}");
                        // For testing, just log what we find
                        break;
                    }
                }
            }
            
            LogToConsole($"[TEST] Parsing complete, found {currentIslandData.Count} islands");
            CloseInventory();
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error parsing inventory: {ex.Message}");
            CloseInventory();
        }
    }
    
    private void DisplayResults()
    {
        LogToConsole("=== TEST Phase 2 Results ===");
        LogToConsole($"Islands found: {currentIslandData.Count}");
        LogToConsole($"Slots checked: {string.Join(", ", islandDataSlots)}");
        LogToConsole("============================");
    }
    
    private void ChangeState(BotState newState)
    {
        if (currentState != newState)
        {
            LogToConsole($"[TEST] State: {currentState} -> {newState}");
            currentState = newState;
            stateChangeTime = DateTime.Now;
        }
    }
    
    private void ScheduleNextTask()
    {
        nextTaskRun = DateTime.Now.AddSeconds(TASK_INTERVAL_SECONDS);
        LogToConsole($"[TEST] Next task: {nextTaskRun:HH:mm:ss}");
    }
}

// Simple IslandData class for testing
public class IslandData
{
    public string Name { get; set; } = "";
    public string AllTimeValue { get; set; } = "";
    public string WeeklyValue { get; set; } = "";
    public string TodayChange { get; set; } = "";
    public int Slot { get; set; }
}
