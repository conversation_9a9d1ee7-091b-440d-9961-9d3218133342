#!/usr/bin/env python3
"""
Control Character Debug Logger
Comprehensive logging system to diagnose control character issues
"""

import json
import datetime
import os
import sys
from typing import Dict, List, Any, Optional

class ControlCharacterDebugger:
    def __init__(self, log_file: str = "control_character_debug.log"):
        self.log_file = log_file
        self.ensure_log_file()
    
    def ensure_log_file(self):
        """Ensure the log file exists and is writable"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"Control Character Debug Session Started: {datetime.datetime.now()}\n")
                f.write(f"{'='*80}\n")
        except Exception as e:
            print(f"Warning: Could not initialize debug log file: {e}")
    
    def log_message(self, component: str, level: str, message: str):
        """Log a message with timestamp and component info"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp}] [{component}] [{level}] {message}\n")
        except Exception as e:
            print(f"Warning: Could not write to debug log: {e}")
    
    def analyze_json_string(self, json_str: str, message_id: str = "unknown") -> Dict[str, Any]:
        """Analyze a JSON string for control characters and other issues"""
        analysis = {
            'message_id': message_id,
            'length': len(json_str),
            'control_characters': [],
            'problematic_positions': [],
            'character_at_91': None,
            'character_at_92': None,
            'hex_dump_around_91': None,
            'unicode_replacement_chars': [],
            'parsing_error': None
        }
        
        # Find all control characters
        for i, char in enumerate(json_str):
            char_code = ord(char)
            if char_code < 32 and char not in ['\n', '\r', '\t']:
                analysis['control_characters'].append({
                    'position': i,
                    'character': repr(char),
                    'unicode': char_code,
                    'hex': f"0x{char_code:02X}"
                })
                analysis['problematic_positions'].append(i)
        
        # Check specific positions mentioned in errors
        if len(json_str) > 90:
            analysis['character_at_91'] = {
                'char': json_str[90],
                'repr': repr(json_str[90]),
                'ord': ord(json_str[90]),
                'hex': f"0x{ord(json_str[90]):02X}"
            }
        
        if len(json_str) > 91:
            analysis['character_at_92'] = {
                'char': json_str[91],
                'repr': repr(json_str[91]),
                'ord': ord(json_str[91]),
                'hex': f"0x{ord(json_str[91]):02X}"
            }
        
        # Hex dump around position 91
        if len(json_str) > 85:
            start = max(0, 85)
            end = min(len(json_str), 100)
            hex_section = json_str[start:end]
            analysis['hex_dump_around_91'] = {
                'start_pos': start,
                'end_pos': end,
                'text': repr(hex_section),
                'hex_bytes': ' '.join(f"{ord(c):02X}" for c in hex_section)
            }
        
        # Find Unicode replacement characters
        for i, char in enumerate(json_str):
            if char == '\uFFFD':  # Unicode replacement character
                analysis['unicode_replacement_chars'].append(i)
        
        # Test JSON parsing
        try:
            json.loads(json_str)
        except json.JSONDecodeError as e:
            analysis['parsing_error'] = {
                'message': str(e),
                'position': getattr(e, 'pos', None),
                'line': getattr(e, 'lineno', None),
                'column': getattr(e, 'colno', None)
            }
        
        return analysis
    
    def log_json_analysis(self, component: str, json_str: str, message_id: str = "unknown", context: str = ""):
        """Log detailed analysis of a JSON string"""
        analysis = self.analyze_json_string(json_str, message_id)
        
        self.log_message(component, "DEBUG", f"JSON Analysis for message {message_id} {context}")
        self.log_message(component, "DEBUG", f"  Length: {analysis['length']} characters")
        
        if analysis['control_characters']:
            self.log_message(component, "WARNING", f"  Found {len(analysis['control_characters'])} control characters:")
            for ctrl in analysis['control_characters'][:10]:  # Limit to first 10
                self.log_message(component, "WARNING", 
                    f"    Position {ctrl['position']}: {ctrl['character']} (U+{ctrl['hex'][2:].zfill(4)})")
        else:
            self.log_message(component, "INFO", "  No control characters found")
        
        if analysis['character_at_91']:
            char_91 = analysis['character_at_91']
            self.log_message(component, "DEBUG", 
                f"  Character at position 91: {char_91['repr']} (ord: {char_91['ord']}, hex: {char_91['hex']})")
        
        if analysis['character_at_92']:
            char_92 = analysis['character_at_92']
            self.log_message(component, "DEBUG", 
                f"  Character at position 92: {char_92['repr']} (ord: {char_92['ord']}, hex: {char_92['hex']})")
        
        if analysis['hex_dump_around_91']:
            hex_dump = analysis['hex_dump_around_91']
            self.log_message(component, "DEBUG", 
                f"  Hex dump around position 91 (pos {hex_dump['start_pos']}-{hex_dump['end_pos']}):")
            self.log_message(component, "DEBUG", f"    Text: {hex_dump['text']}")
            self.log_message(component, "DEBUG", f"    Hex:  {hex_dump['hex_bytes']}")
        
        if analysis['unicode_replacement_chars']:
            self.log_message(component, "WARNING", 
                f"  Unicode replacement characters at positions: {analysis['unicode_replacement_chars']}")
        
        if analysis['parsing_error']:
            error = analysis['parsing_error']
            self.log_message(component, "ERROR", f"  JSON parsing failed: {error['message']}")
            if error['position'] is not None:
                self.log_message(component, "ERROR", f"    Error at position: {error['position']}")
    
    def log_mcc_bot_output(self, message_id: str, raw_embed_data: str, serialized_json: str):
        """Log MCC bot JSON generation process"""
        self.log_message("MCC_BOT", "INFO", f"Processing message {message_id}")
        
        # Log raw embed data
        self.log_message("MCC_BOT", "DEBUG", f"Raw embed data length: {len(raw_embed_data)}")
        if len(raw_embed_data) < 1000:  # Only log if not too long
            self.log_message("MCC_BOT", "DEBUG", f"Raw embed data: {repr(raw_embed_data)}")
        
        # Analyze serialized JSON
        self.log_json_analysis("MCC_BOT", serialized_json, message_id, "(after SimpleJsonSerializer)")
    
    def log_discord_bridge_error(self, message_id: str, json_str: str, error_message: str):
        """Log Discord bridge parsing error"""
        self.log_message("DISCORD_BRIDGE", "ERROR", f"Failed to parse message {message_id}: {error_message}")
        self.log_json_analysis("DISCORD_BRIDGE", json_str, message_id, "(failed parsing)")
    
    def compare_json_strings(self, original: str, processed: str, message_id: str = "unknown"):
        """Compare original and processed JSON strings to see what changed"""
        self.log_message("COMPARISON", "INFO", f"Comparing JSON strings for message {message_id}")
        
        if original == processed:
            self.log_message("COMPARISON", "INFO", "  Strings are identical - no changes made")
            return
        
        # Find differences
        min_len = min(len(original), len(processed))
        differences = []
        
        for i in range(min_len):
            if original[i] != processed[i]:
                differences.append({
                    'position': i,
                    'original': repr(original[i]),
                    'processed': repr(processed[i]),
                    'original_ord': ord(original[i]),
                    'processed_ord': ord(processed[i])
                })
        
        if len(original) != len(processed):
            self.log_message("COMPARISON", "WARNING", 
                f"  Length difference: original={len(original)}, processed={len(processed)}")
        
        if differences:
            self.log_message("COMPARISON", "INFO", f"  Found {len(differences)} character differences:")
            for diff in differences[:10]:  # Limit to first 10
                self.log_message("COMPARISON", "INFO", 
                    f"    Position {diff['position']}: {diff['original']} → {diff['processed']} "
                    f"(ord: {diff['original_ord']} → {diff['processed_ord']})")
        else:
            self.log_message("COMPARISON", "INFO", "  No character differences found")

# Global debugger instance
debugger = ControlCharacterDebugger()

def debug_json_string(component: str, json_str: str, message_id: str = "unknown", context: str = ""):
    """Convenience function to debug a JSON string"""
    debugger.log_json_analysis(component, json_str, message_id, context)

def debug_mcc_output(message_id: str, raw_data: str, serialized: str):
    """Convenience function to debug MCC bot output"""
    debugger.log_mcc_bot_output(message_id, raw_data, serialized)

def debug_bridge_error(message_id: str, json_str: str, error: str):
    """Convenience function to debug Discord bridge error"""
    debugger.log_discord_bridge_error(message_id, json_str, error)

def compare_json(original: str, processed: str, message_id: str = "unknown"):
    """Convenience function to compare JSON strings"""
    debugger.compare_json_strings(original, processed, message_id)

if __name__ == "__main__":
    # Test the debugger
    print("Testing Control Character Debugger...")
    
    # Test with problematic JSON
    test_json = '{"test": "Player\\x08Name", "value": "Item\\x07Alert"}'
    debug_json_string("TEST", test_json, "test-001", "(test case)")
    
    print("Debug log written to control_character_debug.log")
