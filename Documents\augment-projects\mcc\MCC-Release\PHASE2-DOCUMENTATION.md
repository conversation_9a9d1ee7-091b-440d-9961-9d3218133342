# Multi-Phase Bot - Phase 2 Documentation

## Overview

Phase 2 extends the Multi-Phase Bot to extract and track island top data from the `/istop` command GUI. It seamlessly integrates with Phase 1 to provide comprehensive island ranking monitoring and change tracking.

## Phase 2 Features

### 🎯 **Core Functionality**
- **GUI Detection**: Automatically detects when `/istop` opens an inventory GUI
- **Data Extraction**: Extracts island data from specific inventory slots (13, 21, 23, 29, 31)
- **Structured Parsing**: Parses island names, values, and change data
- **Change Tracking**: Compares current data with previous runs to detect changes
- **Data Persistence**: Stores data in MCC variables for access by future phases

### 📊 **Data Structure**
Each island entry contains:
- **Island Name**: e.g., "Revenants", "NIP", "FakeDuo"
- **All-Time Value**: e.g., "$1.78B"
- **Weekly Value**: e.g., "$979.33M"  
- **Today's Change**: e.g., "+$50.69M (+5.5%)"
- **Slot Position**: GUI slot number (13, 21, 23, 29, 31)
- **Last Updated**: Timestamp of data extraction

## State Flow

```
Phase 1: /istop Success → Phase 2: Wait for GUI → Extract Data → Close GUI → Return to Phase 1
```

### **Detailed State Machine**

1. **WaitingForInventoryOpen**: Waits for "Inventory # 1 opened:" message
2. **ExtractingIslandData**: Sends `/inventory 1 list` and parses response
3. **WaitingForInventoryClose**: Sends `/inventory 1 close` and waits for confirmation

## Configuration

### **Key Settings**
```csharp
private readonly int[] islandDataSlots = { 13, 21, 23, 29, 31 }; // Top 5 island slots
private bool phase2Enabled = true;                               // Enable/disable Phase 2
```

### **Timeouts**
- **Inventory Open**: 15 seconds
- **Data Extraction**: 10 seconds  
- **Inventory Close**: 10 seconds

## Data Extraction Process

### **1. GUI Detection**
```
Server Response: "Inventory # 1 opened:"
→ Triggers Phase 2 activation
```

### **2. Data Request**
```
Command: /inventory 1 list
→ Requests inventory content data
```

### **3. Slot Parsing**
For each slot (13, 21, 23, 29, 31):
```
Expected Format:
Slot #13: ItemType
  Island Name (e.g., "Revenants")
  All-Time: $1.78B
  Weekly: $979.33M
  Today: +$50.69M (+5.5%)
```

### **4. Data Storage**
Parsed data is stored in:
- **Current Session**: `currentIslandData` dictionary
- **MCC Variables**: For persistence across sessions
- **Previous Data**: For change comparison

## Output Format

### **Console Report**
```
=== Phase 2 Island Data Report ===
Island Name     | All-Time    | Weekly      | Today's Change
----------------|-------------|-------------|---------------
Revenants       | $1.78B      | $979.33M    | +$50.69M (+5.5%)
NIP             | $973.68M    | $446.74M    | +$18.07M (+4.2%)
FakeDuo         | $856.42M    | $392.15M    | +$12.33M (+3.2%)
IslandName4     | $743.21M    | $301.87M    | +$8.91M (+3.0%)
IslandName5     | $692.58M    | $278.43M    | +$6.74M (+2.5%)
==================================
```

### **Change Detection**
```
[CHANGE] Revenants: Updated from previous run
[NEW] FakeDuo: First time tracking this island
```

## MCC Variable Storage

Phase 2 stores data in MCC variables for access by other phases:

```csharp
// General data
island_count = 5
last_extraction = "2024-01-15 14:30:25"

// Per-island data (index 0-4)
island_0_name = "Revenants"
island_0_alltime = "$1.78B"
island_0_weekly = "$979.33M"
island_0_today = "+$50.69M (+5.5%)"
island_0_slot = 13

island_1_name = "NIP"
island_1_alltime = "$973.68M"
// ... etc
```

## Error Handling

### **Timeout Scenarios**
- **GUI Doesn't Open**: 15-second timeout, skips Phase 2
- **Data Extraction Hangs**: 10-second timeout, closes inventory
- **GUI Won't Close**: 10-second timeout, assumes closed

### **Parsing Errors**
- **Empty Slots**: Logged and skipped
- **Malformed Data**: Graceful parsing with error logging
- **Missing Fields**: Partial data stored with warnings

### **Recovery Mechanisms**
- **Automatic Inventory Close**: Ensures GUI doesn't stay open
- **State Reset**: Returns to Phase 1 idle state on errors
- **Data Preservation**: Previous data maintained on parsing failures

## Integration with Phase 1

### **Trigger Condition**
Phase 2 only activates when:
1. Phase 1 successfully executes `/istop`
2. Phase 2 is enabled (`phase2Enabled = true`)
3. Server responds with successful `/istop` execution

### **Seamless Flow**
```
Phase 1: Send /istop → Success → Phase 2: Extract Data → Complete → Phase 1: Schedule Next Task
```

## Usage Examples

### **Basic Usage**
```bash
# Load bot with both phases enabled
/script multi-phase-bot
```

### **Phase 2 Only Testing**
```bash
# Test Phase 2 with shorter intervals
/script test-phase2-bot
```

### **Manual Control** (if enabled)
```bash
# Disable Phase 2
/script multi-phase-bot
# Then use manual commands to disable Phase 2
```

## Troubleshooting

### **Phase 2 Not Activating**
1. Check if Phase 2 is enabled: `phase2Enabled = true`
2. Verify Phase 1 successfully executes `/istop`
3. Ensure server opens inventory GUI after `/istop`

### **No Data Extracted**
1. Check if inventory slots 13, 21, 23, 29, 31 contain island data
2. Verify inventory data format matches expected structure
3. Check parsing logs for format mismatches

### **GUI Stays Open**
1. Check for timeout errors in logs
2. Verify `/inventory 1 close` command works on server
3. Manual close: `/inventory 1 close`

### **Data Not Persisting**
1. Check MCC variable storage logs
2. Verify MCC.SetVar() calls are successful
3. Check for variable name conflicts

## Files

- **`multi-phase-bot.cs`** - Main bot with Phase 2 implementation
- **`test-phase2-bot.cs`** - Phase 2 testing script
- **`PHASE2-DOCUMENTATION.md`** - This documentation

## Future Enhancements

### **Potential Phase 3 Integration**
- **Trend Analysis**: Calculate ranking changes over time
- **Alert System**: Notify on significant changes
- **Data Export**: Save historical data to files
- **Comparison Reports**: Compare multiple time periods

### **Data Enhancement**
- **Player Tracking**: Track individual player contributions
- **Historical Charts**: Generate trend visualizations  
- **Prediction Models**: Forecast ranking changes

## Status Monitoring

Use the status command to monitor Phase 2:
```
=== Multi-Phase Bot Status ===
Current Phase: 1
Phase 1 Enabled: True
Phase 2 Enabled: True
Current State: Idle
Next Task: 14:25:30
Islands Tracked: 5
Last Data Extraction: 14:15:30
==============================
```

Phase 2 provides comprehensive island ranking monitoring that seamlessly integrates with the existing Phase 1 functionality, enabling powerful multi-phase automation workflows.
