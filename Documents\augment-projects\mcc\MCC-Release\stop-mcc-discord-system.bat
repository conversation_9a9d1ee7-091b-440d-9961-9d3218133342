@echo off
setlocal enabledelayedexpansion

REM ============================================================================
REM MCC Bot + Discord Bridge System Shutdown Script
REM Safely stops both the MCC bot and Discord bridge processes
REM ============================================================================

title MCC Discord System Shutdown

REM Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

echo %CYAN%============================================================================%RESET%
echo %WHITE%                    MCC BOT + DISCORD BRIDGE SHUTDOWN%RESET%
echo %CYAN%============================================================================%RESET%
echo.

echo %YELLOW%[SHUTDOWN]%RESET% Stopping MCC Discord System...
echo.

REM ============================================================================
REM Check for running processes
REM ============================================================================

echo %BLUE%[INFO]%RESET% Checking for running processes...
echo.

REM Check for MCC Bot
tasklist /fi "imagename eq MinecraftClient.exe" 2>nul | find /i "MinecraftClient.exe" >nul
if not errorlevel 1 (
    echo %GREEN%[FOUND]%RESET% MCC Bot (MinecraftClient.exe) is running
    set "MCC_RUNNING=1"
) else (
    echo %YELLOW%[INFO]%RESET% MCC Bot (MinecraftClient.exe) not running
    set "MCC_RUNNING=0"
)

REM Check for Discord Bridge (Python processes)
tasklist /fi "imagename eq python.exe" 2>nul | find /i "python.exe" >nul
if not errorlevel 1 (
    echo %GREEN%[FOUND]%RESET% Python processes running (likely Discord Bridge)
    set "PYTHON_RUNNING=1"
) else (
    echo %YELLOW%[INFO]%RESET% No Python processes detected
    set "PYTHON_RUNNING=0"
)

echo.

REM ============================================================================
REM Stop processes gracefully
REM ============================================================================

if "%MCC_RUNNING%"=="1" (
    echo %YELLOW%[STOPPING]%RESET% Terminating MCC Bot processes...
    taskkill /f /im MinecraftClient.exe >nul 2>&1
    if errorlevel 1 (
        echo %RED%[ERROR]%RESET% Failed to stop MCC Bot
    ) else (
        echo %GREEN%[SUCCESS]%RESET% MCC Bot stopped successfully
    )
    echo.
)

if "%PYTHON_RUNNING%"=="1" (
    echo %YELLOW%[STOPPING]%RESET% Terminating Discord Bridge processes...
    
    REM Try to stop Python processes gracefully first
    for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| find "python.exe"') do (
        set "PID=%%i"
        set "PID=!PID:"=!"
        echo %BLUE%[INFO]%RESET% Stopping Python process PID: !PID!
        taskkill /pid !PID! >nul 2>&1
    )
    
    REM Wait a moment for graceful shutdown
    timeout /t 2 /nobreak >nul
    
    REM Force kill any remaining Python processes
    taskkill /f /im python.exe >nul 2>&1
    
    echo %GREEN%[SUCCESS]%RESET% Discord Bridge processes stopped
    echo.
)

REM ============================================================================
REM Verify shutdown
REM ============================================================================

echo %BLUE%[VERIFY]%RESET% Verifying all processes stopped...
echo.

REM Check MCC Bot
tasklist /fi "imagename eq MinecraftClient.exe" 2>nul | find /i "MinecraftClient.exe" >nul
if not errorlevel 1 (
    echo %RED%[WARNING]%RESET% MCC Bot still running - may need manual termination
) else (
    echo %GREEN%[VERIFIED]%RESET% MCC Bot completely stopped
)

REM Check Python processes
tasklist /fi "imagename eq python.exe" 2>nul | find /i "python.exe" >nul
if not errorlevel 1 (
    echo %YELLOW%[WARNING]%RESET% Python processes still running (may be other applications)
) else (
    echo %GREEN%[VERIFIED]%RESET% No Python processes detected
)

echo.

REM ============================================================================
REM Cleanup temporary files (optional)
REM ============================================================================

echo %BLUE%[CLEANUP]%RESET% Cleaning up temporary files...
echo.

REM Clean up log files older than 7 days (optional)
if exist "*.log" (
    forfiles /m *.log /d -7 /c "cmd /c echo Deleting old log: @file && del @path" 2>nul
)

REM Clean up any crash dumps
if exist "*.dmp" (
    echo %BLUE%[INFO]%RESET% Removing crash dump files...
    del *.dmp >nul 2>&1
)

echo %GREEN%[SUCCESS]%RESET% Cleanup completed
echo.

REM ============================================================================
REM Final status
REM ============================================================================

echo %CYAN%============================================================================%RESET%
echo %WHITE%                           SHUTDOWN COMPLETE%RESET%
echo %CYAN%============================================================================%RESET%
echo.

echo %GREEN%[SUCCESS]%RESET% MCC Discord System shutdown completed
echo.

echo %BLUE%[STATUS]%RESET% All system processes have been terminated
echo %BLUE%[STATUS]%RESET% Temporary files cleaned up
echo %BLUE%[STATUS]%RESET% System ready for restart
echo.

echo %YELLOW%[NOTE]%RESET% To restart the system, run: start-mcc-discord-system.bat
echo.

echo %CYAN%============================================================================%RESET%
echo %WHITE%Press any key to close this window...%RESET%
echo %CYAN%============================================================================%RESET%

pause >nul

exit /b 0
