@echo off
echo ========================================
echo MCC Discord Bridge Bot Startup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install/upgrade dependencies
echo Installing dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

REM Check if config file exists
if not exist "discord-bridge-config.json" (
    echo ERROR: Configuration file 'discord-bridge-config.json' not found
    echo Please ensure the config file is in the same directory as this script
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting Discord Bridge Bot...
echo ========================================
echo Press Ctrl+C to stop the bot
echo.

REM Start the Discord bridge bot
python discord-bridge-bot.py

echo.
echo Discord Bridge Bot stopped.
pause
