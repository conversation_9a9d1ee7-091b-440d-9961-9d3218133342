@echo off
echo ========================================
echo Clear Discord Queue File
echo ========================================
echo.

echo This will clear the corrupted discord_queue.json file
echo and allow the system to start fresh with properly
echo formatted JSON messages.
echo.

if exist "discord_queue.json" (
    echo Found existing discord_queue.json file
    echo File size: 
    dir discord_queue.json | find "discord_queue.json"
    echo.
    
    echo Backing up current file...
    copy discord_queue.json discord_queue_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.json >nul 2>&1
    
    echo Clearing discord_queue.json...
    echo. > discord_queue.json
    
    echo ✅ Queue file cleared successfully
) else (
    echo No discord_queue.json file found - creating empty file...
    echo. > discord_queue.json
    echo ✅ Empty queue file created
)

echo.
echo ========================================
echo Queue file cleared!
echo ========================================
echo.
echo Next steps:
echo 1. Restart the Discord bridge bot: start-discord-bridge-v2.bat
echo 2. Restart the MCC bot: /script multi-phase-bot
echo 3. Wait for the next 10-minute cycle
echo.
echo The system should now work without JSON parsing errors.
echo.
pause
