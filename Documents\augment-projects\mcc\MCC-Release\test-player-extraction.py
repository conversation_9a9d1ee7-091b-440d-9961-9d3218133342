#!/usr/bin/env python3
"""
Test Player Extraction Debug
Runs the MCC bot to test the enhanced player extraction debugging
"""

import subprocess
import time
import os

def run_mcc_bot_test():
    """Run the MCC bot to test player extraction"""
    
    print("🧪 TESTING MCC BOT PLAYER EXTRACTION")
    print("=" * 60)
    
    print("🚀 Starting MCC bot with enhanced debugging...")
    print("📊 This will show:")
    print("   • How many lore lines are processed per category")
    print("   • How many players are actually found")
    print("   • All 10 players (instead of just top 3)")
    print("   • Debug info for each player extraction")
    
    print(f"\n⚠️ IMPORTANT:")
    print(f"   • Make sure you're in the MCC server")
    print(f"   • Open the leaderboard GUI (/lb)")
    print(f"   • The bot will extract data and show enhanced logs")
    print(f"   • Look for 'DEBUG: Found player #X' messages")
    print(f"   • Check if all 10 players are being extracted")
    
    print(f"\n📱 INSTRUCTIONS:")
    print(f"1. Start the MCC bot (it should auto-run)")
    print(f"2. Wait for leaderboard extraction to complete")
    print(f"3. Check console logs for:")
    print(f"   • 'DEBUG: Processed X lore lines, found Y players'")
    print(f"   • 'DEBUG: Found player #1: PlayerName (value)'")
    print(f"   • All players #1-10 should be listed")
    print(f"4. If only 3 players are found, the issue is in lore parsing")
    print(f"5. If 10 players are found, the issue is in Discord bot")
    
    print(f"\n🔍 EXPECTED OUTPUT:")
    print(f"For each category, you should see:")
    print(f"   ✅ Extracted CategoryName leaderboard with 10 players")
    print(f"   DEBUG: Processed 15+ lore lines, found 10 players")
    print(f"   DEBUG: Found player #1: PlayerName (value)")
    print(f"   DEBUG: Found player #2: PlayerName (value)")
    print(f"   ... (continues to #10)")
    print(f"   #1: PlayerName (value) +10 points")
    print(f"   #2: PlayerName (value) +9 points")
    print(f"   ... (continues to #10)")
    
    print(f"\n🎯 DIAGNOSIS:")
    print(f"• If you see 'found 3 players' → Lore data only contains 3 players")
    print(f"• If you see 'found 10 players' → Extraction works, Discord bot issue")
    print(f"• If you see 'found 0 players' → Parsing logic issue")
    
    print(f"\n" + "=" * 60)
    print("🚀 ENHANCED MCC BOT READY FOR TESTING")
    print("Run the MCC bot and check the console output!")

def check_current_directory():
    """Check if we're in the right directory"""
    
    current_dir = os.getcwd()
    expected_files = ["multi-phase-bot.cs", "mcc-discord-bot.py"]
    
    print(f"📁 Current directory: {current_dir}")
    
    missing_files = []
    for file in expected_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️ Missing files: {missing_files}")
        print(f"Make sure you're in the MCC-Release directory")
        return False
    else:
        print(f"✅ All required files found")
        return True

def main():
    """Main test function"""
    
    print("🔧 MCC BOT PLAYER EXTRACTION TEST")
    print("=" * 70)
    
    # Check directory
    if not check_current_directory():
        print("❌ Please navigate to the correct directory first")
        return
    
    # Run test
    run_mcc_bot_test()
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Run the MCC bot (should start automatically)")
    print(f"2. Check console logs for enhanced debugging")
    print(f"3. Verify if all 10 players are being extracted")
    print(f"4. Report back with the debugging output")
    
    print(f"\n🎉 TEST SETUP COMPLETE!")
    print(f"The MCC bot now has enhanced debugging to show player extraction details!")

if __name__ == "__main__":
    main()
