#!/usr/bin/env python3
"""
Trigger debug test by creating a message with control characters
"""

import json
import uuid
from datetime import datetime

def create_test_message_with_control_chars():
    """Create a test message that will trigger the control character error"""
    
    # Create a message with control characters at specific positions
    # to trigger the error at position 91-92
    
    message_data = {
        "id": str(uuid.uuid4()),
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": '{"embeds":[{"title":"FIXED Test Control","description":"Testing FIXED chars"}]}',
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create"
    }
    
    # Write to queue file
    with open("discord_queue.json", "a", encoding='utf-8') as f:
        json_str = json.dumps(message_data, separators=(',', ':'))
        f.write(json_str + "\n")
    
    print(f"✅ Test message created with ID: {message_data['id']}")
    print(f"📊 JSON length: {len(json_str)} characters")
    
    # Check for control characters in the JSON
    control_chars = []
    for i, char in enumerate(json_str):
        if ord(char) < 32 and char not in ['\n', '\r', '\t']:
            control_chars.append((i, char, ord(char)))
    
    if control_chars:
        print(f"⚠️ Found {len(control_chars)} control characters:")
        for pos, char, ord_val in control_chars:
            print(f"   Position {pos}: {repr(char)} (ord: {ord_val})")
    else:
        print("ℹ️ No control characters found in main JSON")
    
    # Check the embed_data_raw specifically
    embed_raw = message_data['embed_data_raw']
    embed_control_chars = []
    for i, char in enumerate(embed_raw):
        if ord(char) < 32 and char not in ['\n', '\r', '\t']:
            embed_control_chars.append((i, char, ord(char)))
    
    if embed_control_chars:
        print(f"⚠️ Found {len(embed_control_chars)} control characters in embed_data_raw:")
        for pos, char, ord_val in embed_control_chars:
            print(f"   Position {pos}: {repr(char)} (ord: {ord_val})")

if __name__ == "__main__":
    print("🔧 Creating test message with control characters...")
    create_test_message_with_control_chars()
    print("✅ Test message added to queue. Monitor Discord bridge for debug output.")
