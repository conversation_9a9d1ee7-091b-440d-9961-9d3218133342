# Discord Button Interaction Setup Guide

## 🎯 Overview

This guide explains how to configure Discord button interactions for the MCC Discord Bridge v2 system. The button interaction system has been completely rebuilt with proper signature verification, CORS support, and enhanced error handling.

## 🔧 What Was Fixed

### ✅ Issues Resolved:
1. **Discord Signature Verification** - Proper Ed25519 signature validation
2. **CORS Headers** - Correct Cross-Origin Resource Sharing configuration  
3. **Server Binding** - Changed from `localhost` to `0.0.0.0` for external access
4. **Response Format** - Proper Discord interaction response structure
5. **Error Handling** - Comprehensive error responses with logging
6. **Ephemeral Responses** - User-specific button feedback

### ✅ New Features:
- Health check endpoint (`/health`)
- CORS preflight handling (`OPTIONS` requests)
- Enhanced logging and debugging
- Multiple button types support
- Graceful error recovery

## 🚀 Quick Start

### Step 1: Install Required Packages
```bash
pip install PyNaCl aiohttp
```

### Step 2: Start the Enhanced Discord Bridge
```bash
python discord-bridge-v2.py
```

### Step 3: Test the System
```bash
python test-button-interactions.py
```

## 🔐 Discord Application Configuration

### Step 1: Get Your Discord Application Details

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Select your application (or create one)
3. Note down these values:
   - **Application ID**
   - **Public Key** (from General Information tab)
   - **Bot Token** (from Bot tab)

### Step 2: Configure Interactions Endpoint

1. In your Discord application settings, go to **General Information**
2. Find **Interactions Endpoint URL**
3. Set it to: `http://YOUR_SERVER_IP:8080/interactions`
   - Replace `YOUR_SERVER_IP` with your actual server's public IP
   - If testing locally, you can use tools like ngrok for tunneling

### Step 3: Update Configuration

Edit `discord-bridge-v2.py` and update:

```python
# Replace this line:
DISCORD_PUBLIC_KEY = "YOUR_DISCORD_PUBLIC_KEY_HERE"

# With your actual public key:
DISCORD_PUBLIC_KEY = "your_actual_public_key_here"
```

## 🌐 Network Configuration

### For Local Testing:
- The server runs on `http://0.0.0.0:8080/interactions`
- Use ngrok or similar for Discord to reach your local server:
  ```bash
  ngrok http 8080
  ```
- Set Discord's Interactions Endpoint to the ngrok URL

### For Production:
- Ensure port 8080 is open in your firewall
- Use your server's public IP address
- Consider using a reverse proxy (nginx) with SSL

## 🧪 Testing the System

### Automated Tests:
```bash
python test-button-interactions.py
```

This tests:
- ✅ Health check endpoint
- ✅ CORS preflight requests  
- ✅ Discord ping interactions
- ✅ Button click interactions

### Manual Testing:
1. Run the Discord bridge: `python discord-bridge-v2.py`
2. Create test messages: `python test-comprehensive-discord-fixes.py`
3. Check Discord channels for messages with buttons
4. Click buttons and verify ephemeral responses appear

## 🔍 Troubleshooting

### "This interaction failed" Errors:

**Possible Causes:**
1. **Interactions Endpoint not configured** in Discord application
2. **Server not accessible** from Discord's servers
3. **Public key mismatch** between Discord app and bridge configuration
4. **Port 8080 blocked** by firewall
5. **CORS issues** with response headers

**Solutions:**
1. Verify Discord application's Interactions Endpoint URL is correct
2. Test server accessibility: `curl http://YOUR_SERVER_IP:8080/health`
3. Double-check public key in discord-bridge-v2.py
4. Open port 8080 in firewall: `sudo ufw allow 8080`
5. Check Discord bridge logs for specific error messages

### Server Not Starting:

**Check for:**
- Port 8080 already in use: `netstat -tulpn | grep 8080`
- Python packages missing: `pip install PyNaCl aiohttp`
- Permissions issues: Run with appropriate user permissions

### Buttons Not Appearing:

**Verify:**
- Message has `components` array in embed data
- Button `custom_id` values are unique
- Message was processed by Discord bridge
- No JSON parsing errors in logs

## 📊 Expected Behavior

### When Working Correctly:

1. **Discord Bridge Logs:**
   ```
   🌐 Interaction server started on http://0.0.0.0:8080/interactions
   🔗 Discord Interactions Endpoint: http://YOUR_SERVER_IP:8080/interactions
   🏥 Health check available at: http://YOUR_SERVER_IP:8080/health
   ```

2. **Button Click Logs:**
   ```
   📥 Discord interaction received - Type: 3
   🔘 Button interaction: leaderboard_slot_4 by TestUser (123456789)
   📊 Leaderboard button response sent for slot 4 to TestUser
   ```

3. **User Experience:**
   - Buttons appear on leaderboard messages
   - Clicking shows ephemeral response (only visible to clicker)
   - Response includes personalized message with username
   - No "This interaction failed" errors

## 🎯 Button Types Supported

### Leaderboard Buttons:
- **Custom ID Pattern:** `leaderboard_slot_*`
- **Response:** Category-specific information
- **Example:** `leaderboard_slot_4` → "Leaderboard Category: Slot 4"

### Test Buttons:
- **Custom ID:** `test_cleaned_data`
- **Response:** System status and feature confirmation
- **Purpose:** Verify button interactions are working

### Generic Test Buttons:
- **Custom ID Pattern:** `test_*`
- **Response:** Generic test confirmation
- **Purpose:** Development and debugging

## 🔧 Advanced Configuration

### Custom Button Handlers:

Add new button types by modifying `handle_button_interaction()`:

```python
elif custom_id.startswith('my_custom_'):
    return await self.handle_my_custom_button(custom_id, username)
```

### Enhanced Logging:

Enable debug logging by setting:
```python
DEBUG_LOGGING_ENABLED = True
```

### Security Considerations:

1. **Always verify signatures** in production
2. **Use HTTPS** for production endpoints
3. **Validate user permissions** before processing interactions
4. **Rate limit** interaction responses if needed

## 📈 Performance Monitoring

### Health Check:
- Endpoint: `http://YOUR_SERVER_IP:8080/health`
- Returns: Server status and timestamp
- Use for monitoring and load balancer health checks

### Metrics to Monitor:
- Interaction response time (should be < 3 seconds)
- Error rate in Discord bridge logs
- Button click success rate
- Server resource usage

## 🎉 Success Indicators

### ✅ System Working Correctly:
- Health check returns 200 OK
- CORS preflight requests succeed
- Discord ping interactions respond correctly
- Button clicks show ephemeral responses
- No "This interaction failed" errors
- Logs show successful interaction processing

### 📱 User Experience:
- Buttons appear on Discord messages
- Clicking buttons shows immediate response
- Responses are personalized with username
- Only the clicking user sees the response (ephemeral)
- Multiple users can click buttons independently

## 🔗 Additional Resources

- [Discord Interactions Documentation](https://discord.com/developers/docs/interactions/receiving-and-responding)
- [Discord Developer Portal](https://discord.com/developers/applications)
- [PyNaCl Documentation](https://pynacl.readthedocs.io/)
- [aiohttp Documentation](https://docs.aiohttp.org/)

---

## 🎯 Quick Checklist

Before going live, ensure:

- [ ] Discord application created and configured
- [ ] Interactions Endpoint URL set in Discord app
- [ ] Public key configured in discord-bridge-v2.py
- [ ] Port 8080 accessible from internet
- [ ] PyNaCl and aiohttp packages installed
- [ ] Discord bridge running without errors
- [ ] Test interactions working correctly
- [ ] Health check endpoint responding
- [ ] Button clicks showing ephemeral responses

**Once all items are checked, your Discord button interaction system is ready for production use!**
