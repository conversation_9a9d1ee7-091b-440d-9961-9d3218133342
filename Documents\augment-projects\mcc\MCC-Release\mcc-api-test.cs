//MCCScript 1.0

/* MCC API Test Script
 * This script demonstrates the correct usage of MCC API methods
 * in both main script context and ChatBot class context
 */

// === MAIN SCRIPT CONTEXT ===
// In main script context, use MCC.MethodName() (static methods)

MCC.LogToConsole("=== MCC API Test Script ===");
MCC.LogToConsole("Testing variable operations in main script context...");

// Test variable operations in main script context
MCC.SetVar("main_script_test", "Hello from main script");
MCC.SetVar("main_script_number", 42);

string mainTestValue = MCC.GetVarAsString("main_script_test");
int mainTestNumber = MCC.GetVarAsInt("main_script_number");

MCC.LogToConsole($"Main script variable test: {mainTestValue}, Number: {mainTestNumber}");

// Load the ChatBot to test instance methods
MCC.LoadBot(new ApiTestBot());

//MCCScript Extensions

/* === CHATBOT CLASS CONTEXT ===
 * In ChatBot class context, use MethodName() (instance methods)
 * Do NOT use MCC.MethodName() inside ChatBot classes
 */

public class ApiTestBot : ChatBot
{
    private int updateCount = 0;
    
    public override void Initialize()
    {
        LogToConsole("=== API Test Bot Initialized ===");
        LogToConsole("Testing variable operations in ChatBot context...");
        
        // Test variable operations in ChatBot context (instance methods)
        SetVar("chatbot_test", "Hello from ChatBot");
        SetVar("chatbot_number", 123);
        SetVar("initialization_time", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        
        string chatbotTestValue = GetVarAsString("chatbot_test");
        int chatbotTestNumber = GetVarAsInt("chatbot_number");
        
        LogToConsole($"ChatBot variable test: {chatbotTestValue}, Number: {chatbotTestNumber}");
        
        // Test reading variables set in main script context
        string mainValue = GetVarAsString("main_script_test");
        int mainNumber = GetVarAsInt("main_script_number");
        
        LogToConsole($"Reading main script variables: {mainValue}, Number: {mainNumber}");
        LogToConsole("=====================================");
    }
    
    public override void Update()
    {
        updateCount++;
        
        // Test variable operations every 50 ticks (approximately 5 seconds)
        if (updateCount % 50 == 0)
        {
            // Update variables using instance methods
            SetVar("update_count", updateCount);
            SetVar("last_update", DateTime.Now.ToString("HH:mm:ss"));
            
            // Read and log current values
            int storedCount = GetVarAsInt("update_count");
            string lastUpdate = GetVarAsString("last_update");
            
            LogDebugToConsole($"Update #{storedCount} at {lastUpdate}");
            
            // Test data persistence
            if (updateCount == 100)
            {
                LogToConsole("=== Variable Persistence Test ===");
                LogToConsole("Variables set in this session:");
                LogToConsole($"  chatbot_test: {GetVarAsString("chatbot_test")}");
                LogToConsole($"  chatbot_number: {GetVarAsInt("chatbot_number")}");
                LogToConsole($"  main_script_test: {GetVarAsString("main_script_test")}");
                LogToConsole($"  main_script_number: {GetVarAsInt("main_script_number")}");
                LogToConsole($"  initialization_time: {GetVarAsString("initialization_time")}");
                LogToConsole("=================================");
            }
        }
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Test chat message detection
            if (IsChatMessage(cleanText, ref message, ref username))
            {
                // Store chat statistics
                int chatCount = GetVarAsInt("chat_message_count") + 1;
                SetVar("chat_message_count", chatCount);
                SetVar("last_chat_user", username);
                SetVar("last_chat_message", message);
                
                LogDebugToConsole($"Chat #{chatCount} from {username}: {message}");
                
                // Respond to test commands
                if (message.ToLower().Contains("api test"))
                {
                    SendText($"API Test Response: I've processed {chatCount} chat messages!");
                    
                    // Update response statistics
                    int responseCount = GetVarAsInt("response_count") + 1;
                    SetVar("response_count", responseCount);
                }
            }
            else if (IsPrivateMessage(cleanText, ref message, ref username))
            {
                // Store private message statistics
                int pmCount = GetVarAsInt("private_message_count") + 1;
                SetVar("private_message_count", pmCount);
                SetVar("last_pm_user", username);
                
                LogDebugToConsole($"Private message #{pmCount} from {username}");
                
                // Respond to private test commands
                if (message.ToLower().Contains("status"))
                {
                    SendText($"/tell {username} Status: {GetVarAsInt("chat_message_count")} chats, {pmCount} PMs, {GetVarAsInt("response_count")} responses");
                }
            }
            else
            {
                // Server message - test server response tracking
                if (cleanText.Contains("Unknown command"))
                {
                    int unknownCmdCount = GetVarAsInt("unknown_command_count") + 1;
                    SetVar("unknown_command_count", unknownCmdCount);
                    LogDebugToConsole($"Unknown command detected #{unknownCmdCount}");
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in GetText: {ex.Message}");
            
            // Track errors
            int errorCount = GetVarAsInt("error_count") + 1;
            SetVar("error_count", errorCount);
            SetVar("last_error", ex.Message);
        }
    }
    
    // Method to display current statistics
    public void ShowStatistics()
    {
        LogToConsole("=== API Test Bot Statistics ===");
        LogToConsole($"Update Count: {GetVarAsInt("update_count")}");
        LogToConsole($"Chat Messages: {GetVarAsInt("chat_message_count")}");
        LogToConsole($"Private Messages: {GetVarAsInt("private_message_count")}");
        LogToConsole($"Responses Sent: {GetVarAsInt("response_count")}");
        LogToConsole($"Unknown Commands: {GetVarAsInt("unknown_command_count")}");
        LogToConsole($"Errors: {GetVarAsInt("error_count")}");
        LogToConsole($"Last Update: {GetVarAsString("last_update")}");
        LogToConsole($"Last Chat User: {GetVarAsString("last_chat_user")}");
        LogToConsole($"Initialization: {GetVarAsString("initialization_time")}");
        LogToConsole("===============================");
    }
}
