//MCCScript 1.0

/* Command Handler Bot
 * Handles custom commands from users */

MCC.LoadBot(new CommandHandler());

//MCCScript Extensions

public class CommandHandler : ChatBot
{
    private string commandPrefix = "!";
    
    public override void Initialize()
    {
        LogToConsole("Command Handler Bot loaded!");
        LogToConsole($"Command prefix: {commandPrefix}");
    }
    
    public override void GetText(string text)
    {
        string message = "";
        string username = "";
        text = GetVerbatim(text);
        
        if (IsChatMessage(text, ref message, ref username))
        {
            if (message.StartsWith(commandPrefix))
            {
                HandleCommand(username, message.Substring(1)); // Remove prefix
            }
        }
    }
    
    private void HandleCommand(string username, string command)
    {
        string[] parts = command.Split(' ');
        string cmd = parts[0].ToLower();
        
        switch (cmd)
        {
            case "help":
                SendText($"{username}, Available commands: !help, !time, !ping, !say <message>");
                break;
                
            case "time":
                SendText($"{username}, Current time: {DateTime.Now:HH:mm:ss}");
                break;
                
            case "ping":
                SendText($"Pong, {username}!");
                break;
                
            case "say":
                if (parts.Length > 1)
                {
                    string messageToSay = string.Join(" ", parts.Skip(1));
                    SendText($"[{username} says]: {messageToSay}");
                }
                else
                {
                    SendText($"{username}, Usage: !say <message>");
                }
                break;
                
            case "roll":
                Random rand = new Random();
                int roll = rand.Next(1, 7); // 1-6 dice roll
                SendText($"{username} rolled a {roll}!");
                break;
                
            default:
                SendText($"{username}, Unknown command. Use !help for available commands.");
                break;
        }
    }
}
