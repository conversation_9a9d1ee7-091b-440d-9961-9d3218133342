//MCCScript 1.0

/* Compilation Test Script
 * This script tests the corrected variable declaration syntax
 * to ensure the multi-phase bot will compile correctly
 */

MCC.LoadBot(new CompileTestBot());

//MCCScript Extensions

public class CompileTestBot : ChatBot
{
    private int tickCount = 0;

    public override void Initialize()
    {
        LogToConsole("=== Compilation Test Bot Loaded ===");
        LogToConsole("Testing corrected variable declaration syntax...");
        LogToConsole("If you see this message, the script compiled successfully!");
        LogToConsole("=====================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Test the corrected syntax - this should compile without errors
            if (IsChatMessage(cleanText, ref message, ref username))
            {
                LogDebugToConsole($"Chat message from {username}: {message}");
            }
            else if (IsPrivateMessage(cleanText, ref message, ref username))
            {
                LogDebugToConsole($"Private message from {username}: {message}");
            }
            else
            {
                // This is a server message
                LogDebugToConsole($"Server message: {cleanText}");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error in GetText: {ex.Message}");
        }
    }
    
    public override void Update()
    {
        // Simple update method - just count ticks
        tickCount++;

        // Log every 100 ticks (approximately 10 seconds)
        if (tickCount % 100 == 0)
        {
            LogDebugToConsole($"Bot running - tick count: {tickCount}");

            // Test variable operations (ChatBot instance methods)
            SetVar("test_tick_count", tickCount);
            int storedCount = GetVarAsInt("test_tick_count");
            LogDebugToConsole($"Variable test: stored {storedCount}");
        }
    }
}
