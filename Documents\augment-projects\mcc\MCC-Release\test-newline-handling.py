#!/usr/bin/env python3
"""
Test the newline handling logic to ensure Discord embeds display properly
"""

import json

def simulate_csharp_string_escaping(text):
    """Simulate the C# SimpleJsonSerializer string escaping logic"""
    
    # Step 1: Escape backslashes first
    text = text.replace("\\", "\\\\")
    
    # Step 2: Escape quotes
    text = text.replace('"', '\\"')
    
    # Step 3: Escape carriage returns and tabs
    text = text.replace("\r", "\\r")
    text = text.replace("\t", "\\t")
    
    # Step 4: Special newline handling
    if "\\\\n" in text:
        # This is a Discord embed field with intentional \\n formatting
        text = text.replace("\\\\n", "\\n")  # Convert \\n to \n for Discord
    else:
        # Regular string with actual newlines - escape them
        text = text.replace("\n", "\\n")
    
    return f'"{text}"'

def test_newline_handling():
    """Test different newline scenarios"""
    
    print("🔧 Testing Newline Handling Logic")
    print("=" * 50)
    
    # Test 1: Island embed field with \\n (double backslash) - Discord format
    print("\n📊 Test 1: Island Embed Field (Discord format with \\\\n)")
    print("-" * 50)
    
    island_field = "**All Time:** $1.04B\\n**Weekly:** $242.3M\\n**Growth:** Insufficient historical data"
    print(f"Input: {repr(island_field)}")
    
    result1 = simulate_csharp_string_escaping(island_field)
    print(f"Output: {result1}")
    
    # Parse the result to see what Discord would receive
    try:
        parsed1 = json.loads(result1)
        print(f"Discord receives: {repr(parsed1)}")
        
        # Check if it contains single \n for Discord line breaks
        if "\\n" in parsed1 and "\\\\n" not in parsed1:
            print("✅ Correct: Contains \\n for Discord line breaks")
        else:
            print("❌ Incorrect: Does not contain proper Discord line breaks")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
    
    # Test 2: Regular string with actual newlines
    print("\n📝 Test 2: Regular String (actual newlines)")
    print("-" * 50)
    
    regular_string = "Line 1\nLine 2\nLine 3"
    print(f"Input: {repr(regular_string)}")
    
    result2 = simulate_csharp_string_escaping(regular_string)
    print(f"Output: {result2}")
    
    try:
        parsed2 = json.loads(result2)
        print(f"Parsed: {repr(parsed2)}")
        
        # Check if actual newlines are properly escaped
        if "\\n" in parsed2 and "\n" not in parsed2:
            print("✅ Correct: Actual newlines properly escaped")
        else:
            print("❌ Incorrect: Actual newlines not properly escaped")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
    
    # Test 3: Complete island embed structure
    print("\n🏝️ Test 3: Complete Island Embed Structure")
    print("-" * 50)
    
    island_embed = {
        "embeds": [{
            "title": "🏝️ Top Islands Data",
            "description": "Island rankings extracted at 23:22:30",
            "color": 39423,
            "fields": [
                {
                    "name": "🏝️ #1: Revenants (10 points)",
                    "value": "**All Time:** $1.04B\\n**Weekly:** $242.3M\\n**Growth:** Insufficient historical data"
                },
                {
                    "name": "🏝️ #2: NIP (9 points)",
                    "value": "**All Time:** $670.37M\\n**Weekly:** $143.43M\\n**Growth:** Insufficient historical data"
                }
            ]
        }]
    }
    
    # Serialize normally (this is what Python/Discord would expect)
    normal_json = json.dumps(island_embed, separators=(',', ':'))
    print("Normal JSON serialization:")
    print(normal_json[:200] + "..." if len(normal_json) > 200 else normal_json)
    
    # Check what the field values look like
    parsed_embed = json.loads(normal_json)
    field_value = parsed_embed['embeds'][0]['fields'][0]['value']
    print(f"\nField value in JSON: {repr(field_value)}")
    
    if "\\n" in field_value:
        print("✅ Field contains \\n - Discord will render as line breaks")
    else:
        print("❌ Field missing \\n - Discord will show as single line")
    
    # Test 4: What happens if we have escaped newlines in the original
    print("\n⚠️ Test 4: Pre-escaped Newlines (problematic case)")
    print("-" * 50)
    
    problematic_field = "**All Time:** $1.04B\\\\n**Weekly:** $242.3M\\\\n**Growth:** Insufficient historical data"
    print(f"Input with double-escaped newlines: {repr(problematic_field)}")
    
    result4 = simulate_csharp_string_escaping(problematic_field)
    print(f"After C# escaping: {result4}")
    
    try:
        parsed4 = json.loads(result4)
        print(f"Final result: {repr(parsed4)}")
        
        if "\\n" in parsed4 and "\\\\n" not in parsed4:
            print("✅ Fixed: Double-escaped newlines converted to single \\n")
        else:
            print("❌ Problem: Still contains double-escaped newlines")
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing failed: {e}")
    
    return True

def test_discord_rendering():
    """Test how Discord would render the different formats"""
    
    print("\n🎨 Discord Rendering Test")
    print("=" * 50)
    
    formats = [
        ("Correct format", "**All Time:** $1.04B\n**Weekly:** $242.3M\n**Growth:** Data"),
        ("Escaped format", "**All Time:** $1.04B\\n**Weekly:** $242.3M\\n**Growth:** Data"),
        ("Double-escaped", "**All Time:** $1.04B\\\\n**Weekly:** $242.3M\\\\n**Growth:** Data")
    ]
    
    for name, text in formats:
        print(f"\n{name}:")
        print(f"Raw: {repr(text)}")
        print("Discord would display:")
        print(text.replace('\\n', '\n').replace('\\\\n', '\\n'))
        print("-" * 30)

if __name__ == "__main__":
    print("🔧 Newline Handling Test for Island Embeds")
    print("=" * 60)
    
    success = test_newline_handling()
    test_discord_rendering()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    print("✅ The C# escaping logic should convert \\\\n to \\n for Discord")
    print("✅ Regular newlines should be escaped to \\n")
    print("✅ Discord will render \\n as actual line breaks")
    print("❌ If Discord shows literal \\n text, the escaping needs adjustment")
