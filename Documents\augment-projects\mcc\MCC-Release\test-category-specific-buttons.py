#!/usr/bin/env python3
"""
Test Category-Specific Button Data
Creates test messages with real category-specific leaderboard data
"""

import json
import uuid
from datetime import datetime

def create_category_specific_test_message():
    """Create a test message with real category-specific leaderboard data"""
    
    message_id = f"category-test-{str(uuid.uuid4())[:8]}"
    
    # Create realistic category-specific data
    leaderboard_categories = {
        "slot_31": {
            "slot": 31,
            "category_name": "XP",
            "players": [
                {"rank": 1, "name": "XPMaster", "value": "2,450,000 XP - 50 GC"},
                {"rank": 2, "name": "LevelGrinder", "value": "2,200,000 XP - 40 GC"},
                {"rank": 3, "name": "ExperienceKing", "value": "1,980,000 XP - 35 GC"},
                {"rank": 4, "name": "XPFarmer", "value": "1,750,000 XP - 30 GC"},
                {"rank": 5, "name": "LevelUp", "value": "1,650,000 XP - 25 GC"},
                {"rank": 6, "name": "XPHunter", "value": "1,500,000 XP - 20 GC"},
                {"rank": 7, "name": "ExpGainer", "value": "1,400,000 XP - 15 GC"},
                {"rank": 8, "name": "XPCollector", "value": "1,300,000 XP - 10 GC"},
                {"rank": 9, "name": "LevelSeeker", "value": "1,200,000 XP - 10 GC"},
                {"rank": 10, "name": "XPAddict", "value": "1,100,000 XP - 5 GC"},
                {"rank": 11, "name": "ExpChaser", "value": "1,000,000 XP - 5 GC"},
                {"rank": 12, "name": "XPGrinder", "value": "950,000 XP - 5 GC"}
            ],
            "extracted_at": datetime.now().isoformat() + "Z"
        },
        "slot_9": {
            "slot": 9,
            "category_name": "Mobs",
            "players": [
                {"rank": 1, "name": "MobSlayer", "value": "15,420 kills - 50 GC"},
                {"rank": 2, "name": "MonsterHunter", "value": "14,850 kills - 40 GC"},
                {"rank": 3, "name": "CreatureKiller", "value": "13,990 kills - 35 GC"},
                {"rank": 4, "name": "BeastDestroyer", "value": "12,750 kills - 30 GC"},
                {"rank": 5, "name": "MobFarmer", "value": "11,600 kills - 25 GC"},
                {"rank": 6, "name": "KillStreak", "value": "10,800 kills - 20 GC"},
                {"rank": 7, "name": "MobCrusher", "value": "9,950 kills - 15 GC"},
                {"rank": 8, "name": "EntityEliminator", "value": "9,200 kills - 10 GC"},
                {"rank": 9, "name": "MobMaster", "value": "8,750 kills - 10 GC"},
                {"rank": 10, "name": "CreatureCrusher", "value": "8,300 kills - 5 GC"},
                {"rank": 11, "name": "MobTerminator", "value": "7,900 kills - 5 GC"},
                {"rank": 12, "name": "BeastBane", "value": "7,500 kills - 5 GC"}
            ],
            "extracted_at": datetime.now().isoformat() + "Z"
        },
        "slot_15": {
            "slot": 15,
            "category_name": "Blocks",
            "players": [
                {"rank": 1, "name": "BlockBreaker", "value": "89,500 blocks - 50 GC"},
                {"rank": 2, "name": "MineDigger", "value": "85,200 blocks - 40 GC"},
                {"rank": 3, "name": "StoneDestroyer", "value": "82,100 blocks - 35 GC"},
                {"rank": 4, "name": "BlockMaster", "value": "78,900 blocks - 30 GC"},
                {"rank": 5, "name": "DiggerPro", "value": "75,600 blocks - 25 GC"},
                {"rank": 6, "name": "MiningKing", "value": "72,300 blocks - 20 GC"},
                {"rank": 7, "name": "BlockCrusher", "value": "69,800 blocks - 15 GC"},
                {"rank": 8, "name": "StoneBreaker", "value": "66,500 blocks - 10 GC"},
                {"rank": 9, "name": "MineExpert", "value": "63,200 blocks - 10 GC"},
                {"rank": 10, "name": "BlockHunter", "value": "60,100 blocks - 5 GC"},
                {"rank": 11, "name": "DigMaster", "value": "57,800 blocks - 5 GC"},
                {"rank": 12, "name": "StoneSlayer", "value": "55,400 blocks - 5 GC"}
            ],
            "extracted_at": datetime.now().isoformat() + "Z"
        },
        "slot_33": {
            "slot": 33,
            "category_name": "PvP",
            "players": [
                {"rank": 1, "name": "PvPChampion", "value": "1,250 kills - 50 GC"},
                {"rank": 2, "name": "WarriorKing", "value": "1,180 kills - 40 GC"},
                {"rank": 3, "name": "BattleMaster", "value": "1,090 kills - 35 GC"},
                {"rank": 4, "name": "CombatPro", "value": "980 kills - 30 GC"},
                {"rank": 5, "name": "FightClub", "value": "890 kills - 25 GC"},
                {"rank": 6, "name": "PvPLegend", "value": "820 kills - 20 GC"},
                {"rank": 7, "name": "DuelMaster", "value": "750 kills - 15 GC"},
                {"rank": 8, "name": "ArenaKing", "value": "680 kills - 10 GC"},
                {"rank": 9, "name": "BattleExpert", "value": "620 kills - 10 GC"},
                {"rank": 10, "name": "WarVeteran", "value": "570 kills - 5 GC"},
                {"rank": 11, "name": "CombatElite", "value": "520 kills - 5 GC"},
                {"rank": 12, "name": "PvPWarrior", "value": "480 kills - 5 GC"}
            ],
            "extracted_at": datetime.now().isoformat() + "Z"
        },
        "slot_27": {
            "slot": 27,
            "category_name": "Quests",
            "players": [
                {"rank": 1, "name": "QuestMaster", "value": "485 quests - 50 GC"},
                {"rank": 2, "name": "TaskCompleter", "value": "460 quests - 40 GC"},
                {"rank": 3, "name": "MissionKing", "value": "435 quests - 35 GC"},
                {"rank": 4, "name": "QuestHero", "value": "410 quests - 30 GC"},
                {"rank": 5, "name": "TaskMaster", "value": "385 quests - 25 GC"},
                {"rank": 6, "name": "QuestSeeker", "value": "360 quests - 20 GC"},
                {"rank": 7, "name": "MissionExpert", "value": "335 quests - 15 GC"},
                {"rank": 8, "name": "QuestRunner", "value": "310 quests - 10 GC"},
                {"rank": 9, "name": "TaskHunter", "value": "285 quests - 10 GC"},
                {"rank": 10, "name": "QuestPro", "value": "260 quests - 5 GC"},
                {"rank": 11, "name": "MissionPro", "value": "235 quests - 5 GC"},
                {"rank": 12, "name": "QuestChamp", "value": "210 quests - 5 GC"}
            ],
            "extracted_at": datetime.now().isoformat() + "Z"
        }
    }
    
    # Create the test message with category-specific data
    test_message = {
        "id": message_id,
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 999,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": json.dumps({
            "embeds": [{
                "title": "🏆 Top 12 Players",
                "description": "Category-specific leaderboard test\\n*Click buttons below to view different categories*",
                "color": 16766720,
                "timestamp": datetime.now().isoformat() + "Z",
                "fields": [{
                    "name": "🏆 Top 12 Players:",
                    "value": "#1: XPMaster 2,450,000 XP - 50 GC\\n#2: LevelGrinder 2,200,000 XP - 40 GC\\n#3: ExperienceKing 1,980,000 XP - 35 GC\\n#4: XPFarmer 1,750,000 XP - 30 GC\\n#5: LevelUp 1,650,000 XP - 25 GC\\n#6: XPHunter 1,500,000 XP - 20 GC\\n#7: ExpGainer 1,400,000 XP - 15 GC\\n#8: XPCollector 1,300,000 XP - 10 GC\\n#9: LevelSeeker 1,200,000 XP - 10 GC\\n#10: XPAddict 1,100,000 XP - 5 GC\\n#11: ExpChaser 1,000,000 XP - 5 GC\\n#12: XPGrinder 950,000 XP - 5 GC",
                    "inline": False
                }],
                "footer": {
                    "text": "Category-Specific Test • Real Data • Button Interactions",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                }
            }],
            "components": [{
                "type": 1,
                "components": [
                    {"type": 2, "style": 2, "label": "XP", "custom_id": "leaderboard_slot_31"},
                    {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_slot_9"},
                    {"type": 2, "style": 2, "label": "Blocks", "custom_id": "leaderboard_slot_15"},
                    {"type": 2, "style": 2, "label": "PvP", "custom_id": "leaderboard_slot_33"},
                    {"type": 2, "style": 2, "label": "Quests", "custom_id": "leaderboard_slot_27"}
                ]
            }]
        }, separators=(',', ':')),
        "leaderboard_categories": leaderboard_categories,  # Include the real category data
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create",
        "supports_interactions": True,
        "interaction_handler": "HandleLeaderboardButtonInteraction"
    }
    
    return message_id, test_message

def test_category_data_parsing():
    """Test parsing of category-specific data"""
    
    print("🧪 TESTING CATEGORY-SPECIFIC DATA PARSING")
    print("=" * 60)
    
    # Create test message
    message_id, test_message = create_category_specific_test_message()
    
    # Test data extraction
    leaderboard_categories = test_message.get('leaderboard_categories', {})
    
    print(f"📊 Categories found: {len(leaderboard_categories)}")
    
    for category_key, category_data in leaderboard_categories.items():
        category_name = category_data.get('category_name', 'Unknown')
        players = category_data.get('players', [])
        
        print(f"\n🏆 {category_name} (slot_{category_data.get('slot', '?')}):")
        print(f"   Players: {len(players)}")
        
        # Show first 3 players
        for i, player in enumerate(players[:3]):
            rank = player.get('rank', i+1)
            name = player.get('name', 'Unknown')
            value = player.get('value', 'N/A')
            
            if rank == 1:
                emoji = "🥇"
            elif rank == 2:
                emoji = "🥈"
            elif rank == 3:
                emoji = "🥉"
            else:
                emoji = "🏅"
            
            print(f"   {emoji} #{rank}: {name} - {value}")
        
        if len(players) > 3:
            print(f"   ... and {len(players) - 3} more players")
    
    return message_id, test_message

def main():
    """Run category-specific button tests"""
    
    print("🚀 CATEGORY-SPECIFIC BUTTON DATA TEST")
    print("=" * 70)
    
    # Test 1: Data parsing
    message_id, test_message = test_category_data_parsing()
    
    # Test 2: Write test message to queue
    print(f"\n📝 CREATING CATEGORY-SPECIFIC TEST MESSAGE")
    print("=" * 60)
    
    # Write to queue file
    with open("discord_queue.json", "a", encoding='utf-8') as f:
        f.write(json.dumps(test_message, separators=(',', ':')) + "\n")
    
    print(f"✅ Category-specific test message created: {message_id}")
    print(f"📤 Message added to Discord queue for processing")
    
    # Summary
    print(f"\n" + "=" * 70)
    print("📊 CATEGORY-SPECIFIC TEST SUMMARY:")
    
    categories = test_message.get('leaderboard_categories', {})
    print(f"✅ Categories created: {len(categories)}")
    
    for category_key, category_data in categories.items():
        category_name = category_data.get('category_name', 'Unknown')
        player_count = len(category_data.get('players', []))
        print(f"   • {category_name}: {player_count} players")
    
    print(f"\n🎯 EXPECTED BUTTON BEHAVIOR:")
    print(f"• Clicking 'XP' button → Shows XP leaderboard with XPMaster, LevelGrinder, etc.")
    print(f"• Clicking 'Mobs' button → Shows Mobs leaderboard with MobSlayer, MonsterHunter, etc.")
    print(f"• Clicking 'Blocks' button → Shows Blocks leaderboard with BlockBreaker, MineDigger, etc.")
    print(f"• Clicking 'PvP' button → Shows PvP leaderboard with PvPChampion, WarriorKing, etc.")
    print(f"• Clicking 'Quests' button → Shows Quests leaderboard with QuestMaster, TaskCompleter, etc.")
    
    print(f"\n📱 NEXT STEPS:")
    print(f"1. Run the enhanced Discord bot: python mcc-discord-bot.py")
    print(f"2. Wait for the test message to be processed")
    print(f"3. Click each button in Discord to verify category-specific data")
    print(f"4. Each button should show different players and scores")
    
    print(f"\n🎉 CATEGORY-SPECIFIC DATA TEST COMPLETE!")

if __name__ == "__main__":
    main()
