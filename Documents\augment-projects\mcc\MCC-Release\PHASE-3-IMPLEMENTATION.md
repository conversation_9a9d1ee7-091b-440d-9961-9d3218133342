# Phase 3 Implementation - Leaderboard Data Extraction

## ✅ **Implementation Complete**

The multi-phase bot has been enhanced with Phase 3 functionality that automatically transitions from island data extraction (Phase 2) to leaderboard data extraction. This creates a seamless three-phase bot that extracts both island ranking data (`/istop`) and leaderboard data (`/lb`) with full NBT analysis.

## 🏗️ **Architecture Overview**

### **1. Enhanced Bot States**

#### **New Phase 3 States Added:**
```csharp
private enum BotState
{
    // Existing states...
    ExtractingIslandData,
    WaitingForInventoryClose,
    
    // NEW Phase 3 states
    ClosingIslandInventory,      // Closing /istop inventory
    WaitingForLeaderboardCommand, // Waiting for /lb command response
    WaitingForLeaderboardOpen,   // Waiting for leaderboard inventory to open
    ExtractingLeaderboardData,   // Extracting leaderboard NBT data
    
    Error
}
```

### **2. Phase Management**

#### **Enhanced Phase Control:**
```csharp
// === PHASE MANAGEMENT ===
private int currentPhase = 1;
private bool phase1Enabled = true;
private bool phase2Enabled = true;
private bool phase3Enabled = true;  // NEW: Phase 3 control

// === PHASE 3 DATA STRUCTURES ===
private Dictionary<string, object> leaderboardData = new Dictionary<string, object>();
private DateTime lastLeaderboardExtraction = DateTime.MinValue;
```

## 🔄 **Phase Transition Flow**

### **Automatic Phase Progression:**
```
Phase 1: /istop Command → Phase 2: Island Data Extraction → Phase 3: Leaderboard Data Extraction → Idle
```

### **Phase 2 to Phase 3 Transition:**
```csharp
// Complete Phase 2 and transition to Phase 3
LogToConsole("[Phase 2] *** COMPLETING PHASE 2 ***");
ProcessIslandDataChanges();

// Check if Phase 3 is enabled
if (phase3Enabled)
{
    LogToConsole("[Phase 2] Phase 3 enabled, transitioning to leaderboard extraction...");
    StartPhase3();
}
else
{
    LogToConsole("[Phase 2] Phase 3 disabled, returning to idle");
    ChangeState(BotState.Idle);
    ScheduleNextTask();
}
```

## 🎯 **Phase 3 Implementation Details**

### **1. Phase 3 Initialization**

#### **StartPhase3() Method:**
```csharp
private void StartPhase3()
{
    LogToConsole("=== STARTING PHASE 3: LEADERBOARD EXTRACTION ===");
    LogToConsole("[Phase 3] Closing island inventory and preparing for leaderboard command...");
    
    // Close current inventory and transition to leaderboard command
    ChangeState(BotState.ClosingIslandInventory);
}
```

### **2. Inventory Transition**

#### **HandleClosingIslandInventory():**
- Waits 2 seconds for island inventory to close
- Sends `/lb` command to open leaderboard
- Transitions to `WaitingForLeaderboardCommand` state

#### **HandleWaitingForLeaderboardCommand():**
- Waits 5 seconds for command response
- Transitions to `WaitingForLeaderboardOpen` state

#### **HandleWaitingForLeaderboardOpen():**
- Checks for leaderboard inventory availability
- Detects when leaderboard GUI opens
- 15-second timeout with graceful fallback
- Transitions to `ExtractingLeaderboardData` state

### **3. Leaderboard Data Extraction**

#### **ExtractLeaderboardData() Method:**
```csharp
private void ExtractLeaderboardData()
{
    // Verify inventory handling is enabled
    // Find leaderboard inventory (non-player inventory)
    // Extract NBT data with raw output
    // Complete Phase 3 and return to idle
}
```

#### **ExtractLeaderboardNBTData() Method:**
```csharp
private void ExtractLeaderboardNBTData(Container inventory, int inventoryId)
{
    // Process all items in leaderboard inventory
    // Extract complete raw NBT structure in JSON format
    // Output detailed NBT data for each slot
    // Extract player-specific information
}
```

### **4. Player Data Processing**

#### **ExtractLeaderboardPlayerData() Method:**
```csharp
private void ExtractLeaderboardPlayerData(Dictionary<string, object> nbt, int slot, string displayName)
{
    // Extract player name from display
    // Extract ranking information (#1, #2, etc.)
    // Process lore data for additional information
    // Store structured leaderboard data
}
```

#### **Player Name Extraction:**
- Supports multiple leaderboard formats
- Regex patterns for "#1: PlayerName" format
- Handles "PlayerName (Level 50)" format
- Removes Minecraft formatting codes

#### **Ranking Extraction:**
- Identifies ranking patterns like "#1", "#2"
- Extracts numerical ranking values
- Handles various display formats

## 📊 **Expected Output**

### **Console Logging:**
```
[Phase 2] *** COMPLETING PHASE 2 ***
[Phase 2] Phase 3 enabled, transitioning to leaderboard extraction...
=== STARTING PHASE 3: LEADERBOARD EXTRACTION ===
[Phase 3] Closing island inventory and preparing for leaderboard command...
[Phase 3] Sending /lb command to open leaderboard...
[Phase 3] Waiting for leaderboard inventory to open...
[Phase 3] Leaderboard inventory detected (2 inventories available)
[Phase 3] *** STARTING LEADERBOARD DATA EXTRACTION ***
[Phase 3] Found 2 inventories, analyzing for leaderboard data...
[Phase 3] Using inventory #1 (Type: Generic_9x6) for leaderboard data
[Phase 3] *** EXTRACTING LEADERBOARD NBT DATA FROM INVENTORY #1 ***
[Phase 3] Inventory Type: Generic_9x6
[Phase 3] Inventory Size: 45 items
```

### **Raw NBT Output:**
```
[Phase 3] === LEADERBOARD SLOT 10 ===
[Phase 3] Item Type: PlayerHead
[Phase 3] Display Name: '#1: PlayerName (Level 50)'
[Phase 3] Count: 1
[Phase 3] NBT Data Found: 2 entries
[Phase 3] *** RAW NBT DATA FOR SLOT 10 ***
[Phase 3] Complete NBT JSON:
{
  "SkullOwner": {
    "Name": "PlayerName",
    "Id": [...],
    "Properties": {...}
  },
  "display": {
    "Name": "{\"extra\":[{\"color\":\"#F0C741\",\"text\":\"#1: \"},{\"color\":\"#9B1F2E\",\"text\":\"PlayerName\"}],\"text\":\"\"}",
    "Lore": [
      "{\"extra\":[{\"color\":\"gray\",\"text\":\"Level: 50\"}],\"text\":\"\"}",
      "{\"extra\":[{\"color\":\"green\",\"text\":\"Score: 12,345\"}],\"text\":\"\"}"
    ]
  }
}
[Phase 3] *** END RAW NBT DATA FOR SLOT 10 ***
```

### **Extracted Player Data:**
```
[Phase 3] *** EXTRACTING PLAYER DATA FROM SLOT 10 ***
[Phase 3] Found display compound with 2 keys
[Phase 3] Extracted player name: 'PlayerName'
[Phase 3] Extracted ranking: '#1'
[Phase 3] Extracted 2 lore lines:
[Phase 3] Lore[0]: 'Level: 50'
[Phase 3] Lore[1]: 'Score: 12,345'
[Phase 3] Stored leaderboard data for slot 10
```

### **Phase 3 Completion:**
```
[Phase 3] *** LEADERBOARD EXTRACTION SUMMARY ***
[Phase 3] Total items processed: 45
[Phase 3] Items with NBT data: 15
[Phase 3] *** END LEADERBOARD NBT EXTRACTION ***
[Phase 3] *** COMPLETING PHASE 3 ***
```

## 🔧 **Integration Features**

### **1. State Management Integration**

#### **Enhanced Update() Method:**
```csharp
private void HandlePhase1(DateTime currentTime)
{
    switch (currentState)
    {
        // Existing Phase 1 & 2 states...
        
        // NEW Phase 3 states
        case BotState.ClosingIslandInventory:
            HandleClosingIslandInventory(currentTime);
            break;
            
        case BotState.WaitingForLeaderboardCommand:
            HandleWaitingForLeaderboardCommand(currentTime);
            break;
            
        case BotState.WaitingForLeaderboardOpen:
            HandleWaitingForLeaderboardOpen(currentTime);
            break;
            
        case BotState.ExtractingLeaderboardData:
            HandleExtractingLeaderboardData(currentTime);
            break;
    }
}
```

### **2. Enhanced Initialization**

#### **Updated Initialize() Method:**
```csharp
LogToConsole("=== Multi-Phase Bot Initialized ===");
LogToConsole($"Phase 1 Enabled: {phase1Enabled}");
LogToConsole($"Phase 2 Enabled: {phase2Enabled}");
LogToConsole($"Phase 3 Enabled: {phase3Enabled}");  // NEW
```

### **3. Cleaned Up Logging**

#### **Reduced Verbose Output:**
- Removed excessive `[GetText]` debug logging
- Cleaned up inventory message spam
- Focused logging on essential information
- Maintained important state transition messages

## 🛡️ **Error Handling & Edge Cases**

### **1. Inventory Handling**
- **Disabled inventory handling**: Graceful skip with error message
- **Missing leaderboard inventory**: Timeout and return to idle
- **Inventory detection failure**: 15-second timeout with fallback

### **2. Command Execution**
- **Failed /lb command**: Timeout handling and state recovery
- **Command response delays**: Appropriate wait times and retries
- **Server lag**: Extended timeouts for inventory opening

### **3. Data Extraction**
- **Missing NBT data**: Logs and continues processing
- **Malformed player data**: Error handling with fallback values
- **JSON serialization errors**: Catches and logs errors gracefully

### **4. State Recovery**
- **Timeout handling**: All states have timeout mechanisms
- **Error state recovery**: Returns to idle on any critical error
- **Phase 3 disable**: Can be disabled independently of other phases

## 🧪 **Testing Scenarios**

### **1. Full Three-Phase Execution**
```
Phase 1: /istop → Phase 2: Island Data → Phase 3: Leaderboard Data → Idle
```

### **2. Phase 3 Disabled**
```
Phase 1: /istop → Phase 2: Island Data → Idle (Phase 3 skipped)
```

### **3. Error Recovery**
```
Phase 1: /istop → Phase 2: Island Data → Phase 3: Error → Idle (graceful recovery)
```

### **4. Inventory Issues**
```
Phase 1: /istop → Phase 2: Island Data → Phase 3: No Leaderboard Inventory → Idle
```

## 📁 **Files Modified**

- ✅ **`multi-phase-bot.cs`** - Enhanced with complete Phase 3 functionality
- ✅ **`PHASE-3-IMPLEMENTATION.md`** - This implementation documentation

## 🚀 **Key Benefits**

### **1. Seamless Integration**
- **Automatic transition**: No manual intervention required
- **Maintains existing functionality**: Phases 1 & 2 unchanged
- **Optional execution**: Phase 3 can be enabled/disabled independently

### **2. Comprehensive Data Extraction**
- **Raw NBT output**: Complete JSON structure for analysis
- **Player data parsing**: Structured extraction of player information
- **Dual dataset**: Both island rankings and leaderboard data

### **3. Robust Error Handling**
- **Timeout mechanisms**: Prevents infinite waiting states
- **Graceful fallbacks**: Returns to idle on any error
- **State recovery**: Maintains bot stability

### **4. Clean Output**
- **Reduced logging spam**: Focused on essential information
- **Structured data**: Clear separation of island and leaderboard data
- **Debug-friendly**: Raw NBT output for detailed analysis

## 🎯 **Usage Instructions**

### **Enable/Disable Phase 3:**
```csharp
private bool phase3Enabled = true;  // Set to false to disable Phase 3
```

### **Expected Execution Flow:**
1. **Phase 1**: Sends `/istop` command every 10 minutes
2. **Phase 2**: Extracts island ranking data with historical analysis
3. **Phase 3**: Automatically transitions to `/lb` and extracts leaderboard data
4. **Return to Idle**: Waits for next 10-minute cycle

### **Testing Commands:**
```bash
/script multi-phase-bot
```

The bot will now execute all three phases automatically, providing comprehensive data extraction from both the island rankings (`/istop`) and player leaderboard (`/lb`) systems! 🎯

## ✅ **Implementation Status**

- ✅ **Phase 3 States**: Added 4 new bot states for leaderboard processing
- ✅ **Automatic Transition**: Seamless flow from Phase 2 to Phase 3
- ✅ **Inventory Management**: Proper closing and opening of different GUIs
- ✅ **NBT Extraction**: Complete raw NBT output in JSON format
- ✅ **Player Data Processing**: Structured extraction of player information
- ✅ **Error Handling**: Comprehensive timeout and error recovery
- ✅ **Clean Logging**: Reduced verbose output, focused on essentials
- ✅ **Integration**: Maintains all existing Phase 1 & 2 functionality

The multi-phase bot now provides complete automation for both island ranking and leaderboard data extraction! 🚀
