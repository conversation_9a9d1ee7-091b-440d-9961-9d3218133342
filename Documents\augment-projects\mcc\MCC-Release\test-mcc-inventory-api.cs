//MCCScript 1.0

/* Test MCC Inventory API directly
 * This script tests different ways to access inventory data
 * to understand the actual structure and find alternative methods
 */

MCC.LoadBot(new MCCInventoryAPITestBot());

//MCCScript Extensions

public class MCCInventoryAPITestBot : ChatBot
{
    private bool inventoryHandlingEnabled = false;
    
    public override void Initialize()
    {
        LogToConsole("=== MCC Inventory API Test Bot ===");
        LogToConsole("*** TESTING DIFFERENT MCC INVENTORY ACCESS METHODS ***");
        
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** CRITICAL: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
        }
        
        LogToConsole("This bot will test various MCC inventory API methods");
        LogToConsole("Run /istop to test different inventory access approaches");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            
            if (cleanText.Contains("Inventory") && cleanText.Contains("opened"))
            {
                LogToConsole("*** INVENTORY OPENED - TESTING MCC API METHODS ***");
                
                // Small delay to ensure inventory is loaded
                System.Threading.Thread.Sleep(50);
                
                TestInventoryAPIMethods();
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error in GetText: {ex.Message}");
        }
    }
    
    private void TestInventoryAPIMethods()
    {
        try
        {
            LogToConsole("*** TESTING MCC INVENTORY API METHODS ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("❌ Cannot test - inventory handling disabled");
                return;
            }
            
            // Method 1: GetInventories() - Current approach
            LogToConsole("=== METHOD 1: GetInventories() ===");
            TestGetInventories();
            
            // Method 2: Try to access player inventory directly
            LogToConsole("=== METHOD 2: Player Inventory Access ===");
            TestPlayerInventoryAccess();
            
            // Method 3: Test different timing
            LogToConsole("=== METHOD 3: Delayed Access ===");
            TestDelayedAccess();
            
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error testing inventory API methods: {ex.Message}");
            LogToConsole($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private void TestGetInventories()
    {
        try
        {
            LogToConsole("*** Testing GetInventories() method ***");
            
            var inventories = GetInventories();
            LogToConsole($"GetInventories() returned {inventories.Count} inventories");
            
            if (inventories.Count == 0)
            {
                LogToConsole("❌ No inventories found with GetInventories()");
                return;
            }
            
            foreach (var kvp in inventories)
            {
                var inventoryId = kvp.Key;
                var inventory = kvp.Value;
                
                LogToConsole($"--- INVENTORY #{inventoryId} ---");
                LogToConsole($"Type: {inventory.Type}");
                LogToConsole($"Items: {inventory.Items.Count}");
                
                // Test accessing a specific slot
                if (inventory.Items.ContainsKey(13))
                {
                    var item = inventory.Items[13];
                    LogToConsole($"*** SLOT 13 DETAILED ANALYSIS ***");
                    LogToConsole($"Item Type: {item.Type}");
                    LogToConsole($"Item Count: {item.Count}");
                    LogToConsole($"Display Name: '{item.DisplayName}'");
                    LogToConsole($"NBT null: {item.NBT == null}");
                    
                    if (item.NBT != null)
                    {
                        LogToConsole($"NBT Count: {item.NBT.Count}");
                        LogToConsole($"NBT Type: {item.NBT.GetType().FullName}");
                        
                        // Test NBT structure
                        TestNBTStructure(item.NBT);
                    }
                }
                
                LogToConsole($"--- END INVENTORY #{inventoryId} ---");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in TestGetInventories: {ex.Message}");
        }
    }
    
    private void TestNBTStructure(Dictionary<string, object> nbt)
    {
        try
        {
            LogToConsole("*** DETAILED NBT STRUCTURE ANALYSIS ***");
            
            foreach (var kvp in nbt)
            {
                var key = kvp.Key;
                var value = kvp.Value;
                
                LogToConsole($"NBT['{key}']:");
                LogToConsole($"  Type: {value?.GetType().FullName ?? "null"}");
                LogToConsole($"  Value: {value?.ToString() ?? "null"}");
                
                // Special handling for known types
                if (value is Dictionary<string, object> dict)
                {
                    LogToConsole($"  Dictionary with {dict.Count} entries:");
                    foreach (var subKvp in dict)
                    {
                        LogToConsole($"    [{subKvp.Key}]: {subKvp.Value?.GetType().Name ?? "null"} = {subKvp.Value?.ToString() ?? "null"}");
                        
                        // Special handling for Lore
                        if (subKvp.Key == "Lore" && subKvp.Value is List<object> loreList)
                        {
                            LogToConsole($"    *** LORE LIST FOUND WITH {loreList.Count} ENTRIES ***");
                            for (int i = 0; i < loreList.Count; i++)
                            {
                                var loreItem = loreList[i];
                                LogToConsole($"      Lore[{i}]: Type={loreItem?.GetType().Name ?? "null"}");
                                LogToConsole($"      Lore[{i}]: Value='{loreItem?.ToString() ?? "null"}'");
                                
                                // This is where we should see the actual lore content!
                                if (loreItem != null)
                                {
                                    string loreText = loreItem.ToString();
                                    if (loreText.Contains("$") || loreText.Contains("Value") || loreText.Contains("point"))
                                    {
                                        LogToConsole($"      *** POTENTIAL ISLAND DATA: {loreText} ***");
                                    }
                                }
                            }
                        }
                    }
                }
                else if (value is List<object> list)
                {
                    LogToConsole($"  List with {list.Count} entries:");
                    for (int i = 0; i < Math.Min(list.Count, 5); i++)
                    {
                        LogToConsole($"    [{i}]: {list[i]?.GetType().Name ?? "null"} = {list[i]?.ToString() ?? "null"}");
                    }
                }
                else if (value is Array array)
                {
                    LogToConsole($"  Array with {array.Length} entries:");
                    for (int i = 0; i < Math.Min(array.Length, 5); i++)
                    {
                        var arrayItem = array.GetValue(i);
                        LogToConsole($"    [{i}]: {arrayItem?.GetType().Name ?? "null"} = {arrayItem?.ToString() ?? "null"}");
                    }
                }
            }
            
            LogToConsole("*** END NBT STRUCTURE ANALYSIS ***");
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error analyzing NBT structure: {ex.Message}");
        }
    }
    
    private void TestPlayerInventoryAccess()
    {
        try
        {
            LogToConsole("*** Testing alternative inventory access methods ***");
            
            // Try different approaches to access inventory
            LogToConsole("Attempting alternative inventory access...");
            
            // Note: These are hypothetical - we'd need to check MCC documentation
            // for actual alternative methods
            
            LogToConsole("No alternative methods found - GetInventories() is the primary method");
            
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in TestPlayerInventoryAccess: {ex.Message}");
        }
    }
    
    private void TestDelayedAccess()
    {
        try
        {
            LogToConsole("*** Testing delayed inventory access ***");
            
            // Test accessing inventory after different delays
            int[] delays = { 100, 250, 500, 1000 };
            
            foreach (int delay in delays)
            {
                LogToConsole($"--- Testing {delay}ms delay ---");
                System.Threading.Thread.Sleep(delay);
                
                var inventories = GetInventories();
                LogToConsole($"After {delay}ms: {inventories.Count} inventories");
                
                if (inventories.Count > 0)
                {
                    var firstInventory = inventories.Values.First();
                    LogToConsole($"First inventory has {firstInventory.Items.Count} items");
                    
                    if (firstInventory.Items.ContainsKey(13))
                    {
                        var item = firstInventory.Items[13];
                        LogToConsole($"Slot 13: {item.Type} - '{item.DisplayName}'");
                        LogToConsole($"NBT: {(item.NBT == null ? "null" : $"{item.NBT.Count} entries")}");
                    }
                }
                
                LogToConsole($"--- End {delay}ms test ---");
            }
            
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in TestDelayedAccess: {ex.Message}");
        }
    }
    
    public override void Update()
    {
        // Minimal update
    }
}
