#!/usr/bin/env python3
"""
Discord Bridge Bot v2.0 - Dedicated File Monitoring
Monitors structured Discord embed files generated by MCC multi-phase bot
"""

import asyncio
import json
import time
import aiohttp
from pathlib import Path
from datetime import datetime
import hashlib
import hmac
from aiohttp import web
import nacl.signing
import nacl.encoding
import discord
from discord.ext import commands

# Import the control character debugger
try:
    # Direct import approach
    import importlib.util
    spec = importlib.util.spec_from_file_location("control_character_debug", "control-character-debug.py")
    control_debug_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(control_debug_module)

    debug_json_string = control_debug_module.debug_json_string
    debug_bridge_error = control_debug_module.debug_bridge_error
    debugger = control_debug_module.debugger
    DEBUG_LOGGING_ENABLED = True
    print("🔧 Control character debug logging enabled")
except Exception as e:
    DEBUG_LOGGING_ENABLED = False
    def debug_json_string(*args, **kwargs): pass
    def debug_bridge_error(*args, **kwargs): pass
    print(f"⚠️ Control character debug logging not available: {e}")

# Configuration
DISCORD_BOT_TOKEN = "MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.GGZ14v.N21m2G5muOQwLtB9QqVci4iPFzGI_Y-1quRj6k"
DISCORD_PUBLIC_KEY = "YOUR_DISCORD_PUBLIC_KEY_HERE"  # Replace with your Discord app's public key for interaction verification
DISCORD_API_BASE = "https://discord.com/api/v10"

# File paths
DISCORD_QUEUE_FILE = Path("discord_queue.json")
DISCORD_OUTPUT_FILE = Path("discord_embeds.json")
PROCESSED_MESSAGES_FILE = Path("processed_discord_messages.json")
BRIDGE_LOG_FILE = Path("discord_bridge_v2.log")

# Channel mapping
CHANNEL_MAPPING = {
    "1401451296711507999": "ISTOP",
    "1401451313216094383": "LB"
}

class DiscordBridgeV2:
    def __init__(self):
        self.session = None
        self.processed_messages = self.load_processed_messages()
        self.last_queue_size = 0
        self.message_count = 0
        
    def log(self, message):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        
        # Also write to log file
        try:
            with open(BRIDGE_LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(log_message + "\n")
        except Exception:
            pass  # Don't fail if logging fails
    
    def load_processed_messages(self):
        """Load processed message IDs to prevent duplicates"""
        try:
            if PROCESSED_MESSAGES_FILE.exists():
                with open(PROCESSED_MESSAGES_FILE, 'r') as f:
                    data = json.load(f)
                    return set(data.get('processed', []))
        except Exception as e:
            self.log(f"Could not load processed messages: {e}")
        return set()
    
    def save_processed_messages(self):
        """Save processed message IDs"""
        try:
            with open(PROCESSED_MESSAGES_FILE, 'w') as f:
                json.dump({
                    'processed': list(self.processed_messages),
                    'last_updated': datetime.now().isoformat(),
                    'total_processed': len(self.processed_messages)
                }, f, indent=2)
        except Exception as e:
            self.log(f"Could not save processed messages: {e}")
    
    async def init_session(self):
        """Initialize HTTP session for Discord API"""
        if not self.session:
            headers = {
                'Authorization': f'Bot {DISCORD_BOT_TOKEN}',
                'Content-Type': 'application/json',
                'User-Agent': 'MCC-Discord-Bridge-v2/1.0'
            }
            self.session = aiohttp.ClientSession(headers=headers)
            self.log("Discord API session initialized")
    
    async def close_session(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def process_discord_formatting(self, embed_data):
        """
        Process embed data to fix Discord formatting issues

        Fixes:
        1. Convert escaped newlines (\\n) to actual newlines for Discord
        2. Clean up formatting for better readability
        3. Ensure proper field structure
        """
        try:
            if not isinstance(embed_data, dict):
                return embed_data

            # Process embeds array
            if 'embeds' in embed_data and isinstance(embed_data['embeds'], list):
                for embed in embed_data['embeds']:
                    if isinstance(embed, dict):
                        # Fix description formatting
                        if 'description' in embed and isinstance(embed['description'], str):
                            embed['description'] = embed['description'].replace('\\n', '\n')

                        # Fix field values formatting
                        if 'fields' in embed and isinstance(embed['fields'], list):
                            for field in embed['fields']:
                                if isinstance(field, dict):
                                    # Fix field names
                                    if 'name' in field and isinstance(field['name'], str):
                                        field['name'] = field['name'].replace('\\n', '\n')

                                    # Fix field values - this is where the main formatting issues are
                                    if 'value' in field and isinstance(field['value'], str):
                                        # Convert escaped newlines to actual newlines
                                        field['value'] = field['value'].replace('\\n', '\n')

                                        # Additional formatting improvements for readability
                                        value = field['value']

                                        # For island data: improve formatting
                                        if 'All Time:' in value and 'Weekly:' in value:
                                            # Format island data more cleanly
                                            lines = value.split('\n')
                                            formatted_lines = []
                                            for line in lines:
                                                line = line.strip()
                                                if line:
                                                    # Add proper spacing and formatting
                                                    if line.startswith('**All Time:**'):
                                                        formatted_lines.append(f"💰 {line}")
                                                    elif line.startswith('**Weekly:**'):
                                                        formatted_lines.append(f"📊 {line}")
                                                    elif line.startswith('**Growth:**'):
                                                        formatted_lines.append(f"📈 {line}")
                                                    else:
                                                        formatted_lines.append(line)
                                            field['value'] = '\n'.join(formatted_lines)

                                        # For player data: improve formatting
                                        elif '#1:' in value and 'points' in value:
                                            # Format player rankings more cleanly
                                            lines = value.split('\n')
                                            formatted_lines = []
                                            for line in lines:
                                                line = line.strip()
                                                if line and line.startswith('#'):
                                                    # Add medal emojis for top 3
                                                    if line.startswith('#1:'):
                                                        formatted_lines.append(f"🥇 {line}")
                                                    elif line.startswith('#2:'):
                                                        formatted_lines.append(f"🥈 {line}")
                                                    elif line.startswith('#3:'):
                                                        formatted_lines.append(f"🥉 {line}")
                                                    else:
                                                        formatted_lines.append(f"🏅 {line}")
                                                elif line:
                                                    formatted_lines.append(line)
                                            field['value'] = '\n'.join(formatted_lines)

                        # Fix footer text formatting
                        if 'footer' in embed and isinstance(embed['footer'], dict):
                            if 'text' in embed['footer'] and isinstance(embed['footer']['text'], str):
                                embed['footer']['text'] = embed['footer']['text'].replace('\\n', '\n')

            return embed_data

        except Exception as e:
            self.log(f"⚠️ Error processing Discord formatting: {e}")
            return embed_data  # Return original data if processing fails

    async def send_discord_message(self, channel_id, embed_data):
        """Send message to Discord via API"""
        if not self.session:
            await self.init_session()
        
        url = f"{DISCORD_API_BASE}/channels/{channel_id}/messages"
        channel_name = CHANNEL_MAPPING.get(channel_id, "UNKNOWN")
        
        try:
            async with self.session.post(url, json=embed_data) as response:
                if response.status == 200:
                    result = await response.json()
                    message_id = result.get('id', 'unknown')
                    self.log(f"✅ Successfully sent message {message_id} to {channel_name} channel ({channel_id})")
                    return True, message_id
                    
                elif response.status == 429:  # Rate limited
                    retry_after = float(response.headers.get('Retry-After', 5))
                    self.log(f"⏳ Rate limited, waiting {retry_after}s")
                    await asyncio.sleep(retry_after)
                    return False, None
                    
                else:
                    error_text = await response.text()
                    self.log(f"❌ Discord API error {response.status}: {error_text}")
                    return False, None
                    
        except Exception as e:
            self.log(f"❌ Error sending Discord message: {e}")
            return False, None
    
    def parse_queue_file(self):
        """Parse new messages from queue file"""
        if not DISCORD_QUEUE_FILE.exists():
            return []
        
        try:
            current_size = DISCORD_QUEUE_FILE.stat().st_size
            if current_size <= self.last_queue_size:
                return []  # No new content
            
            # Read all lines and process new ones
            with open(DISCORD_QUEUE_FILE, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Calculate which lines are new
            if self.last_queue_size == 0:
                new_lines = lines
            else:
                # Estimate line position based on file size
                with open(DISCORD_QUEUE_FILE, 'r', encoding='utf-8') as f:
                    f.seek(self.last_queue_size)
                    new_lines = f.readlines()
            
            self.last_queue_size = current_size
            
            # Parse JSON messages
            new_messages = []
            for line_num, line in enumerate(new_lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                try:
                    message_data = json.loads(line)
                    new_messages.append(message_data)
                except json.JSONDecodeError as e:
                    self.log(f"⚠️ Failed to parse JSON on line {line_num}: {e}")
                    continue
            
            return new_messages
            
        except Exception as e:
            self.log(f"❌ Error parsing queue file: {e}")
            return []
    
    async def process_new_messages(self):
        """Process new messages from queue file"""
        new_messages = self.parse_queue_file()
        
        if not new_messages:
            return
        
        self.log(f"📥 Found {len(new_messages)} new Discord messages to process")
        
        for message_data in new_messages:
            try:
                message_id = message_data.get('id')
                if not message_id:
                    self.log("⚠️ Message missing ID, skipping")
                    continue
                
                if message_id in self.processed_messages:
                    self.log(f"⏭️ Message {message_id[:8]}... already processed, skipping")
                    continue
                
                channel_id = message_data.get('channel_id')
                embed_data = message_data.get('embed_data')
                embed_data_raw = message_data.get('embed_data_raw')
                cycle_number = message_data.get('cycle_number', 'unknown')

                # Handle both embed_data and embed_data_raw fields
                if embed_data_raw:
                    # Parse the raw JSON string for leaderboard messages
                    try:
                        # Debug log the raw JSON before parsing
                        if DEBUG_LOGGING_ENABLED:
                            debug_json_string("DISCORD_BRIDGE", embed_data_raw, message_id, "(before parsing)")

                        embed_data = json.loads(embed_data_raw)

                        # CRITICAL FIX: Process escaped newlines for Discord formatting
                        embed_data = self.process_discord_formatting(embed_data)

                        # Log successful parsing
                        if DEBUG_LOGGING_ENABLED:
                            debugger.log_message("DISCORD_BRIDGE", "INFO",
                                f"Successfully parsed embed_data_raw for message {message_id}")
                            debugger.log_message("DISCORD_BRIDGE", "INFO",
                                f"Applied Discord formatting fixes")

                    except json.JSONDecodeError as e:
                        self.log(f"⚠️ Message {message_id[:8]}... invalid embed_data_raw JSON: {e}")

                        # Enhanced debug logging for JSON parsing errors
                        if DEBUG_LOGGING_ENABLED:
                            debug_bridge_error(message_id, embed_data_raw, str(e))

                            # Additional context logging
                            debugger.log_message("DISCORD_BRIDGE", "ERROR",
                                f"JSON parsing failed for message {message_id}")
                            debugger.log_message("DISCORD_BRIDGE", "ERROR",
                                f"  Error details: {e}")
                            debugger.log_message("DISCORD_BRIDGE", "ERROR",
                                f"  JSON length: {len(embed_data_raw)} characters")

                            # Log the problematic JSON (truncated if too long)
                            if len(embed_data_raw) < 500:
                                debugger.log_message("DISCORD_BRIDGE", "ERROR",
                                    f"  Full JSON: {repr(embed_data_raw)}")
                            else:
                                debugger.log_message("DISCORD_BRIDGE", "ERROR",
                                    f"  JSON preview: {repr(embed_data_raw[:200])}...{repr(embed_data_raw[-200:])}")

                        continue

                # CRITICAL FIX: Also process formatting for embed_data format (island data)
                elif embed_data:
                    # Apply formatting fixes to island data (embed_data format)
                    embed_data = self.process_discord_formatting(embed_data)

                    # Log successful processing
                    if DEBUG_LOGGING_ENABLED:
                        debugger.log_message("DISCORD_BRIDGE", "INFO",
                            f"Successfully processed embed_data for message {message_id}")
                        debugger.log_message("DISCORD_BRIDGE", "INFO",
                            f"Applied Discord formatting fixes to island data")

                if not channel_id or not embed_data:
                    self.log(f"⚠️ Message {message_id[:8]}... missing required data, skipping")
                    continue
                
                channel_name = CHANNEL_MAPPING.get(channel_id, "UNKNOWN")
                self.log(f"📤 Sending message {message_id[:8]}... to {channel_name} channel (Cycle #{cycle_number})")
                
                success, discord_message_id = await self.send_discord_message(channel_id, embed_data)
                
                if success:
                    self.processed_messages.add(message_id)
                    self.message_count += 1
                    self.log(f"✅ Message {self.message_count} processed successfully")
                else:
                    self.log(f"❌ Failed to send message {message_id[:8]}...")
                
                # Rate limiting delay
                await asyncio.sleep(1)
                
            except Exception as e:
                self.log(f"❌ Error processing message: {e}")
        
        # Save processed message IDs
        if new_messages:
            self.save_processed_messages()
            self.log(f"💾 Processed message IDs saved ({len(self.processed_messages)} total)")
    
    async def monitor_queue_file(self):
        """Monitor queue file for new messages"""
        self.log(f"👀 Starting to monitor Discord queue file: {DISCORD_QUEUE_FILE}")
        
        if DISCORD_QUEUE_FILE.exists():
            self.last_queue_size = DISCORD_QUEUE_FILE.stat().st_size
            self.log(f"📊 Initial queue file size: {self.last_queue_size} bytes")
        else:
            self.log("📝 Queue file doesn't exist yet, waiting for MCC bot to create it")
        
        while True:
            try:
                await self.process_new_messages()
                await asyncio.sleep(2)  # Check every 2 seconds
                
            except KeyboardInterrupt:
                self.log("🛑 Received interrupt signal, stopping monitor...")
                break
            except Exception as e:
                self.log(f"❌ Error in file monitoring: {e}")
                await asyncio.sleep(5)  # Wait longer on error

    def verify_discord_signature(self, signature, timestamp, body):
        """Verify Discord interaction signature"""
        try:
            # Skip verification if public key not configured
            if DISCORD_PUBLIC_KEY == "YOUR_DISCORD_PUBLIC_KEY_HERE":
                self.log("⚠️ Discord signature verification skipped - public key not configured")
                return True

            # Create the message to verify
            message = timestamp.encode() + body

            # Verify signature using nacl
            verify_key = nacl.signing.VerifyKey(bytes.fromhex(DISCORD_PUBLIC_KEY))
            verify_key.verify(message, bytes.fromhex(signature))

            return True

        except Exception as e:
            self.log(f"❌ Discord signature verification failed: {e}")
            return False

    async def handle_interaction(self, request):
        """Handle Discord button interactions with proper verification and CORS"""
        try:
            # Get headers
            signature = request.headers.get('X-Signature-Ed25519', '')
            timestamp = request.headers.get('X-Signature-Timestamp', '')

            # Read the request body
            body = await request.read()

            # Verify Discord signature
            if not self.verify_discord_signature(signature, timestamp, body):
                self.log("❌ Invalid Discord signature")
                return web.Response(
                    status=401,
                    text="Invalid signature",
                    headers={
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Methods': 'POST, OPTIONS',
                        'Access-Control-Allow-Headers': 'Content-Type, X-Signature-Ed25519, X-Signature-Timestamp'
                    }
                )

            # Parse interaction data
            try:
                interaction_data = json.loads(body.decode('utf-8'))
            except json.JSONDecodeError as e:
                self.log(f"❌ Invalid JSON in interaction request: {e}")
                return web.Response(
                    status=400,
                    text="Invalid JSON",
                    headers={'Access-Control-Allow-Origin': '*'}
                )

            # Log interaction details
            interaction_type = interaction_data.get('type', 0)
            self.log(f"📥 Discord interaction received - Type: {interaction_type}")

            # Handle ping (Discord verification)
            if interaction_type == 1:
                self.log("🏓 Discord ping received - responding with pong")
                return web.json_response(
                    {'type': 1},
                    headers={'Access-Control-Allow-Origin': '*'}
                )

            # Handle button interactions (MESSAGE_COMPONENT)
            if interaction_type == 3:
                return await self.handle_button_interaction(interaction_data)

            # Handle slash commands (APPLICATION_COMMAND)
            if interaction_type == 2:
                return await self.handle_slash_command(interaction_data)

            # Unknown interaction type
            self.log(f"❌ Unknown interaction type: {interaction_type}")
            return web.Response(
                status=400,
                text=f"Unknown interaction type: {interaction_type}",
                headers={'Access-Control-Allow-Origin': '*'}
            )

        except Exception as e:
            self.log(f"❌ Error handling interaction: {e}")
            import traceback
            self.log(f"❌ Traceback: {traceback.format_exc()}")

            # Return proper error response to Discord
            error_response = {
                "type": 4,  # CHANNEL_MESSAGE_WITH_SOURCE
                "data": {
                    "content": "❌ An internal error occurred while processing your request.",
                    "flags": 64  # EPHEMERAL
                }
            }
            return web.json_response(
                error_response,
                headers={'Access-Control-Allow-Origin': '*'}
            )

    async def handle_button_interaction(self, interaction_data):
        """Handle button click interactions with proper response format"""
        try:
            # Extract interaction details
            custom_id = interaction_data.get('data', {}).get('custom_id', '')

            # Get user info (handle both guild and DM interactions)
            user_info = interaction_data.get('member', {}).get('user', {}) or interaction_data.get('user', {})
            user_id = user_info.get('id', 'unknown')
            username = user_info.get('username', 'Unknown User')

            channel_id = interaction_data.get('channel_id', '')
            message_id = interaction_data.get('message', {}).get('id', '')

            self.log(f"🔘 Button interaction: {custom_id} by user {username} ({user_id})")
            self.log(f"📍 Channel: {channel_id}, Message: {message_id}")

            # Handle leaderboard button clicks
            if custom_id.startswith('leaderboard_slot_'):
                slot_number = custom_id.replace('leaderboard_slot_', '')
                return await self.handle_leaderboard_button(slot_number, username)

            # Handle test buttons
            elif custom_id == 'test_cleaned_data':
                return await self.handle_test_button(username)

            # Handle other custom button types
            elif custom_id.startswith('test_'):
                return await self.handle_generic_test_button(custom_id, username)

            else:
                # Unknown button - send a generic response
                self.log(f"❓ Unknown button interaction: {custom_id}")
                response_data = {
                    "type": 4,  # CHANNEL_MESSAGE_WITH_SOURCE
                    "data": {
                        "content": f"🔘 **Button Clicked: `{custom_id}`**\n\n" +
                                  f"👋 Hello {username}!\n" +
                                  f"🔧 This button feature is under development.\n\n" +
                                  f"*Button interactions are working correctly!*",
                        "flags": 64  # EPHEMERAL
                    }
                }
                return web.json_response(
                    response_data,
                    headers={'Access-Control-Allow-Origin': '*'}
                )

        except Exception as e:
            self.log(f"❌ Error handling button interaction: {e}")
            import traceback
            self.log(f"❌ Traceback: {traceback.format_exc()}")

            # Return proper error response
            response_data = {
                "type": 4,  # CHANNEL_MESSAGE_WITH_SOURCE
                "data": {
                    "content": "❌ **Interaction Error**\n\n" +
                              "An error occurred while processing your button click.\n" +
                              "Please try again or contact support if the issue persists.",
                    "flags": 64  # EPHEMERAL
                }
            }
            return web.json_response(
                response_data,
                headers={'Access-Control-Allow-Origin': '*'}
            )

    async def handle_leaderboard_button(self, slot_number, username):
        """Handle leaderboard category button clicks"""
        try:
            # Create enhanced response for leaderboard category
            response_data = {
                "type": 4,  # CHANNEL_MESSAGE_WITH_SOURCE
                "data": {
                    "content": f"🏆 **Leaderboard Category: Slot {slot_number}**\n\n" +
                              f"� Hello {username}!\n" +
                              f"📊 You requested data for leaderboard slot **{slot_number}**\n\n" +
                              f"🔄 **Feature Status:**\n" +
                              f"• ✅ Button interactions working\n" +
                              f"• ✅ Discord formatting active\n" +
                              f"• 🔧 Category-specific data in development\n\n" +
                              f"*This will show detailed player rankings for this category once fully implemented.*",
                    "flags": 64  # EPHEMERAL (only visible to the user who clicked)
                }
            }

            self.log(f"📊 Leaderboard button response sent for slot {slot_number} to {username}")
            return web.json_response(
                response_data,
                headers={'Access-Control-Allow-Origin': '*'}
            )

        except Exception as e:
            self.log(f"❌ Error handling leaderboard button: {e}")
            response_data = {
                "type": 4,
                "data": {
                    "content": "❌ Error loading leaderboard data.",
                    "flags": 64
                }
            }
            return web.json_response(response_data)

    async def handle_test_button(self, interaction_data):
        """Handle test button clicks"""
        response_data = {
            "type": 4,  # CHANNEL_MESSAGE_WITH_SOURCE
            "data": {
                "content": "✅ **Test Button Response**\n\n" +
                          "🔧 Control character cleaning is working correctly!\n" +
                          "📊 All systems operational\n" +
                          "🎯 Button interactions are functioning properly",
                "flags": 64  # EPHEMERAL
            }
        }

        self.log("🧪 Test button interaction handled successfully")
        return web.json_response(response_data)

    async def handle_cors_preflight(self, request):
        """Handle CORS preflight requests"""
        return web.Response(
            status=200,
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, X-Signature-Ed25519, X-Signature-Timestamp',
                'Access-Control-Max-Age': '86400'
            }
        )

    async def start_interaction_server(self, port=8080):
        """Start HTTP server for Discord interactions with proper CORS support"""
        try:
            app = web.Application()

            # Add CORS preflight handler
            app.router.add_options('/interactions', self.handle_cors_preflight)

            # Add main interaction handler
            app.router.add_post('/interactions', self.handle_interaction)

            # Add health check endpoint
            async def health_check(request):
                return web.json_response({
                    'status': 'healthy',
                    'service': 'Discord Bridge v2 Interaction Server',
                    'timestamp': datetime.now().isoformat()
                })
            app.router.add_get('/health', health_check)

            runner = web.AppRunner(app)
            await runner.setup()

            # Bind to all interfaces (0.0.0.0) so Discord can reach it
            site = web.TCPSite(runner, '0.0.0.0', port)
            await site.start()

            self.log(f"🌐 Interaction server started on http://0.0.0.0:{port}/interactions")
            self.log(f"🔗 Discord Interactions Endpoint: http://YOUR_SERVER_IP:{port}/interactions")
            self.log(f"🏥 Health check available at: http://YOUR_SERVER_IP:{port}/health")
            return runner

        except Exception as e:
            self.log(f"❌ Error starting interaction server: {e}")
            import traceback
            self.log(f"❌ Traceback: {traceback.format_exc()}")
            return None

async def main():
    """Main function"""
    print("🚀 Starting MCC Discord Bridge Bot v2.0")
    print("=" * 60)
    print(f"📁 Queue file: {DISCORD_QUEUE_FILE}")
    print(f"📁 Output file: {DISCORD_OUTPUT_FILE}")
    print(f"📁 Log file: {BRIDGE_LOG_FILE}")
    print(f"🎯 Target channels: {', '.join([f'{name}={cid}' for cid, name in CHANNEL_MAPPING.items()])}")
    print("⌨️ Press Ctrl+C to stop")
    print("=" * 60)
    print()
    
    bridge = DiscordBridgeV2()

    try:
        await bridge.init_session()
        bridge.log("🚀 Discord Bridge Bot v2.0 started successfully")
        bridge.log("👀 Monitoring for new Discord messages from MCC bot...")

        # Start interaction server for button handling
        interaction_runner = await bridge.start_interaction_server(8080)

        # Start monitoring queue file
        await bridge.monitor_queue_file()
        
    except KeyboardInterrupt:
        bridge.log("🛑 Received interrupt signal, shutting down...")
    except Exception as e:
        bridge.log(f"💥 Unexpected error: {e}")
    finally:
        await bridge.close_session()
        bridge.log("🔚 Discord Bridge Bot v2.0 stopped")
        print("\n🔚 Discord Bridge Bot v2.0 stopped")

if __name__ == "__main__":
    asyncio.run(main())
