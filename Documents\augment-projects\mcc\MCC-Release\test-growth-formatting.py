#!/usr/bin/env python3
"""
Test Growth Metrics Formatting - Verify the monetary formatting is working correctly
"""

import json
import os
import re

def test_growth_formatting():
    """Test the current discord_queue.json file for properly formatted growth metrics"""
    print("🧪 Testing Growth Metrics Formatting")
    print("=" * 60)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} lines in queue file")
        
        island_messages = 0
        properly_formatted = 0
        poorly_formatted = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # Test JSON parsing
                message_data = json.loads(line)
                
                channel_name = message_data.get('channel_name', 'Unknown')
                
                if channel_name == 'LB':
                    # Check if it's island data
                    embed_data = message_data.get('embed_data', {})
                    embeds = embed_data.get('embeds', [])
                    
                    if embeds and 'Islands' in embeds[0].get('title', ''):
                        island_messages += 1
                        print(f"✅ Line {i}: Island Data Message")
                        
                        # Check growth formatting in fields
                        fields = embeds[0].get('fields', [])
                        for field in fields:
                            value = field.get('value', '')
                            
                            # Look for growth metrics
                            if 'Growth:' in value:
                                growth_line = value.split('Growth:')[1].strip() if 'Growth:' in value else ''
                                print(f"   📈 Growth data: {growth_line}")
                                
                                # Check for proper formatting patterns
                                if check_proper_formatting(growth_line):
                                    properly_formatted += 1
                                    print(f"   ✅ PROPERLY FORMATTED")
                                else:
                                    poorly_formatted += 1
                                    print(f"   ❌ POORLY FORMATTED")
                                    analyze_formatting_issues(growth_line)
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
        
        print()
        print("📊 Formatting Analysis:")
        print(f"   🏝️ Island messages found: {island_messages}")
        print(f"   ✅ Properly formatted growth: {properly_formatted}")
        print(f"   ❌ Poorly formatted growth: {poorly_formatted}")
        
        if properly_formatted > 0:
            print("✅ FORMATTING IMPROVEMENTS WORKING!")
        else:
            print("⚠️ No properly formatted growth metrics found yet")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def check_proper_formatting(growth_text):
    """Check if growth text is properly formatted"""
    if not growth_text or growth_text in ['No historical data available', 'Insufficient historical data', 'No growth data']:
        return True  # These are acceptable status messages
    
    # Look for proper formatting patterns like "+$3.6M", "-$2.1B", "+$250K"
    patterns = [
        r'[+-]\$\d+\.\d+[BMK]',  # +$3.6M, -$2.1B, +$250K
        r'[+-]\$\d+[BMK]',       # +$3M, -$2B, +$250K
        r'[+-]\$\d+',            # +$500, -$100
    ]
    
    for pattern in patterns:
        if re.search(pattern, growth_text):
            return True
    
    return False

def analyze_formatting_issues(growth_text):
    """Analyze what's wrong with the formatting"""
    if not growth_text:
        print("     Issue: Empty growth text")
        return
    
    # Check for raw numbers (old format)
    if re.search(r'\$\d{7,}', growth_text):  # 7+ digits without formatting
        print("     Issue: Raw numbers not formatted (e.g., $3680000 should be $3.6M)")
    
    # Check for double signs
    if '+-' in growth_text or '-+' in growth_text:
        print("     Issue: Double signs found (e.g., +-$2100000)")
    
    # Check for missing units
    if re.search(r'\$\d{4,}(?![BMK])', growth_text):
        print("     Issue: Large numbers missing units (should have M, B, K)")

def test_formatting_examples():
    """Show examples of the formatting improvements"""
    print("📋 Formatting Examples")
    print("=" * 50)

    examples = [
        (3680000, "+$3.7M"),
        (-2100000, "-$2.1M"),
        (1500000000, "+$1.5B"),
        (250000, "+$250.0K"),
        (500, "+$500"),
        (-750, "-$750"),
        (12400000, "+$12.4M"),
        (-45200000, "-$45.2M"),
        (128700000, "+$128.7M")
    ]

    print("Raw Value → Expected Format")
    print("-" * 30)
    for raw, expected in examples:
        print(f"{raw:>12} → {expected}")

    print()

def main():
    print("🔧 Growth Metrics Formatting Test")
    print("=" * 70)
    print()

    test_formatting_examples()
    test_growth_formatting()

    print()
    print("Expected formatting examples:")
    print("✅ Good: 'Growth: Last: +$3.6M • 1h: +$12.4M • 24h: +$45.2M • 3d: +$128.7M'")
    print("✅ Good: 'Growth: Last: -$2.1M • 1h: +$5.8M • 24h: -$15.3M'")
    print("✅ Good: 'Growth: Last: +$250K • 1h: +$1.2M • 24h: +$8.5M'")
    print("❌ Bad:  'Growth: Last: +$3680000 • 1h: +$12400000'")
    print("❌ Bad:  'Growth: Last: +$-2100000'")
    print()
    print("Time periods included:")
    print("• Last: Growth since last snapshot (10 minutes ago)")
    print("• 1h: Growth over the past hour (6 snapshots)")
    print("• 24h: Growth over the past 24 hours (144 snapshots)")
    print("• 3d: Growth over the past 3 days (432 snapshots)")
    print()
    print("If you see poorly formatted values:")
    print("- Restart the MCC bot to apply the formatting fixes")
    print("- Wait for historical data to accumulate for growth calculations")
    print("- Check that WeeklyValueNumeric is being parsed correctly")
    print("- Enable debug logging to see detailed formatting process")

if __name__ == "__main__":
    main()
