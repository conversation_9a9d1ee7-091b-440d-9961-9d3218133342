//MCCScript 1.0

/* Test script for enhanced NBT parsing and island data extraction
 * This script focuses on testing the improved NBT lore parsing
 * and island ranking data extraction
 */

MCC.LoadBot(new EnhancedNBTTestBot());

//MCCScript Extensions

public class EnhancedNBTTestBot : ChatBot
{
    private int messageCount = 0;
    private bool inventoryHandlingEnabled = false;
    private readonly int[] islandDataSlots = { 13, 21, 23, 29, 31 };
    
    public override void Initialize()
    {
        LogToConsole("=== Enhanced NBT Parsing Test Bot ===");
        LogToConsole("*** TESTING IMPROVED ISLAND DATA EXTRACTION ***");
        
        // Check if inventory handling is enabled
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** CRITICAL: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
        }
        
        LogToConsole("This bot will:");
        LogToConsole("1. Focus on inventory #2 for island data");
        LogToConsole("2. Extract detailed NBT lore data from slots 13, 21, 23, 29, 31");
        LogToConsole("3. Parse island rankings and values from lore text");
        LogToConsole("4. Show comprehensive island data extraction");
        LogToConsole("Try running /istop to test the enhanced parsing");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            messageCount++;
            string cleanText = GetVerbatim(text);
            
            // Focus on inventory messages
            if (cleanText.Contains("Inventory"))
            {
                DateTime now = DateTime.Now;
                LogToConsole($"[{now:HH:mm:ss.fff}] *** INVENTORY MESSAGE DETECTED ***");
                LogToConsole($"[{now:HH:mm:ss.fff}] Text: '{cleanText}'");
                
                if (cleanText.Contains("opened"))
                {
                    LogToConsole($"[{now:HH:mm:ss.fff}] *** INVENTORY OPENED - TESTING ENHANCED PARSING ***");
                    HandleInventoryOpened();
                }
            }
            
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error processing message #{messageCount}: {ex.Message}");
        }
    }
    
    private void HandleInventoryOpened()
    {
        try
        {
            LogToConsole("[NBT TEST] *** STARTING ENHANCED NBT PARSING TEST ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("[NBT TEST] ❌ Cannot test - inventory handling disabled");
                return;
            }
            
            var inventories = GetInventories();
            LogToConsole($"[NBT TEST] Found {inventories.Count} open inventories");
            
            // Focus on inventory #2 as specified
            Container targetInventory = null;
            if (inventories.ContainsKey(2))
            {
                targetInventory = inventories[2];
                LogToConsole($"[NBT TEST] *** USING INVENTORY #2 (PREFERRED) ***");
                LogToConsole($"[NBT TEST] Inventory #2 has {targetInventory.Items.Count} items");
            }
            else if (inventories.Count > 0)
            {
                targetInventory = inventories.Values.First();
                var inventoryId = inventories.Keys.First();
                LogToConsole($"[NBT TEST] *** INVENTORY #2 NOT FOUND, USING INVENTORY #{inventoryId} ***");
                LogToConsole($"[NBT TEST] Fallback inventory has {targetInventory.Items.Count} items");
            }
            
            if (targetInventory != null)
            {
                TestEnhancedNBTParsing(targetInventory);
            }
            else
            {
                LogToConsole("[NBT TEST] ❌ No suitable inventory found for testing");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[NBT TEST] ❌ Error in enhanced NBT test: {ex.Message}");
        }
    }
    
    private void TestEnhancedNBTParsing(Container inventory)
    {
        try
        {
            LogToConsole("[NBT TEST] *** TESTING ENHANCED NBT PARSING ON ISLAND DATA SLOTS ***");
            LogToConsole($"[NBT TEST] Target slots: {string.Join(", ", islandDataSlots)}");
            
            int foundItems = 0;
            int successfulParses = 0;
            
            foreach (int slot in islandDataSlots)
            {
                LogToConsole($"[NBT TEST] ========== TESTING SLOT {slot} ==========");
                
                if (inventory.Items.ContainsKey(slot))
                {
                    var item = inventory.Items[slot];
                    foundItems++;
                    
                    LogToConsole($"[NBT TEST] *** FOUND ITEM IN SLOT {slot} ***");
                    LogToConsole($"[NBT TEST] Type: {item.Type}");
                    LogToConsole($"[NBT TEST] Count: {item.Count}");
                    LogToConsole($"[NBT TEST] Display Name: '{item.DisplayName}'");
                    
                    // Test ranking extraction
                    int ranking = ExtractRankingFromDisplayName(item.DisplayName);
                    LogToConsole($"[NBT TEST] Extracted Ranking: #{ranking}");
                    
                    // Test island name extraction
                    string islandName = ExtractIslandNameFromItem(item.DisplayName);
                    LogToConsole($"[NBT TEST] Extracted Island Name: '{islandName}'");
                    
                    // Test NBT parsing
                    if (item.NBT != null && item.NBT.Count > 0)
                    {
                        LogToConsole($"[NBT TEST] *** TESTING NBT PARSING ({item.NBT.Count} NBT entries) ***");
                        
                        // Log all NBT keys
                        foreach (var nbtKey in item.NBT.Keys)
                        {
                            var nbtValue = item.NBT[nbtKey];
                            LogToConsole($"[NBT TEST] NBT Key: '{nbtKey}' = Type: {nbtValue?.GetType().Name ?? "null"}");
                        }
                        
                        // Test enhanced NBT parsing
                        var parsedValues = ParseValuesFromNBT(item.NBT);
                        
                        if (parsedValues.HasValues)
                        {
                            successfulParses++;
                            LogToConsole($"[NBT TEST] ✅ SUCCESSFUL NBT PARSING FOR SLOT {slot}");
                            LogToConsole($"[NBT TEST] All-Time: '{parsedValues.AllTime}'");
                            LogToConsole($"[NBT TEST] Weekly: '{parsedValues.Weekly}'");
                            LogToConsole($"[NBT TEST] Today: '{parsedValues.TodayChange}'");
                        }
                        else
                        {
                            LogToConsole($"[NBT TEST] ❌ NO VALUES EXTRACTED FROM NBT FOR SLOT {slot}");
                        }
                    }
                    else
                    {
                        LogToConsole($"[NBT TEST] ❌ NO NBT DATA AVAILABLE FOR SLOT {slot}");
                    }
                }
                else
                {
                    LogToConsole($"[NBT TEST] ❌ SLOT {slot} IS EMPTY");
                }
                
                LogToConsole($"[NBT TEST] ========== END SLOT {slot} ==========");
            }
            
            LogToConsole("[NBT TEST] *** ENHANCED NBT PARSING TEST COMPLETE ***");
            LogToConsole($"[NBT TEST] Summary:");
            LogToConsole($"[NBT TEST] - Found {foundItems} items in island data slots");
            LogToConsole($"[NBT TEST] - Successfully parsed {successfulParses} items");
            LogToConsole($"[NBT TEST] - Success rate: {(foundItems > 0 ? (successfulParses * 100 / foundItems) : 0)}%");
            
            if (successfulParses < foundItems)
            {
                LogToConsole($"[NBT TEST] *** RECOMMENDATIONS ***");
                LogToConsole($"[NBT TEST] - Check NBT structure for unparsed items");
                LogToConsole($"[NBT TEST] - Verify lore format patterns");
                LogToConsole($"[NBT TEST] - Consider additional parsing methods");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[NBT TEST] ❌ Error in enhanced NBT parsing test: {ex.Message}");
        }
    }
    
    // Helper methods (simplified versions of the main bot methods)
    private int ExtractRankingFromDisplayName(string displayName)
    {
        try
        {
            if (string.IsNullOrEmpty(displayName))
                return 0;
            
            string cleanName = RemoveMinecraftFormatting(displayName);
            
            if (cleanName.StartsWith("#"))
            {
                int colonIndex = cleanName.IndexOf(":");
                if (colonIndex > 1)
                {
                    string rankingText = cleanName.Substring(1, colonIndex - 1).Trim();
                    if (int.TryParse(rankingText, out int ranking))
                    {
                        return ranking;
                    }
                }
            }
            
            return 0;
        }
        catch
        {
            return 0;
        }
    }
    
    private string ExtractIslandNameFromItem(string displayName)
    {
        try
        {
            if (string.IsNullOrEmpty(displayName))
                return "";
            
            string cleanName = RemoveMinecraftFormatting(displayName);
            
            // Parse ranking format like "#1: Revenants (0 points)"
            if (cleanName.Contains("#") && cleanName.Contains(":"))
            {
                int colonIndex = cleanName.IndexOf(":");
                if (colonIndex > 0 && colonIndex < cleanName.Length - 1)
                {
                    string afterColon = cleanName.Substring(colonIndex + 1).Trim();
                    
                    int openParen = afterColon.IndexOf("(");
                    if (openParen > 0)
                    {
                        afterColon = afterColon.Substring(0, openParen).Trim();
                    }
                    
                    return afterColon;
                }
            }
            
            return cleanName;
        }
        catch
        {
            return displayName;
        }
    }
    
    private string RemoveMinecraftFormatting(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "";
        
        string result = text;
        while (result.Contains("§") && result.Length > result.IndexOf("§") + 1)
        {
            int formatIndex = result.IndexOf("§");
            if (formatIndex < result.Length - 1)
            {
                result = result.Remove(formatIndex, 2);
            }
            else
            {
                result = result.Remove(formatIndex, 1);
            }
        }
        return result;
    }
    
    // Placeholder for NBT parsing (would use the actual implementation)
    private NBTParseResult ParseValuesFromNBT(Dictionary<string, object> nbt)
    {
        // This would use the actual enhanced NBT parsing logic from the main bot
        // For testing purposes, we'll just check if NBT contains expected keys
        var result = new NBTParseResult();
        
        if (nbt.ContainsKey("display"))
        {
            result.AllTime = "Test Value";
        }
        
        return result;
    }
    
    public override void Update()
    {
        // Minimal update for testing
    }
}

public class NBTParseResult
{
    public string AllTime { get; set; }
    public string Weekly { get; set; }
    public string TodayChange { get; set; }
    
    public bool HasValues => !string.IsNullOrEmpty(AllTime) || !string.IsNullOrEmpty(Weekly) || !string.IsNullOrEmpty(TodayChange);
}
