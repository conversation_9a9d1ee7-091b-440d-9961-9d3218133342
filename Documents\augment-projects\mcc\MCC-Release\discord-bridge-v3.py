#!/usr/bin/env python3
"""
Discord Bridge Bot v3.0 - Enhanced with proper Discord bot integration
Handles both message sending and button interactions with persistent connection
"""

import asyncio
import json
import time
import aiohttp
from pathlib import Path
from datetime import datetime
import discord
from discord.ext import commands

# Configuration
DISCORD_BOT_TOKEN = "YOUR_BOT_TOKEN_HERE"  # Replace with actual bot token
DISCORD_QUEUE_FILE = Path("discord_queue.json")
DISCORD_OUTPUT_FILE = Path("discord_embeds.json")
PROCESSED_IDS_FILE = Path("processed_message_ids.json")

# Channel IDs
CHANNEL_IDS = {
    "ISTOP": 1401451296711507999,
    "LB": 1401451313216094383
}

# Debug logging
DEBUG_LOGGING_ENABLED = True

class EnhancedDiscordBot(commands.Bot):
    """Enhanced Discord bot with message sending and interaction handling"""
    
    def __init__(self):
        # Set up bot intents
        intents = discord.Intents.default()
        intents.message_content = True
        
        super().__init__(command_prefix='!', intents=intents)
        
        # Initialize state
        self.processed_message_ids = set()
        self.last_queue_size = 0
        self.leaderboard_data = {}  # Store leaderboard data for button interactions
        
        # Load processed message IDs
        self.load_processed_ids()
    
    async def on_ready(self):
        """Called when bot is ready"""
        self.log(f"🤖 {self.user} has connected to Discord!")
        self.log(f"🔗 Bot is in {len(self.guilds)} guilds")
        
        # Start monitoring queue file
        self.loop.create_task(self.monitor_queue_file())
    
    def log(self, message):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
    
    def load_processed_ids(self):
        """Load processed message IDs from file"""
        try:
            if PROCESSED_IDS_FILE.exists():
                with open(PROCESSED_IDS_FILE, 'r') as f:
                    data = json.load(f)
                    self.processed_message_ids = set(data.get('processed_ids', []))
                    self.log(f"📊 Loaded {len(self.processed_message_ids)} processed message IDs")
        except Exception as e:
            self.log(f"⚠️ Error loading processed IDs: {e}")
            self.processed_message_ids = set()
    
    def save_processed_ids(self):
        """Save processed message IDs to file"""
        try:
            data = {
                'processed_ids': list(self.processed_message_ids),
                'last_updated': datetime.now().isoformat()
            }
            with open(PROCESSED_IDS_FILE, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            self.log(f"⚠️ Error saving processed IDs: {e}")
    
    def process_discord_formatting(self, embed_data):
        """Process embed data to fix Discord formatting issues"""
        try:
            if not isinstance(embed_data, dict):
                return embed_data
            
            # Process embeds array
            if 'embeds' in embed_data and isinstance(embed_data['embeds'], list):
                for embed in embed_data['embeds']:
                    if isinstance(embed, dict):
                        # Fix description formatting
                        if 'description' in embed and isinstance(embed['description'], str):
                            embed['description'] = embed['description'].replace('\\n', '\n')
                        
                        # Fix field values formatting
                        if 'fields' in embed and isinstance(embed['fields'], list):
                            for field in embed['fields']:
                                if isinstance(field, dict):
                                    # Fix field names
                                    if 'name' in field and isinstance(field['name'], str):
                                        field['name'] = field['name'].replace('\\n', '\n')
                                    
                                    # Fix field values - this is where the main formatting issues are
                                    if 'value' in field and isinstance(field['value'], str):
                                        # Convert escaped newlines to actual newlines
                                        field['value'] = field['value'].replace('\\n', '\n')
                                        
                                        # Additional formatting improvements for readability
                                        value = field['value']
                                        
                                        # For island data: improve formatting
                                        if '**All Time:**' in value and '**Weekly:**' in value:
                                            # Format island data more cleanly
                                            lines = value.split('\n')
                                            formatted_lines = []
                                            for line in lines:
                                                line = line.strip()
                                                if line:
                                                    # Add proper spacing and formatting
                                                    if line.startswith('**All Time:**'):
                                                        formatted_lines.append(f"💰 {line}")
                                                    elif line.startswith('**Weekly:**'):
                                                        formatted_lines.append(f"📊 {line}")
                                                    elif line.startswith('**Growth:**'):
                                                        formatted_lines.append(f"📈 {line}")
                                                    else:
                                                        formatted_lines.append(line)
                                            field['value'] = '\n'.join(formatted_lines)
                                        
                                        # For player data: improve formatting
                                        elif '#1:' in value and 'points' in value:
                                            # Format player rankings more cleanly
                                            lines = value.split('\n')
                                            formatted_lines = []
                                            for line in lines:
                                                line = line.strip()
                                                if line and line.startswith('#'):
                                                    # Add medal emojis for top 3
                                                    if line.startswith('#1:'):
                                                        formatted_lines.append(f"🥇 {line}")
                                                    elif line.startswith('#2:'):
                                                        formatted_lines.append(f"🥈 {line}")
                                                    elif line.startswith('#3:'):
                                                        formatted_lines.append(f"🥉 {line}")
                                                    else:
                                                        formatted_lines.append(f"🏅 {line}")
                                                elif line:
                                                    formatted_lines.append(line)
                                            field['value'] = '\n'.join(formatted_lines)
                        
                        # Fix footer text formatting
                        if 'footer' in embed and isinstance(embed['footer'], dict):
                            if 'text' in embed['footer'] and isinstance(embed['footer']['text'], str):
                                embed['footer']['text'] = embed['footer']['text'].replace('\\n', '\n')
            
            return embed_data
            
        except Exception as e:
            self.log(f"⚠️ Error processing Discord formatting: {e}")
            return embed_data  # Return original data if processing fails
    
    async def send_discord_message(self, channel_id, embed_data, message_data=None):
        """Send message to Discord channel"""
        try:
            channel = self.get_channel(channel_id)
            if not channel:
                self.log(f"❌ Channel {channel_id} not found")
                return False, None
            
            # Create Discord embed from embed_data
            discord_embed = discord.Embed.from_dict(embed_data['embeds'][0])
            
            # Create view with buttons if components exist
            view = None
            if 'components' in embed_data:
                view = self.create_button_view(embed_data['components'], message_data)
            
            # Send message
            message = await channel.send(embed=discord_embed, view=view)
            
            self.log(f"✅ Successfully sent message {message.id} to {channel.name} channel")
            return True, str(message.id)
            
        except Exception as e:
            self.log(f"❌ Error sending Discord message: {e}")
            return False, None
    
    def create_button_view(self, components, message_data):
        """Create Discord view with buttons from components data"""
        try:
            view = discord.ui.View(timeout=None)  # Persistent view
            
            for component in components:
                if component.get('type') == 1:  # Action row
                    for button_data in component.get('components', []):
                        if button_data.get('type') == 2:  # Button
                            button = LeaderboardButton(
                                label=button_data.get('label', 'Button'),
                                custom_id=button_data.get('custom_id', 'unknown'),
                                style=discord.ButtonStyle.secondary,
                                bot=self,
                                message_data=message_data
                            )
                            view.add_item(button)
            
            return view
            
        except Exception as e:
            self.log(f"⚠️ Error creating button view: {e}")
            return None
    
    async def monitor_queue_file(self):
        """Monitor queue file for new messages"""
        self.log("👀 Starting to monitor Discord queue file")
        
        if DISCORD_QUEUE_FILE.exists():
            self.last_queue_size = DISCORD_QUEUE_FILE.stat().st_size
            self.log(f"📊 Initial queue file size: {self.last_queue_size} bytes")
        
        while True:
            try:
                await self.process_new_messages()
                await asyncio.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                self.log(f"❌ Error in monitor loop: {e}")
                await asyncio.sleep(5)  # Wait longer on error
    
    async def process_new_messages(self):
        """Process new messages from queue file"""
        if not DISCORD_QUEUE_FILE.exists():
            return
        
        current_size = DISCORD_QUEUE_FILE.stat().st_size
        if current_size <= self.last_queue_size:
            return
        
        try:
            # Read new content
            with open(DISCORD_QUEUE_FILE, 'r', encoding='utf-8') as f:
                f.seek(self.last_queue_size)
                new_lines = f.readlines()
            
            new_messages = []
            for line in new_lines:
                if line.strip():
                    try:
                        message_data = json.loads(line.strip())
                        message_id = message_data.get('id', '')
                        
                        if message_id and message_id not in self.processed_message_ids:
                            new_messages.append(message_data)
                    except json.JSONDecodeError as e:
                        self.log(f"⚠️ JSON decode error: {e}")
            
            if new_messages:
                self.log(f"📥 Found {len(new_messages)} new Discord messages to process")
                
                for message_data in new_messages:
                    await self.process_message(message_data)
                
                # Update processed IDs and save
                self.save_processed_ids()
            
            self.last_queue_size = current_size
            
        except Exception as e:
            self.log(f"❌ Error processing new messages: {e}")
    
    async def process_message(self, message_data):
        """Process a single message"""
        try:
            message_id = message_data.get('id', 'unknown')
            channel_id = message_data.get('channel_id')
            channel_name = message_data.get('channel_name', 'unknown')
            cycle_number = message_data.get('cycle_number', 'unknown')
            
            # Get embed data
            embed_data = message_data.get('embed_data')
            embed_data_raw = message_data.get('embed_data_raw')
            
            # Handle both formats
            if embed_data_raw:
                # Parse JSON string (leaderboard messages)
                embed_data = json.loads(embed_data_raw)
                
                # Store leaderboard data for button interactions
                if message_data.get('message_type') == 'leaderboard':
                    self.leaderboard_data[message_id] = {
                        'embed_data': embed_data,
                        'message_data': message_data,
                        'timestamp': datetime.now()
                    }
            
            if not embed_data:
                self.log(f"⚠️ Message {message_id[:8]}... missing embed data")
                return
            
            # Apply formatting fixes
            embed_data = self.process_discord_formatting(embed_data)
            
            # Send to Discord
            self.log(f"📤 Sending message {message_id[:8]}... to {channel_name} channel (Cycle #{cycle_number})")
            
            success, discord_message_id = await self.send_discord_message(channel_id, embed_data, message_data)
            
            if success:
                self.processed_message_ids.add(message_id)
                self.log(f"✅ Message processed successfully")
            else:
                self.log(f"❌ Failed to send message")
                
        except Exception as e:
            self.log(f"❌ Error processing message: {e}")

class LeaderboardButton(discord.ui.Button):
    """Custom button for leaderboard interactions"""
    
    def __init__(self, label, custom_id, style, bot, message_data):
        super().__init__(label=label, custom_id=custom_id, style=style)
        self.bot = bot
        self.message_data = message_data
    
    async def callback(self, interaction: discord.Interaction):
        """Handle button click"""
        try:
            # Extract slot number from custom_id
            slot_number = self.custom_id.replace('leaderboard_slot_', '')
            
            self.bot.log(f"🔘 Button interaction: {self.custom_id} by user {interaction.user.id}")
            
            # Create response embed
            embed = discord.Embed(
                title=f"🏆 Leaderboard Category: Slot {slot_number}",
                description=f"📊 Showing data for leaderboard slot {slot_number}\n" +
                           f"🔄 This will show category-specific player rankings\n\n" +
                           f"*Note: Full leaderboard integration is in development*",
                color=0x00ff00
            )
            
            # Send ephemeral response
            await interaction.response.send_message(embed=embed, ephemeral=True)
            
            self.bot.log(f"📊 Leaderboard button response sent for slot {slot_number}")
            
        except Exception as e:
            self.bot.log(f"❌ Error handling button interaction: {e}")
            
            # Send error response
            error_embed = discord.Embed(
                title="❌ Error",
                description="An error occurred processing your request.",
                color=0xff0000
            )
            
            try:
                await interaction.response.send_message(embed=error_embed, ephemeral=True)
            except:
                pass  # Interaction might have already been responded to

async def main():
    """Main function"""
    print("🚀 Starting Enhanced Discord Bridge Bot v3.0")
    print("=" * 60)
    print(f"📁 Queue file: {DISCORD_QUEUE_FILE}")
    print(f"📁 Output file: {DISCORD_OUTPUT_FILE}")
    print(f"🎯 Target channels: ISTOP={CHANNEL_IDS['ISTOP']}, LB={CHANNEL_IDS['LB']}")
    print("⌨️ Press Ctrl+C to stop")
    print("=" * 60)
    
    # Check if bot token is configured
    if DISCORD_BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("❌ ERROR: Discord bot token not configured!")
        print("Please set DISCORD_BOT_TOKEN in the script")
        return
    
    # Create and run bot
    bot = EnhancedDiscordBot()
    
    try:
        await bot.start(DISCORD_BOT_TOKEN)
    except KeyboardInterrupt:
        print("\n👋 Shutting down Discord Bridge Bot v3.0")
        await bot.close()
    except Exception as e:
        print(f"❌ Error running bot: {e}")

if __name__ == "__main__":
    asyncio.run(main())
