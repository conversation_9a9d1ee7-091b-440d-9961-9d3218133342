#!/usr/bin/env python3
"""
Fix Leaderboard Embed Issue - Diagnose and fix the placeholder embed problem
"""

import json
import os
from datetime import datetime

def analyze_current_issue():
    """Analyze the current leaderboard embed issue"""
    print("🔍 Analyzing Leaderboard Embed Issue")
    print("=" * 60)
    
    # Check console logs for evidence
    console_file = "consolelogs.txt"
    if os.path.exists(console_file):
        with open(console_file, 'r', encoding='utf-8') as f:
            logs = f.read()
        
        # Check for new method calls
        if "SendLeaderboardEmbed" in logs:
            print("✅ New SendLeaderboardEmbed method is being called")
        else:
            print("❌ New SendLeaderboardEmbed method is NOT being called")
            print("   → Bot is still running old code version")
        
        if "WriteLeaderboardEmbedToFile" in logs:
            print("✅ New WriteLeaderboardEmbedToFile method is being called")
        else:
            print("❌ New WriteLeaderboardEmbedToFile method is NOT being called")
        
        if "ConvertJsonStringToObject" in logs:
            print("✅ ConvertJsonStringToObject method is being called")
        else:
            print("❌ ConvertJsonStringToObject method is NOT being called")
    
    # Check discord queue for recent leaderboard messages
    queue_file = "discord_queue.json"
    if os.path.exists(queue_file):
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"\n📊 Analyzing {len(lines)} queue messages...")
        
        recent_leaderboard = None
        for line in reversed(lines):
            line = line.strip()
            if not line:
                continue
            
            try:
                message_data = json.loads(line)
                if message_data.get('channel_name') == 'LB':
                    embed_data = message_data.get('embed_data', {})
                    if 'embeds' in embed_data:
                        embeds = embed_data['embeds']
                        if embeds and len(embeds) > 0:
                            title = embeds[0].get('title', '')
                            if 'Leaderboard' in title or 'Top' in title:
                                recent_leaderboard = message_data
                                break
            except json.JSONDecodeError:
                continue
        
        if recent_leaderboard:
            print("\n🎯 Found Recent Leaderboard Message:")
            print(f"   ID: {recent_leaderboard.get('id', 'Unknown')}")
            print(f"   Timestamp: {recent_leaderboard.get('timestamp', 'Unknown')}")
            
            # Check message format
            if recent_leaderboard.get('message_type') == 'leaderboard':
                print("✅ Using new leaderboard message format")
                print(f"   Action: {recent_leaderboard.get('action', 'Unknown')}")
                print(f"   Supports interactions: {recent_leaderboard.get('supports_interactions', False)}")
            else:
                print("❌ Using old message format (no message_type field)")
            
            # Check embed content
            embed_data = recent_leaderboard.get('embed_data', {})
            if 'embed_data_raw' in recent_leaderboard:
                print("✅ Contains raw embed JSON data")
            elif 'embeds' in embed_data:
                embeds = embed_data['embeds']
                if embeds and len(embeds) > 0:
                    embed = embeds[0]
                    title = embed.get('title', '')
                    description = embed.get('description', '')
                    
                    if title == "Parsed from JSON" or description == "Embed data parsed successfully":
                        print("❌ FOUND PLACEHOLDER DATA!")
                        print("   Title:", title)
                        print("   Description:", description)
                        print("   → This confirms the ConvertJsonStringToObject issue")
                    else:
                        print("✅ Contains real leaderboard data")
                        print(f"   Title: {title}")
                        print(f"   Description: {description}")
                        
                        # Check for interactive components
                        if 'components' in embed_data:
                            components = embed_data['components']
                            if components and len(components) > 0:
                                print("✅ Contains interactive components (buttons)")
                            else:
                                print("❌ No interactive components found")
                        else:
                            print("❌ No components field found")
        else:
            print("❌ No recent leaderboard messages found")

def identify_root_cause():
    """Identify the root cause of the issue"""
    print("\n🔬 Root Cause Analysis")
    print("=" * 40)
    
    # Check if bot needs restart
    console_file = "consolelogs.txt"
    if os.path.exists(console_file):
        with open(console_file, 'r', encoding='utf-8') as f:
            logs = f.read()
        
        # Look for recent bot initialization
        if "Multi-Phase Bot initialized successfully" in logs:
            lines = logs.split('\n')
            for line in reversed(lines):
                if "Multi-Phase Bot initialized successfully" in line:
                    print(f"📅 Last bot initialization: {line}")
                    break
        
        # Check for method calls
        if "SendLeaderboardEmbed" not in logs:
            print("🚨 PRIMARY ISSUE: Bot is running old code version")
            print("   → The new SendLeaderboardEmbed method is not being called")
            print("   → MCC needs to be restarted to load updated script")
            return "RESTART_REQUIRED"
        
        if "ConvertJsonStringToObject" in logs and "Parsed from JSON" in logs:
            print("🚨 SECONDARY ISSUE: ConvertJsonStringToObject returning placeholder")
            print("   → Method is being called but returning wrong data")
            return "JSON_PARSING_ISSUE"
    
    return "UNKNOWN"

def provide_solution(root_cause):
    """Provide solution based on root cause"""
    print("\n💡 Solution")
    print("=" * 20)
    
    if root_cause == "RESTART_REQUIRED":
        print("🔄 SOLUTION: Restart MCC to load updated script")
        print()
        print("Steps:")
        print("1. Stop the current MCC bot script")
        print("2. Restart MCC completely")
        print("3. Load the script: /script multi-phase-bot")
        print("4. Wait for next 10-minute cycle to test")
        print()
        print("Expected result:")
        print("• Console should show 'SendLeaderboardEmbed' calls")
        print("• Discord queue should have message_type: 'leaderboard'")
        print("• Embeds should contain real player data with buttons")
        
    elif root_cause == "JSON_PARSING_ISSUE":
        print("🔧 SOLUTION: JSON parsing method fixed")
        print()
        print("The ConvertJsonStringToObject method has been updated to:")
        print("• Pass raw JSON data directly to Discord bridge")
        print("• Avoid placeholder data generation")
        print("• Preserve original embed structure")
        print()
        print("Restart MCC to apply the fix.")
        
    else:
        print("❓ SOLUTION: Manual investigation required")
        print()
        print("Check:")
        print("• Console logs for error messages")
        print("• Discord queue file format")
        print("• Embed creation methods")

def main():
    print("🔧 Leaderboard Embed Issue Diagnostic Tool")
    print("=" * 70)
    print()
    
    # Analyze the current state
    analyze_current_issue()
    
    # Identify root cause
    root_cause = identify_root_cause()
    
    # Provide solution
    provide_solution(root_cause)
    
    print()
    print("📋 Summary of Fixes Applied:")
    print("1. ✅ Fixed ConvertJsonStringToObject placeholder issue")
    print("2. ✅ Updated WriteLeaderboardEmbedToFile to use raw JSON")
    print("3. ✅ Added proper embed data handling")
    print("4. ✅ Preserved original embed structure for Discord bridge")
    
    print()
    print("🎯 Expected Behavior After Restart:")
    print("• Leaderboard embeds show real player data")
    print("• Interactive buttons appear below embed")
    print("• Message updates instead of creating new messages")
    print("• Console shows new method calls")
    
    print()
    print("🚨 CRITICAL: Restart MCC to apply all fixes!")

if __name__ == "__main__":
    main()
