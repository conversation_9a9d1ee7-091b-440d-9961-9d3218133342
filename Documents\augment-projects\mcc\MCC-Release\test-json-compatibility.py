#!/usr/bin/env python3
"""
Test JSON Compatibility for MCC Multi-Phase Bot
Validates that the JSON output from the MCC bot is properly formatted
and can be parsed by the Discord bridge bot.
"""

import json
import os
from datetime import datetime

def test_json_parsing():
    """Test JSON parsing of the discord_queue.json file"""
    print("🧪 JSON Compatibility Test")
    print("=" * 50)
    print()
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print(f"❌ Queue file not found: {queue_file}")
        print("   Run the MCC bot first to generate JSON messages")
        return False
    
    print(f"📁 Testing file: {queue_file}")
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            print("⚠️ Queue file is empty")
            return True
        
        print(f"📊 Found {len(lines)} JSON messages to test")
        print()
        
        success_count = 0
        error_count = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            try:
                # Test JSON parsing
                message_data = json.loads(line)
                
                # Validate required fields
                required_fields = ['id', 'timestamp', 'channel_id', 'embed_data']
                missing_fields = [field for field in required_fields if field not in message_data]
                
                if missing_fields:
                    print(f"❌ Line {i}: Missing fields: {missing_fields}")
                    error_count += 1
                    continue
                
                # Test embed_data parsing
                embed_data = message_data.get('embed_data')
                if isinstance(embed_data, str):
                    # If embed_data is a string, try to parse it as JSON
                    try:
                        json.loads(embed_data)
                    except json.JSONDecodeError as e:
                        print(f"❌ Line {i}: Invalid embed_data JSON: {e}")
                        error_count += 1
                        continue
                
                # Success
                channel_name = message_data.get('channel_name', 'Unknown')
                message_id = message_data.get('id', 'Unknown')[:8]
                print(f"✅ Line {i}: Valid JSON - {channel_name} channel - ID {message_id}...")
                success_count += 1
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
                print(f"   Content: {line[:100]}...")
                error_count += 1
        
        print()
        print("📊 Test Results:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Errors: {error_count}")
        print(f"   📈 Success Rate: {success_count/(success_count+error_count)*100:.1f}%")
        
        if error_count == 0:
            print()
            print("🎉 All JSON messages are valid!")
            print("   The Discord bridge bot should process them successfully.")
            return True
        else:
            print()
            print("⚠️ Some JSON messages have errors.")
            print("   The Discord bridge bot may fail to process invalid messages.")
            return False
            
    except Exception as e:
        print(f"❌ Error reading queue file: {e}")
        return False

def test_unicode_handling():
    """Test Unicode character handling"""
    print()
    print("🔤 Unicode Character Test")
    print("=" * 30)
    
    # Test common emojis used in the bot
    test_emojis = {
        "🔄": "Cycle start",
        "📊": "Status info", 
        "⏳": "Pending",
        "⏰": "Time",
        "🎯": "Target",
        "✅": "Success",
        "⏱️": "Performance",
        "•": "Bullet point"
    }
    
    for emoji, description in test_emojis.items():
        try:
            # Test JSON encoding/decoding
            test_data = {"emoji": emoji, "description": description}
            json_str = json.dumps(test_data, ensure_ascii=False)
            parsed_data = json.loads(json_str)
            
            if parsed_data["emoji"] == emoji:
                print(f"✅ {emoji} ({description}): OK")
            else:
                print(f"❌ {emoji} ({description}): Encoding issue")
                
        except Exception as e:
            print(f"❌ {emoji} ({description}): Error - {e}")
    
    print()
    print("✅ Unicode test complete")

def show_sample_json():
    """Show a sample of what valid JSON should look like"""
    print()
    print("📋 Sample Valid JSON Format")
    print("=" * 30)
    
    sample_json = {
        "id": "12345678-1234-1234-1234-123456789012",
        "timestamp": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
        "cycle_number": 1,
        "channel_id": "1401451296711507999",
        "channel_name": "ISTOP",
        "embed_data": {
            "embeds": [{
                "title": "🔄 Test Message",
                "description": "This is a test message with emojis 📊",
                "color": 65280,
                "fields": [{
                    "name": "✅ Status",
                    "value": "Working correctly",
                    "inline": True
                }]
            }]
        },
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    print(json.dumps(sample_json, indent=2, ensure_ascii=False))

def main():
    """Main test function"""
    print("🚀 MCC Multi-Phase Bot JSON Compatibility Test")
    print("=" * 60)
    print()
    
    # Test JSON parsing
    json_valid = test_json_parsing()
    
    # Test Unicode handling
    test_unicode_handling()
    
    # Show sample format
    show_sample_json()
    
    print()
    print("=" * 60)
    if json_valid:
        print("🎉 JSON compatibility test PASSED!")
        print("   The MCC bot is generating valid JSON for the Discord bridge bot.")
    else:
        print("⚠️ JSON compatibility test found issues.")
        print("   Check the error messages above and fix the JSON generation.")
    
    print()
    print("Next steps:")
    print("1. If test passed: Restart the Discord bridge bot")
    print("2. If test failed: Fix the JSON issues and test again")
    print("3. Monitor the bridge bot console for successful message processing")

if __name__ == "__main__":
    main()
