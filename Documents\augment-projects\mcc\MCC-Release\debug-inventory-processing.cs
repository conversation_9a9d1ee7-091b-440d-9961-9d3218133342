//MCCScript 1.0

/* Debug script specifically for inventory processing issues
 * This script will help identify why ExtractIslandDataFromInventory is not being called
 */

MCC.LoadBot(new InventoryProcessingDebugBot());

//MCCScript Extensions

public class InventoryProcessingDebugBot : ChatBot
{
    private bool inventoryHandlingEnabled = false;
    
    public override void Initialize()
    {
        LogToConsole("=== Inventory Processing Debug Bot ===");
        LogToConsole("*** DEBUGGING WHY ExtractIslandDataFromInventory IS NOT CALLED ***");
        
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** CRITICAL: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
        }
        
        LogToConsole("This bot will debug the inventory processing flow");
        LogToConsole("Run /istop to test the issue");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            
            if (cleanText.Contains("Inventory") && cleanText.Contains("opened"))
            {
                LogToConsole("*** INVENTORY OPENED - DEBUGGING PROCESSING FLOW ***");
                DebugInventoryProcessing();
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error in GetText: {ex.Message}");
        }
    }
    
    private void DebugInventoryProcessing()
    {
        try
        {
            LogToConsole("*** STEP 1: CHECKING INVENTORY ACCESS ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("❌ Cannot debug - inventory handling disabled");
                return;
            }
            
            LogToConsole("*** STEP 2: GETTING INVENTORIES ***");
            var inventories = GetInventories();
            LogToConsole($"Found {inventories.Count} inventories");
            
            if (inventories.Count == 0)
            {
                LogToConsole("❌ No inventories found - cannot proceed");
                return;
            }
            
            LogToConsole("*** STEP 3: LOGGING INVENTORY DETAILS ***");
            LogInventoryDetails(inventories);
            LogToConsole("*** STEP 3 COMPLETED ***");
            
            LogToConsole("*** STEP 4: INVENTORY SELECTION LOGIC ***");
            Container targetInventory = null;
            
            LogToConsole($"Available inventory IDs: {string.Join(", ", inventories.Keys)}");
            LogToConsole($"Checking for inventory #2...");
            LogToConsole($"inventories.ContainsKey(2): {inventories.ContainsKey(2)}");
            
            if (inventories.ContainsKey(2))
            {
                targetInventory = inventories[2];
                LogToConsole($"*** USING INVENTORY #2 ***");
                LogToConsole($"Inventory #2 has {targetInventory.Items.Count} items");
            }
            else if (inventories.Count > 0)
            {
                targetInventory = inventories.Values.First();
                var inventoryId = inventories.Keys.First();
                LogToConsole($"*** USING INVENTORY #{inventoryId} AS FALLBACK ***");
                LogToConsole($"Fallback inventory has {targetInventory.Items.Count} items");
            }
            
            LogToConsole($"Target inventory selected: {targetInventory != null}");
            LogToConsole("*** STEP 4 COMPLETED ***");
            
            if (targetInventory != null)
            {
                LogToConsole("*** STEP 5: CALLING ISLAND DATA EXTRACTION ***");
                LogToConsole($"Target inventory has {targetInventory.Items.Count} items");
                
                // Call the extraction method
                TestExtractIslandData(targetInventory);
                
                LogToConsole("*** STEP 5 COMPLETED ***");
            }
            else
            {
                LogToConsole("❌ No suitable inventory found for data extraction");
            }
            
            LogToConsole("*** INVENTORY PROCESSING DEBUG COMPLETE ***");
            
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Exception in inventory processing debug: {ex.Message}");
            LogToConsole($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private void LogInventoryDetails(Dictionary<int, Container> inventories)
    {
        try
        {
            LogToConsole("*** LOGGING INVENTORY DETAILS ***");
            
            int inventoryIndex = 0;
            foreach (var kvp in inventories)
            {
                var inventoryId = kvp.Key;
                var inventory = kvp.Value;
                inventoryIndex++;
                
                LogToConsole($"=== INVENTORY #{inventoryIndex} (ID: {inventoryId}) ===");
                LogToConsole($"Type: {inventory.Type}");
                LogToConsole($"Total Items: {inventory.Items.Count}");
                
                // Log first few items
                int itemCount = 0;
                foreach (var itemKvp in inventory.Items.OrderBy(x => x.Key))
                {
                    if (itemCount >= 3) break; // Only log first 3 items
                    
                    var slot = itemKvp.Key;
                    var item = itemKvp.Value;
                    
                    LogToConsole($"Slot {slot:D2}: {item.Type} x{item.Count}");
                    LogToConsole($"  Display: '{item.DisplayName}'");
                    LogToConsole($"  NBT: {(item.NBT == null || item.NBT.Count == 0 ? "None" : $"{item.NBT.Count} entries")}");
                    
                    itemCount++;
                }
                
                if (inventory.Items.Count > 3)
                {
                    LogToConsole($"... and {inventory.Items.Count - 3} more items");
                }
                
                LogToConsole($"=== END INVENTORY #{inventoryIndex} ===");
            }
            
            LogToConsole("*** INVENTORY DETAILS LOGGING COMPLETE ***");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error logging inventory details: {ex.Message}");
        }
    }
    
    private void TestExtractIslandData(Container inventory)
    {
        try
        {
            LogToConsole("*** ENTERED TestExtractIslandData ***");
            LogToConsole($"Inventory parameter is null: {inventory == null}");
            LogToConsole($"Inventory items count: {inventory?.Items?.Count ?? 0}");
            
            if (inventory == null)
            {
                LogToConsole("❌ Inventory is null - cannot extract data");
                return;
            }
            
            LogToConsole("*** TESTING ISLAND DATA SLOTS ***");
            int[] islandDataSlots = { 13, 21, 23, 29, 31 };
            LogToConsole($"Checking island data slots: {string.Join(", ", islandDataSlots)}");
            
            int foundIslands = 0;
            foreach (int slot in islandDataSlots)
            {
                LogToConsole($"--- CHECKING SLOT {slot} ---");
                
                if (inventory.Items.ContainsKey(slot))
                {
                    var item = inventory.Items[slot];
                    foundIslands++;
                    
                    LogToConsole($"*** FOUND ITEM IN SLOT {slot} ***");
                    LogToConsole($"Type: {item.Type}");
                    LogToConsole($"Count: {item.Count}");
                    LogToConsole($"Display Name: '{item.DisplayName}'");
                    LogToConsole($"NBT null: {item.NBT == null}");
                    LogToConsole($"NBT count: {item.NBT?.Count ?? 0}");
                    
                    // Test NBT parsing
                    if (item.NBT != null && item.NBT.Count > 0)
                    {
                        LogToConsole($"*** TESTING NBT PARSING FOR SLOT {slot} ***");
                        TestItemNBT(item.NBT, slot);
                    }
                    else
                    {
                        LogToConsole($"No NBT data to parse for slot {slot}");
                    }
                }
                else
                {
                    LogToConsole($"Slot {slot} is EMPTY");
                }
                
                LogToConsole($"--- END SLOT {slot} ---");
            }
            
            LogToConsole($"*** EXTRACTION COMPLETE: {foundIslands} islands found ***");
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in TestExtractIslandData: {ex.Message}");
            LogToConsole($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private void TestItemNBT(Dictionary<string, object> nbt, int slot)
    {
        try
        {
            LogToConsole($"*** NBT ANALYSIS FOR SLOT {slot} ***");
            LogToConsole($"NBT has {nbt.Count} entries");
            
            foreach (var kvp in nbt)
            {
                LogToConsole($"NBT Key: '{kvp.Key}' = Type: {kvp.Value?.GetType().Name ?? "null"}");
                
                if (kvp.Key == "display" && kvp.Value is Dictionary<string, object> displayDict)
                {
                    LogToConsole("*** FOUND DISPLAY COMPOUND ***");
                    TestDisplayCompound(displayDict, slot);
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing NBT for slot {slot}: {ex.Message}");
        }
    }
    
    private void TestDisplayCompound(Dictionary<string, object> display, int slot)
    {
        try
        {
            LogToConsole($"*** DISPLAY COMPOUND FOR SLOT {slot} ***");
            LogToConsole($"Display has {display.Count} entries");
            
            foreach (var kvp in display)
            {
                LogToConsole($"Display Key: '{kvp.Key}' = Type: {kvp.Value?.GetType().Name ?? "null"}");
                
                if (kvp.Key == "Lore" && kvp.Value is List<object> loreList)
                {
                    LogToConsole("*** FOUND LORE LIST ***");
                    TestLoreList(loreList, slot);
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing display compound for slot {slot}: {ex.Message}");
        }
    }
    
    private void TestLoreList(List<object> lore, int slot)
    {
        try
        {
            LogToConsole($"*** LORE LIST FOR SLOT {slot} ***");
            LogToConsole($"Lore has {lore.Count} entries");
            
            for (int i = 0; i < lore.Count; i++)
            {
                var loreItem = lore[i];
                LogToConsole($"Lore[{i}]: '{loreItem?.ToString() ?? "null"}'");
                
                // This is where we would see the actual lore content!
                if (loreItem != null)
                {
                    string loreText = loreItem.ToString();
                    if (loreText.Contains("$") || loreText.Contains("point") || loreText.Contains("value"))
                    {
                        LogToConsole($"*** POTENTIAL VALUE IN LORE[{i}]: {loreText} ***");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error testing lore list for slot {slot}: {ex.Message}");
        }
    }
    
    public override void Update()
    {
        // Minimal update
    }
}
