# MCC Discord Bridge Bot - Complete Setup Guide

## 🎯 **Overview**

The MCC Discord Bridge Bot is an external Python script that monitors the MCC multi-phase bot logs and sends the generated Discord embeds to actual Discord channels. This bridges the gap between MCC's perfect embed generation (simulation mode) and actual Discord message delivery.

## 📋 **Prerequisites**

### **1. Python Installation**
- **Python 3.8 or higher** required
- Download from: https://python.org/downloads/
- **Important**: Check "Add Python to PATH" during installation

### **2. Discord Bot Setup**
- **Bot Token**: `MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.Gd7AJ8.w0YR7GFuBpMgbu5LqXys7iA1b-W3w7fIbC2wO4`
- **Client ID**: `1401449503609065493`
- **Target Channels**:
  - **ISTOP Channel**: `1401451296711507999`
  - **LB Channel**: `1401451313216094383`

### **3. Required Files**
All files should be in the same directory:
- ✅ `discord-bridge-bot.py` - Main bridge bot script
- ✅ `discord-bridge-config.json` - Configuration file
- ✅ `requirements.txt` - Python dependencies
- ✅ `start-discord-bridge.bat` - Windows startup script
- ✅ `inventory2.txt` - MCC log file (or update path in config)

## 🚀 **Quick Start (Windows)**

### **Method 1: Automatic Setup (Recommended)**
1. **Double-click** `start-discord-bridge.bat`
2. **Wait for setup** - The script will:
   - Check Python installation
   - Create virtual environment
   - Install dependencies
   - Start the Discord bridge bot
3. **Monitor output** - You should see:
   ```
   🚀 Starting MCC Discord Bridge Bot
   Monitoring log file: inventory2.txt
   Target channels: ISTOP=1401451296711507999, LB=1401451313216094383
   ```

### **Method 2: Manual Setup**
```bash
# 1. Create virtual environment
python -m venv venv

# 2. Activate virtual environment
venv\Scripts\activate.bat

# 3. Install dependencies
pip install -r requirements.txt

# 4. Start the bridge bot
python discord-bridge-bot.py
```

## ⚙️ **Configuration**

### **discord-bridge-config.json**
```json
{
  "discord": {
    "bot_token": "MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.Gd7AJ8.w0YR7GFuBpMgbu5LqXys7iA1b-W3w7fIbC2wO4",
    "channels": {
      "istop": "1401451296711507999",
      "lb": "1401451313216094383"
    },
    "api_base": "https://discord.com/api/v10"
  },
  "monitoring": {
    "mcc_log_file": "inventory2.txt",
    "processed_messages_file": "processed_messages.json",
    "bridge_log_file": "discord_bridge.log"
  },
  "rate_limiting": {
    "delay_between_calls": 1.0,
    "max_retries": 3,
    "retry_delay": 5.0
  }
}
```

### **Customization Options:**
- **`mcc_log_file`**: Path to MCC log file (change if different location)
- **`delay_between_calls`**: Seconds between Discord API calls (increase if rate limited)
- **`max_retries`**: Number of retry attempts for failed messages
- **`retry_delay`**: Seconds to wait before retrying failed requests

## 🔄 **How It Works**

### **1. Log Monitoring**
The bridge bot monitors the MCC log file for Discord embed content:
```
[MCC] [MultiPhaseBot] [Phase 4] Channel: 1401451296711507999
[MCC] [MultiPhaseBot] [Phase 4] Content: {"embeds":[{"title":"✅ /istop Command Status"...}]}
```

### **2. Embed Extraction**
- **Parses channel IDs** from log lines
- **Extracts JSON embed data** from "Content:" lines
- **Validates JSON format** before sending

### **3. Discord API Delivery**
- **Sends HTTP POST requests** to Discord API
- **Handles rate limiting** automatically
- **Retries failed requests** with exponential backoff
- **Logs all operations** for monitoring

### **4. Duplicate Prevention**
- **Tracks processed messages** in `processed_messages.json`
- **Prevents duplicate sends** when restarting the bot
- **Handles log file rotation** gracefully

## 📊 **Expected Output**

### **Successful Startup:**
```
2025-08-03 14:30:00 - INFO - 🚀 Starting MCC Discord Bridge Bot
2025-08-03 14:30:00 - INFO - Monitoring log file: inventory2.txt
2025-08-03 14:30:00 - INFO - Target channels: ISTOP=1401451296711507999, LB=1401451313216094383
2025-08-03 14:30:00 - INFO - Discord API session initialized
2025-08-03 14:30:00 - INFO - Processing log file: inventory2.txt
```

### **Message Processing:**
```
2025-08-03 14:30:05 - INFO - 📤 Sending Discord message to channel 1401451296711507999
2025-08-03 14:30:06 - INFO - ✅ Successfully sent message 1234567890 to channel 1401451296711507999
2025-08-03 14:30:10 - INFO - 📤 Sending Discord message to channel 1401451313216094383
2025-08-03 14:30:11 - INFO - ✅ Successfully sent message 1234567891 to channel 1401451313216094383
```

### **Real-time Monitoring:**
```
2025-08-03 14:35:00 - INFO - 📤 New Discord message detected for channel 1401451296711507999
2025-08-03 14:35:01 - INFO - ✅ Successfully sent message 1234567892 to channel 1401451296711507999
```

## 🔧 **Integration with MCC Multi-Phase Bot**

### **Complete Workflow:**
1. **Start MCC** with the multi-phase bot: `/script multi-phase-bot`
2. **Start Discord Bridge** in a separate terminal/window
3. **Monitor both logs** to verify integration:
   - **MCC Log**: Shows embed generation in simulation mode
   - **Bridge Log**: Shows actual Discord message delivery

### **Expected Discord Messages:**

#### **ISTOP Channel Messages:**
- **Bot initialization** notification
- **/istop command status** updates
- **Error and timeout** notifications
- **Task completion** summaries

#### **LB Channel Messages:**
- **Island data embeds** with top 5 islands and values
- **Leaderboard embeds** with 24 categories and top 3 players

## 🛠️ **Troubleshooting**

### **Common Issues:**

#### **1. "Python not found"**
```bash
# Solution: Install Python and add to PATH
# Download from: https://python.org/downloads/
# Restart command prompt after installation
```

#### **2. "Discord API error 401"**
```bash
# Solution: Check bot token in config file
# Ensure bot has proper permissions in Discord server
```

#### **3. "Log file not found"**
```bash
# Solution: Update mcc_log_file path in config
# Ensure MCC is generating logs in the specified location
```

#### **4. "Rate limited by Discord"**
```bash
# Solution: Increase delay_between_calls in config
# Default is 1.0 seconds, try 2.0 or higher
```

### **Debug Mode:**
Change logging level in config to `"DEBUG"` for verbose output:
```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

## 📁 **File Structure**
```
MCC-Release/
├── discord-bridge-bot.py          # Main bridge bot script
├── discord-bridge-config.json     # Configuration file
├── requirements.txt                # Python dependencies
├── start-discord-bridge.bat       # Windows startup script
├── inventory2.txt                  # MCC log file (example)
├── processed_messages.json        # Processed message tracking (auto-generated)
├── discord_bridge.log             # Bridge bot logs (auto-generated)
└── venv/                          # Python virtual environment (auto-generated)
```

## 🚀 **Production Deployment**

### **Running as a Service (Windows):**
1. **Install NSSM** (Non-Sucking Service Manager)
2. **Create service**:
   ```bash
   nssm install "MCC Discord Bridge" "C:\path\to\python.exe" "C:\path\to\discord-bridge-bot.py"
   nssm set "MCC Discord Bridge" AppDirectory "C:\path\to\MCC-Release"
   nssm start "MCC Discord Bridge"
   ```

### **Running with Task Scheduler:**
1. **Open Task Scheduler**
2. **Create Basic Task**
3. **Set trigger**: At startup
4. **Set action**: Start program `start-discord-bridge.bat`

## ✅ **Verification Steps**

### **1. Test Bridge Bot:**
```bash
# Start the bridge bot
python discord-bridge-bot.py

# Check for successful startup messages
# Verify Discord API session initialization
```

### **2. Test with MCC:**
```bash
# Start MCC multi-phase bot
/script multi-phase-bot

# Monitor bridge bot logs for message processing
# Check Discord channels for actual messages
```

### **3. Verify Discord Integration:**
- **Check ISTOP channel** for bot status messages
- **Check LB channel** for island and leaderboard data
- **Verify message formatting** matches MCC embed generation

## 🎯 **Success Indicators**

### **✅ Bridge Bot Working:**
- Discord API session initialized successfully
- Log file monitoring active
- No error messages in bridge logs

### **✅ Full Integration Working:**
- MCC bot generates embeds in simulation mode
- Bridge bot detects and processes embed content
- Actual Discord messages appear in target channels
- Message content matches MCC-generated embeds

## 📞 **Support**

### **Log Files to Check:**
- **`discord_bridge.log`** - Bridge bot operations
- **MCC console output** - Multi-phase bot execution
- **`processed_messages.json`** - Message tracking

### **Common Success Patterns:**
```
✅ Discord API session initialized
✅ Successfully sent message [ID] to channel [CHANNEL]
✅ Message [COUNT] sent successfully
```

**Your MCC Discord Bridge Bot is now ready to provide full Discord integration for the multi-phase bot!** 🚀
