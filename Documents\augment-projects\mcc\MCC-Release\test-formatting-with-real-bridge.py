#!/usr/bin/env python3
"""
Test formatting fixes with the real Discord bridge
"""

import json
import uuid
from datetime import datetime

def create_formatting_test_message():
    """Create a test message to verify formatting fixes work"""
    
    print("🧪 CREATING FORMATTING TEST MESSAGE")
    print("=" * 50)
    
    # Create a test message that simulates the problematic formatting
    message_data = {
        "id": "formatting-test-" + str(uuid.uuid4())[:8],
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 999,
        "channel_id": "1401451313216094383",  # LB channel
        "channel_name": "LB",
        "embed_data_raw": json.dumps({
            "embeds": [{
                "title": "🧪 FORMATTING TEST - Enhanced Discord Display",
                "description": "Testing enhanced formatting fixes\\n*This should show proper line breaks and emojis*",
                "color": 65280,  # Green
                "timestamp": datetime.now().isoformat() + "Z",
                "fields": [
                    {
                        "name": "🏝️ #1: Test Island (10 points)",
                        "value": "**All Time:** $1.5B\\n**Weekly:** $701.6M\\n**Growth:** Last: +$303.6M • 1h: +$303.6M • 24h: +$379.0M",
                        "inline": False
                    },
                    {
                        "name": "🏆 Top Test Players:",
                        "value": "#1: Nincompoopz 69 points - 50 GC\\n#2: Jaeger1000 61 points - 40 GC\\n#3: MostlyMissing 51 points - 35 GC\\n#4: TestPlayer 45 points - 30 GC\\n#5: AnotherTest 40 points - 25 GC",
                        "inline": False
                    },
                    {
                        "name": "📊 Formatting Test Results",
                        "value": "✅ Newlines should appear as actual line breaks\\n✅ Island data should have emoji indicators\\n✅ Player rankings should have medal emojis\\n✅ Text should be properly structured",
                        "inline": False
                    }
                ],
                "footer": {
                    "text": "Formatting Test • Enhanced Discord Display Active",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/test-icon.png"
                }
            }],
            "components": [{
                "type": 1,
                "components": [
                    {"type": 2, "style": 2, "label": "Test Button 1", "custom_id": "test_button_1"},
                    {"type": 2, "style": 2, "label": "Test Button 2", "custom_id": "test_button_2"},
                    {"type": 2, "style": 2, "label": "Leaderboard Test", "custom_id": "leaderboard_slot_99"}
                ]
            }]
        }, separators=(',', ':')),
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "formatting_test",
        "action": "create",
        "update_message_id": None,
        "supports_interactions": True,
        "interaction_handler": "HandleTestButtonInteraction"
    }
    
    return message_data

def create_real_world_test():
    """Create a test that simulates real MCC bot output"""
    
    print("🎯 CREATING REAL-WORLD SIMULATION TEST")
    print("=" * 50)
    
    # This simulates what the actual MCC bot generates
    message_data = {
        "id": "real-world-test-" + str(uuid.uuid4())[:8],
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",  # LB channel
        "channel_name": "LB",
        "embed_data_raw": json.dumps({
            "embeds": [{
                "title": "🏆 Top 10 Players",
                "description": "Player rankings extracted at " + datetime.now().strftime("%H:%M:%S") + "\\n*Click buttons below to view different categories*",
                "color": 16766720,
                "timestamp": datetime.now().isoformat() + "Z",
                "fields": [{
                    "name": "🏆 Top 12 Players:",
                    "value": "#1: MostlyMissing 78 points - 50 GC\\n#2: Jaeger1000 73 points - 40 GC\\n#3: Nincompoopz 67 points - 35 GC\\n#4: Lil_Bouncy21 44 points - 30 GC\\n#5: Cataclysm69 40 points - 25 GC\\n#6: DaSimsGamer 29 points - 20 GC\\n#7: roughjoke95 25 points - 15 GC\\n#8: Ketchup47 23 points - 10 GC\\n#9: LogicalPlays 23 points - 10 GC\\n#10: gleeked1 23 points - 5 GC",
                    "inline": False
                }],
                "footer": {
                    "text": "Phase 3 • 23 categories available",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                }
            }],
            "components": [
                {
                    "type": 1,
                    "components": [
                        {"type": 2, "style": 2, "label": "Top 12 Players:", "custom_id": "leaderboard_slot_4"},
                        {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_slot_9"},
                        {"type": 2, "style": 2, "label": "Scrolls Completed ◆ ", "custom_id": "leaderboard_slot_11"},
                        {"type": 2, "style": 2, "label": "Runes", "custom_id": "leaderboard_slot_13"},
                        {"type": 2, "style": 2, "label": "Blocks", "custom_id": "leaderboard_slot_15"}
                    ]
                },
                {
                    "type": 1,
                    "components": [
                        {"type": 2, "style": 2, "label": "Crops Harvested", "custom_id": "leaderboard_slot_17"},
                        {"type": 2, "style": 2, "label": "Ores Mined", "custom_id": "leaderboard_slot_19"},
                        {"type": 2, "style": 2, "label": "Pouches", "custom_id": "leaderboard_slot_21"},
                        {"type": 2, "style": 2, "label": "Votes", "custom_id": "leaderboard_slot_23"},
                        {"type": 2, "style": 2, "label": "Fishing", "custom_id": "leaderboard_slot_25"}
                    ]
                }
            ]
        }, separators=(',', ':')),
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create",
        "update_message_id": None,
        "supports_interactions": True,
        "interaction_handler": "HandleLeaderboardButtonInteraction"
    }
    
    return message_data

def main():
    """Create and queue test messages"""
    
    print("🧪 DISCORD FORMATTING & INTERACTION TEST")
    print("=" * 60)
    
    # Create test messages
    formatting_test = create_formatting_test_message()
    real_world_test = create_real_world_test()
    
    # Write to queue file
    with open("discord_queue.json", "a", encoding='utf-8') as f:
        # Add formatting test
        f.write(json.dumps(formatting_test, separators=(',', ':')) + "\n")
        print(f"✅ Formatting test message queued: {formatting_test['id']}")
        
        # Add real-world test
        f.write(json.dumps(real_world_test, separators=(',', ':')) + "\n")
        print(f"✅ Real-world test message queued: {real_world_test['id']}")
    
    print(f"\n📝 TEST MESSAGES CREATED:")
    print(f"1. Formatting Test: {formatting_test['id']}")
    print(f"   - Tests enhanced formatting with emojis")
    print(f"   - Tests newline conversion")
    print(f"   - Tests button interactions")
    
    print(f"\n2. Real-World Test: {real_world_test['id']}")
    print(f"   - Simulates actual MCC bot leaderboard message")
    print(f"   - Tests player ranking formatting")
    print(f"   - Tests leaderboard button interactions")
    
    print(f"\n👀 MONITORING INSTRUCTIONS:")
    print(f"1. Watch Discord bridge logs for processing messages")
    print(f"2. Check Discord channel for properly formatted messages")
    print(f"3. Click buttons to test interaction responses")
    print(f"4. Verify formatting shows:")
    print(f"   - Actual line breaks (not \\n literals)")
    print(f"   - Emoji indicators (💰📊📈 for islands, 🥇🥈🥉 for players)")
    print(f"   - Proper field structure")
    
    print(f"\n🔘 BUTTON TEST INSTRUCTIONS:")
    print(f"1. Click any leaderboard button")
    print(f"2. Should see ephemeral response (only visible to you)")
    print(f"3. Should NOT see 'This interaction failed'")
    print(f"4. Check bridge logs for interaction processing")

if __name__ == "__main__":
    main()
