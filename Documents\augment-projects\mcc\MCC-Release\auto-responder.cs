//MCCScript 1.0

/* Auto-Responder Bot
 * Responds to specific keywords in chat */

MCC.LoadBot(new AutoResponder());

//MCCScript Extensions

public class AutoResponder : ChatBot
{
    private Dictionary<string, string> responses;
    
    public override void Initialize()
    {
        LogToConsole("Auto-Responder Bot loaded!");
        
        // Initialize responses
        responses = new Dictionary<string, string>
        {
            {"hello", "Hello there! How can I help you?"},
            {"help", "I'm here to assist! Ask me anything."},
            {"time", "Current server time: " + DateTime.Now.ToString()},
            {"rules", "Please follow server rules and be respectful!"},
            {"bot", "Yes, I'm a bot created with MCC!"}
        };
    }
    
    public override void GetText(string text)
    {
        string message = "";
        string username = "";
        text = GetVerbatim(text);
        
        if (IsChatMessage(text, ref message, ref username))
        {
            // Convert to lowercase for case-insensitive matching
            string lowerMessage = message.ToLower();
            
            // Check for keywords
            foreach (var keyword in responses.Keys)
            {
                if (lowerMessage.Contains(keyword))
                {
                    // Wait a moment to seem more natural
                    Thread.Sleep(1000);
                    SendText(responses[keyword]);
                    break; // Only respond to first match
                }
            }
        }
        else if (IsPrivateMessage(text, ref message, ref username))
        {
            LogToConsole($"Private message from {username}: {message}");
            // Respond to private messages
            SendText($"/tell {username} Thanks for your private message!");
        }
    }
}
