//MCCScript 1.0

/* Debug script for NBT parsing issues
 * This script will help identify why the enhanced NBT parsing is not working
 */

MCC.LoadBot(new DebugNBTBot());

//MCCScript Extensions

public class DebugNBTBot : ChatBot
{
    private bool inventoryHandlingEnabled = false;
    
    public override void Initialize()
    {
        LogToConsole("=== Debug NBT Parsing Bot ===");
        LogToConsole("*** DEBUGGING NBT PARSING ISSUES ***");
        
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** CRITICAL: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
        }
        
        LogToConsole("This bot will help debug why NBT parsing is not working");
        LogToConsole("Run /istop to test the issue");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            
            if (cleanText.Contains("Inventory") && cleanText.Contains("opened"))
            {
                LogToConsole("*** INVENTORY OPENED - STARTING DEBUG ***");
                DebugInventoryAccess();
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error in GetText: {ex.Message}");
        }
    }
    
    private void DebugInventoryAccess()
    {
        try
        {
            LogToConsole("*** DEBUG: CHECKING INVENTORY ACCESS ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("❌ Cannot debug - inventory handling disabled");
                return;
            }
            
            var inventories = GetInventories();
            LogToConsole($"Found {inventories.Count} inventories");
            
            if (inventories.Count == 0)
            {
                LogToConsole("❌ No inventories found");
                return;
            }
            
            // Test each inventory
            foreach (var kvp in inventories)
            {
                var inventoryId = kvp.Key;
                var inventory = kvp.Value;
                
                LogToConsole($"*** TESTING INVENTORY #{inventoryId} ***");
                LogToConsole($"Items count: {inventory.Items.Count}");
                LogToConsole($"Type: {inventory.Type}");
                
                // Test specific slots
                int[] testSlots = { 13, 21, 23, 29, 31 };
                foreach (int slot in testSlots)
                {
                    if (inventory.Items.ContainsKey(slot))
                    {
                        var item = inventory.Items[slot];
                        LogToConsole($"*** SLOT {slot} DEBUG ***");
                        LogToConsole($"Type: {item.Type}");
                        LogToConsole($"Display: '{item.DisplayName}'");
                        LogToConsole($"NBT null: {item.NBT == null}");
                        LogToConsole($"NBT count: {item.NBT?.Count ?? 0}");
                        
                        if (item.NBT != null && item.NBT.Count > 0)
                        {
                            LogToConsole($"*** NBT STRUCTURE FOR SLOT {slot} ***");
                            DebugNBTStructure(item.NBT, 0);
                        }
                        
                        LogToConsole($"*** END SLOT {slot} DEBUG ***");
                        break; // Only test first found item for detailed analysis
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in debug: {ex.Message}");
            LogToConsole($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private void DebugNBTStructure(Dictionary<string, object> nbt, int depth)
    {
        try
        {
            string indent = new string(' ', depth * 2);
            
            foreach (var kvp in nbt)
            {
                var key = kvp.Key;
                var value = kvp.Value;
                
                LogToConsole($"{indent}Key: '{key}' = Type: {value?.GetType().Name ?? "null"}");
                
                if (value == null)
                {
                    LogToConsole($"{indent}  Value: null");
                }
                else if (value is string str)
                {
                    LogToConsole($"{indent}  String Value: '{str}'");
                }
                else if (value is Dictionary<string, object> dict)
                {
                    LogToConsole($"{indent}  Dictionary with {dict.Count} entries:");
                    if (depth < 3) // Prevent infinite recursion
                    {
                        DebugNBTStructure(dict, depth + 1);
                    }
                }
                else if (value is List<object> list)
                {
                    LogToConsole($"{indent}  List with {list.Count} entries:");
                    for (int i = 0; i < Math.Min(list.Count, 10); i++)
                    {
                        var listItem = list[i];
                        LogToConsole($"{indent}    [{i}]: Type: {listItem?.GetType().Name ?? "null"}");
                        LogToConsole($"{indent}    [{i}]: Value: '{listItem?.ToString() ?? "null"}'");
                    }
                    if (list.Count > 10)
                    {
                        LogToConsole($"{indent}    ... and {list.Count - 10} more entries");
                    }
                }
                else if (value is object[] array)
                {
                    LogToConsole($"{indent}  Array with {array.Length} entries:");
                    for (int i = 0; i < Math.Min(array.Length, 10); i++)
                    {
                        var arrayItem = array[i];
                        LogToConsole($"{indent}    [{i}]: Type: {arrayItem?.GetType().Name ?? "null"}");
                        LogToConsole($"{indent}    [{i}]: Value: '{arrayItem?.ToString() ?? "null"}'");
                    }
                    if (array.Length > 10)
                    {
                        LogToConsole($"{indent}    ... and {array.Length - 10} more entries");
                    }
                }
                else
                {
                    LogToConsole($"{indent}  Other Value: '{value}'");
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error debugging NBT structure: {ex.Message}");
        }
    }
    
    public override void Update()
    {
        // Minimal update
    }
}
