#!/usr/bin/env python3
"""
Real-time Discord queue monitor to track new vs old messages
"""

import json
import time
import os
from datetime import datetime
from pathlib import Path

class QueueMonitor:
    def __init__(self, queue_file="discord_queue.json"):
        self.queue_file = Path(queue_file)
        self.last_size = 0
        self.processed_messages = set()
        self.fixes_implemented_time = datetime(2025, 8, 4, 0, 46, 0)  # When we implemented fixes
        
        if self.queue_file.exists():
            self.last_size = self.queue_file.stat().st_size
            self.load_existing_messages()
    
    def load_existing_messages(self):
        """Load existing messages to avoid reprocessing them"""
        try:
            with open(self.queue_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                if line.strip():
                    try:
                        message = json.loads(line.strip())
                        self.processed_messages.add(message.get('id', ''))
                    except json.JSONDecodeError:
                        pass
            
            print(f"📊 Loaded {len(self.processed_messages)} existing messages")
        except Exception as e:
            print(f"⚠️ Error loading existing messages: {e}")
    
    def analyze_message_timing(self, message):
        """Analyze if message was created before or after fixes"""
        try:
            # Parse message timestamp
            timestamp_str = message.get('timestamp', '')
            if timestamp_str:
                # Handle both formats: ISO with Z and local time
                if timestamp_str.endswith('Z'):
                    msg_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    # Convert to local time (assuming UTC+7 based on logs)
                    msg_time = msg_time.replace(tzinfo=None)
                else:
                    msg_time = datetime.fromisoformat(timestamp_str)
                
                # Compare with fixes implementation time
                if msg_time >= self.fixes_implemented_time:
                    return "AFTER_FIXES", msg_time
                else:
                    return "BEFORE_FIXES", msg_time
            
            # Fallback to created_at field
            created_at = message.get('created_at', '')
            if created_at:
                msg_time = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
                if msg_time >= self.fixes_implemented_time:
                    return "AFTER_FIXES", msg_time
                else:
                    return "BEFORE_FIXES", msg_time
            
            return "UNKNOWN", None
        except Exception as e:
            print(f"⚠️ Error parsing message timing: {e}")
            return "ERROR", None
    
    def analyze_control_characters(self, json_str):
        """Analyze JSON string for control characters"""
        control_chars = []
        for i, char in enumerate(json_str):
            if ord(char) < 32 and char not in ['\n', '\r', '\t']:
                control_chars.append({
                    'position': i,
                    'char': repr(char),
                    'ord': ord(char),
                    'hex': f"0x{ord(char):02X}"
                })
        return control_chars
    
    def analyze_message(self, message):
        """Comprehensive message analysis"""
        msg_id = message.get('id', 'unknown')
        msg_type = message.get('message_type', 'unknown')
        channel = message.get('channel_name', 'unknown')
        
        # Timing analysis
        timing_status, msg_time = self.analyze_message_timing(message)
        
        # Control character analysis
        embed_data_raw = message.get('embed_data_raw', '')
        control_chars = []
        if embed_data_raw:
            control_chars = self.analyze_control_characters(embed_data_raw)
        
        # Check for specific problematic characters at position 91-92
        pos_91_92_issue = False
        if len(embed_data_raw) > 91:
            char_91 = embed_data_raw[90] if len(embed_data_raw) > 90 else None
            char_92 = embed_data_raw[91] if len(embed_data_raw) > 91 else None
            
            if char_91 and ord(char_91) < 32 and char_91 not in ['\n', '\r', '\t']:
                pos_91_92_issue = True
            if char_92 and ord(char_92) < 32 and char_92 not in ['\n', '\r', '\t']:
                pos_91_92_issue = True
        
        return {
            'id': msg_id,
            'type': msg_type,
            'channel': channel,
            'timing_status': timing_status,
            'message_time': msg_time,
            'has_embed_data_raw': bool(embed_data_raw),
            'control_chars_count': len(control_chars),
            'control_chars': control_chars[:5],  # Limit to first 5
            'pos_91_92_issue': pos_91_92_issue,
            'json_length': len(embed_data_raw) if embed_data_raw else 0
        }
    
    def check_for_new_messages(self):
        """Check for new messages in the queue"""
        if not self.queue_file.exists():
            return []
        
        current_size = self.queue_file.stat().st_size
        if current_size <= self.last_size:
            return []
        
        new_messages = []
        try:
            with open(self.queue_file, 'r', encoding='utf-8') as f:
                f.seek(self.last_size)
                new_lines = f.readlines()
            
            for line in new_lines:
                if line.strip():
                    try:
                        message = json.loads(line.strip())
                        msg_id = message.get('id', '')
                        
                        if msg_id not in self.processed_messages:
                            analysis = self.analyze_message(message)
                            new_messages.append(analysis)
                            self.processed_messages.add(msg_id)
                    except json.JSONDecodeError as e:
                        print(f"⚠️ JSON decode error: {e}")
            
            self.last_size = current_size
        except Exception as e:
            print(f"❌ Error reading new messages: {e}")
        
        return new_messages
    
    def print_message_analysis(self, analysis):
        """Print detailed message analysis"""
        print(f"\n🔍 NEW MESSAGE DETECTED:")
        print(f"   ID: {analysis['id'][:12]}...")
        print(f"   Type: {analysis['type']}")
        print(f"   Channel: {analysis['channel']}")
        print(f"   Time: {analysis['message_time']}")
        print(f"   Status: {analysis['timing_status']}")
        
        if analysis['has_embed_data_raw']:
            print(f"   📊 embed_data_raw: {analysis['json_length']} chars")
            
            if analysis['control_chars_count'] > 0:
                print(f"   ❌ CONTROL CHARACTERS: {analysis['control_chars_count']} found")
                for ctrl in analysis['control_chars']:
                    print(f"      Position {ctrl['position']}: {ctrl['char']} ({ctrl['hex']})")
                
                if analysis['pos_91_92_issue']:
                    print(f"   🚨 CRITICAL: Control character at position 91-92!")
                
                if analysis['timing_status'] == "AFTER_FIXES":
                    print(f"   🚨 CRITICAL: This message was created AFTER fixes - enhanced cleaning not working!")
                else:
                    print(f"   ℹ️ Expected: This message was created BEFORE fixes")
            else:
                print(f"   ✅ No control characters found")
                if analysis['timing_status'] == "AFTER_FIXES":
                    print(f"   ✅ SUCCESS: Enhanced cleaning working correctly")
        else:
            print(f"   📊 Uses embed_data format (not embed_data_raw)")

def main():
    print("🔧 REAL-TIME DISCORD QUEUE MONITOR")
    print("=" * 60)
    print("📝 Monitoring for new messages...")
    print("🕐 Fixes implemented at: 2025-08-04 00:46:00")
    print("👀 Watching for control character issues...")
    print("⌨️ Press Ctrl+C to stop")
    print("=" * 60)
    
    monitor = QueueMonitor()
    
    try:
        while True:
            new_messages = monitor.check_for_new_messages()
            
            for analysis in new_messages:
                monitor.print_message_analysis(analysis)
                
                # Special alerts for critical issues
                if (analysis['timing_status'] == "AFTER_FIXES" and 
                    analysis['control_chars_count'] > 0):
                    print(f"\n🚨 CRITICAL ALERT: NEW MESSAGE WITH CONTROL CHARACTERS!")
                    print(f"   This indicates the enhanced cleaning is not working!")
                    print(f"   Message ID: {analysis['id']}")
                    print(f"   Investigation needed!")
            
            time.sleep(2)  # Check every 2 seconds
            
    except KeyboardInterrupt:
        print(f"\n\n📊 MONITORING SUMMARY:")
        print(f"   Total messages processed: {len(monitor.processed_messages)}")
        print(f"   Monitor stopped by user")

if __name__ == "__main__":
    main()
