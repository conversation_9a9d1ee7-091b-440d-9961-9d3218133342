//MCCScript 1.0

/* Test script to verify newline handling fix for island embeds */

using System;
using System.Collections.Generic;

MCC.LoadBot(new NewlineFixTest());

public class NewlineFixTest : ChatBot
{
    public override void Initialize()
    {
        LogToConsole("=== NEWLINE HANDLING TEST ===");
        
        // Test the island embed field format with \\n (double backslash)
        string islandFieldValue = "**All Time:** $1.04B\\n**Weekly:** $242.3M\\n**Growth:** Insufficient historical data";
        
        LogToConsole($"Original island field value: {islandFieldValue}");
        
        // Create test island embed data similar to CreateIslandDataEmbed
        var islandEmbed = new
        {
            embeds = new[]
            {
                new
                {
                    title = "🏝️ Top Islands Data - Test",
                    description = "Island rankings extracted at 23:22:30",
                    color = 0x0099FF,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "🏝️ #1: Revenants (10 points)",
                            value = islandFieldValue,  // This contains \\n
                            inline = false
                        },
                        new
                        {
                            name = "🏝️ #2: NIP (9 points)", 
                            value = "**All Time:** $670.37M\\n**Weekly:** $143.43M\\n**Growth:** Insufficient historical data",
                            inline = false
                        }
                    },
                    footer = new
                    {
                        text = "Phase 2 • 2 islands tracked • Updated every 10 minutes",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
                    }
                }
            }
        };
        
        // Test the JSON serialization with the fixed SimpleJsonSerializer
        string result = SimpleJsonSerializer(islandEmbed);
        LogToConsole("=== SERIALIZED ISLAND EMBED ===");
        LogToConsole(result);
        
        // Check if the newlines are properly handled
        bool hasCorrectNewlines = result.Contains("\\n") && !result.Contains("\\\\n");
        LogToConsole($"=== NEWLINE TEST RESULT ===");
        LogToConsole($"Contains \\n (single backslash): {result.Contains("\\n")}");
        LogToConsole($"Contains \\\\n (double backslash): {result.Contains("\\\\n")}");
        LogToConsole($"Newline handling correct: {hasCorrectNewlines}");
        LogToConsole(hasCorrectNewlines ? "✅ TEST PASSED" : "❌ TEST FAILED");
        
        // Test regular string with actual newlines
        LogToConsole("\n=== REGULAR STRING TEST ===");
        string regularString = "Line 1\nLine 2\nLine 3";
        string regularResult = SimpleJsonSerializer(regularString);
        LogToConsole($"Regular string result: {regularResult}");
        bool regularCorrect = regularResult.Contains("\\n") && !regularResult.Contains("\n");
        LogToConsole($"Regular string escaping correct: {regularCorrect}");
        LogToConsole(regularCorrect ? "✅ REGULAR STRING TEST PASSED" : "❌ REGULAR STRING TEST FAILED");
        
        LogToConsole("=== TEST COMPLETE ===");
    }
    
    private string SimpleJsonSerializer(object obj)
    {
        if (obj == null) return "null";

        var type = obj.GetType();

        if (type == typeof(string))
        {
            // Properly escape JSON string values for Discord embeds
            string str = obj.ToString();
            
            // Handle Discord-specific newlines: preserve \\n as \n for Discord rendering
            // but escape actual newlines and other special characters
            str = str.Replace("\\", "\\\\")  // Escape backslashes first
                     .Replace("\"", "\\\"")  // Escape quotes
                     .Replace("\r", "\\r")   // Escape carriage returns  
                     .Replace("\t", "\\t");  // Escape tabs
            
            // Special handling for newlines: 
            // - If string contains \\n (double backslash), preserve as \n for Discord
            // - If string contains actual \n characters, escape them
            if (str.Contains("\\\\n"))
            {
                // This is a Discord embed field with intentional \\n formatting
                str = str.Replace("\\\\n", "\\n"); // Convert \\n to \n for Discord
            }
            else
            {
                // Regular string with actual newlines - escape them
                str = str.Replace("\n", "\\n");
            }
            
            return $"\"{str}\"";
        }

        if (type == typeof(int) || type == typeof(long) || type == typeof(double) || type == typeof(float))
            return obj.ToString();

        if (type == typeof(bool))
            return obj.ToString().ToLower();

        if (obj is System.Collections.IEnumerable && !(obj is string))
        {
            var items = new List<string>();
            foreach (var item in (System.Collections.IEnumerable)obj)
            {
                items.Add(SimpleJsonSerializer(item));
            }
            return $"[{string.Join(",", items.ToArray())}]";
        }

        // Handle anonymous objects and complex types
        var properties = type.GetProperties();
        var jsonPairs = new List<string>();

        foreach (var prop in properties)
        {
            var value = prop.GetValue(obj, null);
            var jsonValue = SimpleJsonSerializer(value);
            jsonPairs.Add($"\"{prop.Name}\":{jsonValue}");
        }

        return $"{{{string.Join(",", jsonPairs.ToArray())}}}";
    }
}
