# 🔗 Discord Bridge Integration Guide

## Overview
This guide explains how to integrate the MCC Multi-Phase Bot with a Discord bridge to handle interactive button responses and message updates.

## New Features Implemented

### 📝 Message Update System
- **File**: `discord_queue.json`
- **New Fields**:
  - `message_type`: "leaderboard" for leaderboard messages
  - `action`: "create" or "update"
  - `update_message_id`: ID of message to update (when action = "update")
  - `supports_interactions`: true for interactive messages
  - `interaction_handler`: "HandleLeaderboardButtonInteraction"

### 🔘 Button Interaction System
- **File**: `discord_interactions.json` (new)
- **Purpose**: Stores button interaction responses for Discord bridge processing
- **Format**: Each line contains interaction response data

## Discord Bridge Requirements

### 1. Message Update Handling

The Discord bridge should check the `action` field in `discord_queue.json`:

```python
# Example Discord bridge logic
message_data = json.loads(queue_line)

if message_data.get('action') == 'update':
    # Update existing message
    message_id = message_data.get('update_message_id')
    await channel.edit_message(message_id, embed=embed_data)
    print(f"Updated message {message_id}")
else:
    # Create new message
    message = await channel.send(embed=embed_data)
    print(f"Created new message {message.id}")
```

### 2. Button Interaction Webhook

The Discord bridge needs to handle button interaction webhooks:

```python
@bot.event
async def on_interaction(interaction):
    if interaction.type == discord.InteractionType.component:
        # Button was clicked
        custom_id = interaction.data['custom_id']
        user_id = str(interaction.user.id)
        user_name = interaction.user.display_name
        
        # Call MCC bot's interaction handler
        response = handle_mcc_button_interaction(custom_id, user_id, user_name)
        
        # Send ephemeral response
        await interaction.response.send_message(
            embed=response_embed, 
            ephemeral=True
        )
```

### 3. Interaction Response Processing

Monitor `discord_interactions.json` for responses:

```python
def process_interaction_responses():
    if os.path.exists('discord_interactions.json'):
        with open('discord_interactions.json', 'r') as f:
            for line in f:
                interaction_data = json.loads(line)
                if interaction_data.get('status') == 'pending':
                    # Process the interaction response
                    send_interaction_response(interaction_data)
                    # Mark as processed
                    mark_interaction_processed(interaction_data['id'])
```

## File Formats

### discord_queue.json (Enhanced)
```json
{
  "id": "message-uuid",
  "timestamp": "2025-08-03T21:32:53.987Z",
  "cycle_number": 1,
  "channel_id": "1401451296711507999",
  "channel_name": "LB",
  "embed_data": {
    "embeds": [...],
    "components": [...]
  },
  "status": "pending",
  "created_at": "2025-08-03 14:32:53",
  "message_type": "leaderboard",
  "action": "update",
  "update_message_id": "previous-message-uuid",
  "supports_interactions": true,
  "interaction_handler": "HandleLeaderboardButtonInteraction"
}
```

### discord_interactions.json (New)
```json
{
  "id": "interaction-uuid",
  "timestamp": "2025-08-03T21:32:53.987Z",
  "button_id": "leaderboard_slot_13",
  "user_id": "123456789",
  "user_name": "PlayerName",
  "response": {
    "type": 4,
    "data": {
      "embeds": [...],
      "flags": 64
    }
  },
  "status": "pending",
  "created_at": "2025-08-03 14:32:53",
  "interaction_type": "button_click",
  "ephemeral": true
}
```

## Implementation Steps

### Step 1: Update Discord Bridge Message Handling
1. Check for `action` field in queue messages
2. Implement message update logic for `action: "update"`
3. Store message IDs for future updates

### Step 2: Add Interaction Webhook Handler
1. Set up Discord interaction webhook endpoint
2. Handle button click events
3. Extract `custom_id` and user information

### Step 3: Connect to MCC Bot
1. Call MCC bot's `HandleLeaderboardButtonInteraction()` method
2. Pass button ID and user information
3. Process the returned interaction response

### Step 4: Monitor Interaction Response File
1. Watch `discord_interactions.json` for new responses
2. Send ephemeral responses to Discord
3. Mark interactions as processed

## Button Custom ID Format

### Real Data Buttons
- Format: `leaderboard_{category_key}`
- Example: `leaderboard_slot_13`
- Handler: Returns category-specific top 10 players

### Sample Data Buttons
- Format: `sample_{category}`
- Example: `sample_mobs`, `sample_runes`
- Handler: Returns sample category data

## Error Handling

### Interaction Timeout
- Discord requires response within 3 seconds
- Send immediate acknowledgment, then follow up with data

### Failed Interactions
- Log errors to console
- Send user-friendly error message
- Continue processing other interactions

### Message Update Failures
- Fall back to creating new message
- Log the failure for debugging
- Update tracking to prevent future conflicts

## Testing

### Test Button Interactions
1. Send leaderboard message with buttons
2. Click buttons to trigger interactions
3. Verify ephemeral responses appear
4. Check `discord_interactions.json` for response data

### Test Message Updates
1. Send first leaderboard (should create new message)
2. Send second leaderboard within 15 minutes (should update existing)
3. Send third leaderboard after 15+ minutes (should create new)

### Test Error Handling
1. Test with invalid button IDs
2. Test with missing category data
3. Test interaction timeout scenarios

## Troubleshooting

### "This interaction failed" Error
- Check webhook endpoint is receiving interactions
- Verify 3-second response timeout
- Check interaction response format

### Buttons Not Appearing
- Verify `components` array in embed data
- Check button type (should be 2) and action row type (should be 1)
- Ensure Discord API version supports components

### Message Updates Not Working
- Check `update_message_id` is valid
- Verify message exists and bot has permissions
- Check message age (Discord has limits on old message updates)

## Security Considerations

### Button ID Validation
- Validate custom_id format before processing
- Sanitize user input
- Rate limit button interactions per user

### Ephemeral Responses
- Always use ephemeral flag for button responses
- Don't expose sensitive data in public responses
- Log interactions for audit purposes
