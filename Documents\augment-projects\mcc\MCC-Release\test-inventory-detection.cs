//MCCScript 1.0

/* Test script for inventory detection debugging
 * This script focuses on detecting and logging all inventory-related messages
 * to understand how MCC handles inventory GUI events
 */

MCC.LoadBot(new InventoryDetectionTestBot());

//MCCScript Extensions

public class InventoryDetectionTestBot : ChatBot
{
    private int messageCount = 0;
    private bool inventoryHandlingEnabled = false;
    
    public override void Initialize()
    {
        LogToConsole("=== Inventory Detection Test Bot ===");
        LogToConsole("*** DEBUGGING INVENTORY MESSAGE DETECTION ***");
        
        // Check if inventory handling is enabled
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("WARNING: Inventory handling is disabled!");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
        }
        
        LogToConsole("This bot will log ALL text messages to help debug inventory detection");
        LogToConsole("Try running /istop to see what messages appear");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            messageCount++;
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Log every single message for debugging
            LogToConsole($"[MSG #{messageCount}] Raw: '{text}'");
            LogToConsole($"[MSG #{messageCount}] Clean: '{cleanText}'");
            
            // Check if it's a chat message
            bool isChatMessage = IsChatMessage(cleanText, ref message, ref username);
            bool isPrivateMessage = IsPrivateMessage(cleanText, ref message, ref username);
            
            LogToConsole($"[MSG #{messageCount}] Chat: {isChatMessage}, PM: {isPrivateMessage}");
            
            if (isChatMessage)
            {
                LogToConsole($"[MSG #{messageCount}] CHAT from {username}: {message}");
            }
            else if (isPrivateMessage)
            {
                LogToConsole($"[MSG #{messageCount}] PM from {username}: {message}");
            }
            else
            {
                LogToConsole($"[MSG #{messageCount}] SERVER/SYSTEM MESSAGE");
            }
            
            // Special handling for inventory-related messages
            if (cleanText.ToLower().Contains("inventory"))
            {
                LogToConsole($"[INVENTORY ALERT] *** INVENTORY MESSAGE DETECTED ***");
                LogToConsole($"[INVENTORY ALERT] Message #{messageCount}: '{cleanText}'");
                LogToConsole($"[INVENTORY ALERT] Chat: {isChatMessage}, PM: {isPrivateMessage}");
                
                if (cleanText.Contains("opened"))
                {
                    LogToConsole("[INVENTORY ALERT] *** INVENTORY OPENED ***");
                    HandleInventoryOpened(cleanText);
                }
                else if (cleanText.Contains("closed"))
                {
                    LogToConsole("[INVENTORY ALERT] *** INVENTORY CLOSED ***");
                }
                else if (cleanText.Contains("content"))
                {
                    LogToConsole("[INVENTORY ALERT] *** INVENTORY CONTENT ***");
                }
            }
            
            // Check for /istop related messages
            if (cleanText.ToLower().Contains("istop") || cleanText.ToLower().Contains("island") || cleanText.ToLower().Contains("top"))
            {
                LogToConsole($"[ISTOP ALERT] *** ISTOP-RELATED MESSAGE ***");
                LogToConsole($"[ISTOP ALERT] Message #{messageCount}: '{cleanText}'");
            }
            
            LogToConsole($"[MSG #{messageCount}] ----------------------------------------");
            
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error processing message #{messageCount}: {ex.Message}");
        }
    }
    
    private void HandleInventoryOpened(string inventoryMessage)
    {
        try
        {
            LogToConsole("[INVENTORY] Processing inventory opened event...");
            
            if (inventoryHandlingEnabled)
            {
                LogToConsole("[INVENTORY] Inventory handling is enabled, checking inventories...");
                
                var inventories = GetInventories();
                LogToConsole($"[INVENTORY] Found {inventories.Count} open inventories");
                
                foreach (var kvp in inventories)
                {
                    var id = kvp.Key;
                    var inventory = kvp.Value;
                    LogToConsole($"[INVENTORY] Inventory {id}: {inventory.Items.Count} items, Type: {inventory.Type}");
                    
                    // Log first few items for debugging
                    int itemCount = 0;
                    foreach (var itemKvp in inventory.Items)
                    {
                        if (itemCount >= 5) break; // Only log first 5 items
                        
                        var slot = itemKvp.Key;
                        var item = itemKvp.Value;
                        LogToConsole($"[INVENTORY] Slot {slot}: {item.Type} - '{item.DisplayName}'");
                        itemCount++;
                    }
                    
                    if (inventory.Items.Count > 5)
                    {
                        LogToConsole($"[INVENTORY] ... and {inventory.Items.Count - 5} more items");
                    }
                }
                
                // Test specific slots that should contain island data
                TestIslandDataSlots(inventories);
            }
            else
            {
                LogToConsole("[INVENTORY] Inventory handling is disabled, cannot access inventory data");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[INVENTORY] Error handling inventory opened: {ex.Message}");
        }
    }
    
    private void TestIslandDataSlots(Dictionary<int, Container> inventories)
    {
        try
        {
            int[] testSlots = { 13, 21, 23, 29, 31 }; // Expected island data slots
            
            LogToConsole("[INVENTORY] Testing island data slots...");
            
            foreach (var inventory in inventories.Values)
            {
                LogToConsole($"[INVENTORY] Checking inventory with {inventory.Items.Count} items for island data...");
                
                foreach (int slot in testSlots)
                {
                    if (inventory.Items.ContainsKey(slot))
                    {
                        var item = inventory.Items[slot];
                        LogToConsole($"[ISLAND DATA] Slot {slot}: {item.Type}");
                        LogToConsole($"[ISLAND DATA] Display Name: '{item.DisplayName}'");
                        LogToConsole($"[ISLAND DATA] NBT: {(item.NBT == null || item.NBT.Count == 0 ? "None" : $"{item.NBT.Count} entries")}");
                        
                        // This is where we would extract island information
                        if (!string.IsNullOrEmpty(item.DisplayName))
                        {
                            LogToConsole($"[ISLAND DATA] Potential island name: {ExtractCleanName(item.DisplayName)}");
                        }
                    }
                    else
                    {
                        LogToConsole($"[ISLAND DATA] Slot {slot}: EMPTY");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[INVENTORY] Error testing island data slots: {ex.Message}");
        }
    }
    
    private string ExtractCleanName(string displayName)
    {
        // Remove Minecraft formatting codes
        return displayName.Replace("§", "").Trim();
    }
    
    public override void Update()
    {
        // Simple update counter
        static int updateCount = 0;
        updateCount++;
        
        // Log status every 200 ticks (approximately 20 seconds)
        if (updateCount % 200 == 0)
        {
            LogToConsole($"[STATUS] Bot running - {messageCount} messages processed, Update #{updateCount}");
            LogToConsole($"[STATUS] Inventory handling: {inventoryHandlingEnabled}");
            
            if (inventoryHandlingEnabled)
            {
                try
                {
                    var inventories = GetInventories();
                    LogToConsole($"[STATUS] Currently {inventories.Count} inventories open");
                }
                catch (Exception ex)
                {
                    LogToConsole($"[STATUS] Error checking inventories: {ex.Message}");
                }
            }
        }
    }
}
