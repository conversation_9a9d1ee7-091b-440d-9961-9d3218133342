#!/usr/bin/env python3
"""
Test Discord Button Interactions - Comprehensive Testing
Tests the fixed button interaction system in discord-bridge-v2.py
"""

import json
import uuid
import asyncio
import aiohttp
from datetime import datetime

# Test configuration
INTERACTION_SERVER_URL = "http://localhost:8080/interactions"
HEALTH_CHECK_URL = "http://localhost:8080/health"

def create_test_leaderboard_message():
    """Create a test leaderboard message with buttons"""
    
    message_id = f"button-test-{str(uuid.uuid4())[:8]}"
    
    test_message = {
        "id": message_id,
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 999,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": json.dumps({
            "embeds": [{
                "title": "🧪 BUTTON INTERACTION TEST",
                "description": "Testing enhanced button interaction system\\n*Click buttons below to test the fixes*",
                "color": 16766720,
                "timestamp": datetime.now().isoformat() + "Z",
                "fields": [{
                    "name": "🔧 Test Features:",
                    "value": "✅ Discord signature verification\\n✅ Proper CORS headers\\n✅ Enhanced error handling\\n✅ Ephemeral responses\\n✅ User-specific feedback",
                    "inline": False
                }],
                "footer": {
                    "text": "Button Interaction Test • Enhanced System",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/test-icon.png"
                }
            }],
            "components": [{
                "type": 1,
                "components": [
                    {"type": 2, "style": 2, "label": "Test Leaderboard", "custom_id": "leaderboard_slot_test"},
                    {"type": 2, "style": 2, "label": "Test Button", "custom_id": "test_cleaned_data"},
                    {"type": 2, "style": 2, "label": "Generic Test", "custom_id": "test_generic"},
                    {"type": 2, "style": 1, "label": "Error Test", "custom_id": "test_error_handling"}
                ]
            }]
        }, separators=(',', ':')),
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create",
        "supports_interactions": True,
        "interaction_handler": "HandleLeaderboardButtonInteraction"
    }
    
    return message_id, test_message

async def test_health_check():
    """Test the health check endpoint"""
    print("🏥 Testing health check endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(HEALTH_CHECK_URL) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Health check passed: {data}")
                    return True
                else:
                    print(f"❌ Health check failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

async def test_cors_preflight():
    """Test CORS preflight request"""
    print("🌐 Testing CORS preflight...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.options(
                INTERACTION_SERVER_URL,
                headers={
                    'Origin': 'https://discord.com',
                    'Access-Control-Request-Method': 'POST',
                    'Access-Control-Request-Headers': 'Content-Type, X-Signature-Ed25519, X-Signature-Timestamp'
                }
            ) as response:
                if response.status == 200:
                    headers = dict(response.headers)
                    print(f"✅ CORS preflight passed")
                    print(f"   Access-Control-Allow-Origin: {headers.get('Access-Control-Allow-Origin', 'NOT SET')}")
                    print(f"   Access-Control-Allow-Methods: {headers.get('Access-Control-Allow-Methods', 'NOT SET')}")
                    return True
                else:
                    print(f"❌ CORS preflight failed: {response.status}")
                    return False
    except Exception as e:
        print(f"❌ CORS preflight error: {e}")
        return False

async def test_discord_ping():
    """Test Discord ping interaction"""
    print("🏓 Testing Discord ping interaction...")
    
    ping_payload = {
        "type": 1,  # PING
        "id": "test-ping-interaction",
        "application_id": "test-app-id",
        "token": "test-token"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                INTERACTION_SERVER_URL,
                json=ping_payload,
                headers={
                    'Content-Type': 'application/json',
                    'X-Signature-Ed25519': 'test-signature',
                    'X-Signature-Timestamp': str(int(datetime.now().timestamp()))
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('type') == 1:
                        print("✅ Discord ping test passed")
                        return True
                    else:
                        print(f"❌ Discord ping wrong response: {data}")
                        return False
                else:
                    print(f"❌ Discord ping failed: {response.status}")
                    text = await response.text()
                    print(f"   Response: {text}")
                    return False
    except Exception as e:
        print(f"❌ Discord ping error: {e}")
        return False

async def test_button_interaction():
    """Test button interaction"""
    print("🔘 Testing button interaction...")
    
    button_payload = {
        "type": 3,  # MESSAGE_COMPONENT
        "id": "test-button-interaction",
        "application_id": "test-app-id",
        "token": "test-token",
        "channel_id": "1401451313216094383",
        "guild_id": "test-guild-id",
        "data": {
            "custom_id": "leaderboard_slot_test",
            "component_type": 2
        },
        "member": {
            "user": {
                "id": "123456789",
                "username": "TestUser",
                "discriminator": "0001"
            }
        },
        "message": {
            "id": "test-message-id"
        }
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                INTERACTION_SERVER_URL,
                json=button_payload,
                headers={
                    'Content-Type': 'application/json',
                    'X-Signature-Ed25519': 'test-signature',
                    'X-Signature-Timestamp': str(int(datetime.now().timestamp()))
                }
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('type') == 4:  # CHANNEL_MESSAGE_WITH_SOURCE
                        content = data.get('data', {}).get('content', '')
                        flags = data.get('data', {}).get('flags', 0)
                        
                        print("✅ Button interaction test passed")
                        print(f"   Response type: {data.get('type')}")
                        print(f"   Ephemeral flag: {flags == 64}")
                        print(f"   Content preview: {content[:100]}...")
                        return True
                    else:
                        print(f"❌ Button interaction wrong response type: {data}")
                        return False
                else:
                    print(f"❌ Button interaction failed: {response.status}")
                    text = await response.text()
                    print(f"   Response: {text}")
                    return False
    except Exception as e:
        print(f"❌ Button interaction error: {e}")
        return False

async def run_comprehensive_tests():
    """Run all interaction tests"""
    
    print("🧪 COMPREHENSIVE BUTTON INTERACTION TESTS")
    print("=" * 60)
    print()
    
    tests = [
        ("Health Check", test_health_check),
        ("CORS Preflight", test_cors_preflight),
        ("Discord Ping", test_discord_ping),
        ("Button Interaction", test_button_interaction)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"🔍 Running {test_name} test...")
        try:
            result = await test_func()
            results[test_name] = result
            print(f"{'✅' if result else '❌'} {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
        print()
    
    # Summary
    print("=" * 60)
    print("📊 TEST SUMMARY:")
    print()
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print()
    print(f"📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Button interactions are working correctly.")
    else:
        print("⚠️ Some tests failed. Check the Discord bridge configuration.")
    
    return passed == total

def create_test_message_file():
    """Create test message and add to queue"""
    print("📝 Creating test message with buttons...")
    
    message_id, test_message = create_test_leaderboard_message()
    
    # Write to queue file
    with open("discord_queue.json", "a", encoding='utf-8') as f:
        f.write(json.dumps(test_message, separators=(',', ':')) + "\n")
    
    print(f"✅ Test message created: {message_id}")
    print("📤 Message added to Discord queue for processing")
    
    return message_id

async def main():
    """Main test function"""
    
    print("🚀 DISCORD BUTTON INTERACTION TEST SUITE")
    print("=" * 60)
    print()
    
    # Create test message
    test_message_id = create_test_message_file()
    print()
    
    # Wait a moment for the Discord bridge to potentially process the message
    print("⏳ Waiting 3 seconds for Discord bridge to process message...")
    await asyncio.sleep(3)
    print()
    
    # Run interaction tests
    all_passed = await run_comprehensive_tests()
    
    print()
    print("=" * 60)
    print("🎯 NEXT STEPS:")
    print()
    
    if all_passed:
        print("✅ All interaction tests passed!")
        print("🔧 The button interaction system is working correctly")
        print("📱 Test the buttons in Discord to verify end-to-end functionality")
    else:
        print("⚠️ Some tests failed. Common issues:")
        print("   • Discord bridge not running")
        print("   • Interaction server not started")
        print("   • Port 8080 blocked or in use")
        print("   • CORS configuration issues")
    
    print()
    print("🔗 Configuration needed for production:")
    print("   • Set Discord application's Interactions Endpoint URL")
    print("   • Configure Discord public key for signature verification")
    print("   • Ensure server is accessible from Discord's servers")
    
    print()
    print(f"📋 Test message ID: {test_message_id}")
    print("🎉 Test suite completed!")

if __name__ == "__main__":
    asyncio.run(main())
