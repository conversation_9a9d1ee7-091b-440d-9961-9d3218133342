//MCCScript 1.0

/* Direct NBT Extractor - Bypasses method chain issues
 * This script directly accesses NBT data when inventory opens
 * without relying on complex method chains that may not execute
 */

MCC.LoadBot(new DirectNBTExtractorBot());

//MCCScript Extensions

public class DirectNBTExtractorBot : ChatBot
{
    private bool inventoryHandlingEnabled = false;
    private readonly int[] islandDataSlots = { 13, 21, 23, 29, 31 };
    
    public override void Initialize()
    {
        LogToConsole("=== Direct NBT Extractor Bot ===");
        LogToConsole("*** DIRECT APPROACH TO NBT LORE EXTRACTION ***");
        
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** CRITICAL: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
            return;
        }
        
        LogToConsole("This bot will directly extract NBT lore data when inventory opens");
        LogToConsole("Run /istop to test direct NBT extraction");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            
            // Immediate NBT extraction when inventory opens
            if (cleanText.Contains("Inventory") && cleanText.Contains("opened"))
            {
                LogToConsole("*** INVENTORY OPENED - IMMEDIATE NBT EXTRACTION ***");
                
                // Use a small delay to ensure inventory is fully loaded
                System.Threading.Thread.Sleep(100);
                
                ExtractNBTDataDirectly();
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error in GetText: {ex.Message}");
        }
    }
    
    private void ExtractNBTDataDirectly()
    {
        try
        {
            LogToConsole("*** STARTING DIRECT NBT EXTRACTION ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole("❌ Cannot extract - inventory handling disabled");
                return;
            }
            
            // Get inventories immediately
            var inventories = GetInventories();
            LogToConsole($"*** FOUND {inventories.Count} INVENTORIES ***");
            
            if (inventories.Count == 0)
            {
                LogToConsole("❌ No inventories found for direct extraction");
                return;
            }
            
            // Try all inventories, prioritizing #2
            Container targetInventory = null;
            int targetInventoryId = 0;
            
            if (inventories.ContainsKey(2))
            {
                targetInventory = inventories[2];
                targetInventoryId = 2;
                LogToConsole("*** USING INVENTORY #2 FOR DIRECT EXTRACTION ***");
            }
            else
            {
                var firstInventory = inventories.First();
                targetInventory = firstInventory.Value;
                targetInventoryId = firstInventory.Key;
                LogToConsole($"*** USING INVENTORY #{targetInventoryId} FOR DIRECT EXTRACTION ***");
            }
            
            LogToConsole($"Target inventory has {targetInventory.Items.Count} items");
            
            // Direct NBT extraction from island slots
            ExtractIslandNBTData(targetInventory, targetInventoryId);
            
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in direct NBT extraction: {ex.Message}");
            LogToConsole($"Stack trace: {ex.StackTrace}");
        }
    }
    
    private void ExtractIslandNBTData(Container inventory, int inventoryId)
    {
        try
        {
            LogToConsole($"*** EXTRACTING NBT DATA FROM INVENTORY #{inventoryId} ***");
            LogToConsole($"Checking slots: {string.Join(", ", islandDataSlots)}");
            
            int foundItems = 0;
            int extractedLore = 0;
            
            foreach (int slot in islandDataSlots)
            {
                LogToConsole($"=== DIRECT EXTRACTION FROM SLOT {slot} ===");
                
                if (inventory.Items.ContainsKey(slot))
                {
                    var item = inventory.Items[slot];
                    foundItems++;
                    
                    LogToConsole($"✅ ITEM FOUND IN SLOT {slot}");
                    LogToConsole($"Type: {item.Type}");
                    LogToConsole($"Display: '{item.DisplayName}'");
                    LogToConsole($"NBT Status: {(item.NBT == null ? "NULL" : $"{item.NBT.Count} entries")}");
                    
                    // Extract island name and ranking
                    string islandName = ExtractCleanIslandName(item.DisplayName);
                    int ranking = ExtractRanking(item.DisplayName);
                    
                    LogToConsole($"Island Name: '{islandName}'");
                    LogToConsole($"Ranking: #{ranking}");
                    
                    // Direct NBT lore extraction
                    if (item.NBT != null && item.NBT.Count > 0)
                    {
                        LogToConsole($"*** DIRECT NBT ANALYSIS FOR SLOT {slot} ***");
                        
                        var loreData = ExtractLoreDirectly(item.NBT);
                        if (loreData.Count > 0)
                        {
                            extractedLore++;
                            LogToConsole($"*** EXTRACTED {loreData.Count} LORE LINES FROM SLOT {slot} ***");
                            
                            for (int i = 0; i < loreData.Count; i++)
                            {
                                LogToConsole($"Lore[{i}]: '{loreData[i]}'");
                                
                                // Extract values from lore
                                var values = ExtractValuesFromLoreLine(loreData[i]);
                                if (values.Count > 0)
                                {
                                    LogToConsole($"*** VALUES FOUND IN LORE[{i}]: {string.Join(", ", values)} ***");
                                }
                            }
                            
                            // Display island data summary
                            DisplayIslandSummary(islandName, ranking, loreData);
                        }
                        else
                        {
                            LogToConsole($"❌ No lore data extracted from slot {slot}");
                        }
                    }
                    else
                    {
                        LogToConsole($"❌ No NBT data in slot {slot}");
                    }
                }
                else
                {
                    LogToConsole($"❌ Slot {slot} is empty");
                }
                
                LogToConsole($"=== END SLOT {slot} ===");
            }
            
            LogToConsole("*** DIRECT NBT EXTRACTION COMPLETE ***");
            LogToConsole($"Summary: Found {foundItems} items, extracted lore from {extractedLore} items");
            
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error extracting island NBT data: {ex.Message}");
        }
    }
    
    private List<string> ExtractLoreDirectly(Dictionary<string, object> nbt)
    {
        var loreLines = new List<string>();
        
        try
        {
            LogToConsole("*** DIRECT LORE EXTRACTION ***");
            
            // Check for display compound
            if (nbt.ContainsKey("display") && nbt["display"] is Dictionary<string, object> display)
            {
                LogToConsole("✅ Found display compound");
                
                // Check for Lore
                if (display.ContainsKey("Lore") && display["Lore"] is List<object> lore)
                {
                    LogToConsole($"✅ Found lore list with {lore.Count} entries");
                    
                    foreach (var loreItem in lore)
                    {
                        if (loreItem != null)
                        {
                            string loreText = loreItem.ToString();
                            // Remove Minecraft formatting codes
                            string cleanLore = RemoveFormatting(loreText);
                            loreLines.Add(cleanLore);
                        }
                    }
                }
                else
                {
                    LogToConsole("❌ No Lore found in display compound");
                    
                    // Log all display keys for debugging
                    LogToConsole("Display compound keys:");
                    foreach (var key in display.Keys)
                    {
                        LogToConsole($"  - {key}: {display[key]?.GetType().Name ?? "null"}");
                    }
                }
            }
            else
            {
                LogToConsole("❌ No display compound found");
                
                // Log all NBT keys for debugging
                LogToConsole("NBT keys:");
                foreach (var key in nbt.Keys)
                {
                    LogToConsole($"  - {key}: {nbt[key]?.GetType().Name ?? "null"}");
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"❌ Error in direct lore extraction: {ex.Message}");
        }
        
        return loreLines;
    }
    
    private List<string> ExtractValuesFromLoreLine(string loreLine)
    {
        var values = new List<string>();
        
        try
        {
            if (string.IsNullOrEmpty(loreLine))
                return values;
            
            string lowerLine = loreLine.ToLower();
            
            // Look for money values ($1.78B, $979.33M, etc.)
            if (loreLine.Contains("$"))
            {
                var moneyMatches = System.Text.RegularExpressions.Regex.Matches(loreLine, @"[\+\-]?\$[\d,]+\.?\d*[KMBkmb]?");
                foreach (System.Text.RegularExpressions.Match match in moneyMatches)
                {
                    values.Add($"Money: {match.Value}");
                }
            }
            
            // Look for point values
            if (lowerLine.Contains("point"))
            {
                var pointMatches = System.Text.RegularExpressions.Regex.Matches(loreLine, @"[\d,]+\s*points?");
                foreach (System.Text.RegularExpressions.Match match in pointMatches)
                {
                    values.Add($"Points: {match.Value}");
                }
            }
            
            // Look for percentage values
            if (loreLine.Contains("%"))
            {
                var percentMatches = System.Text.RegularExpressions.Regex.Matches(loreLine, @"[\+\-]?[\d,]+\.?\d*%");
                foreach (System.Text.RegularExpressions.Match match in percentMatches)
                {
                    values.Add($"Percent: {match.Value}");
                }
            }
            
            // Look for numeric values with units
            var numericMatches = System.Text.RegularExpressions.Regex.Matches(loreLine, @"[\d,]+\.?\d*[KMBkmb]");
            foreach (System.Text.RegularExpressions.Match match in numericMatches)
            {
                if (!values.Any(v => v.Contains(match.Value))) // Avoid duplicates
                {
                    values.Add($"Numeric: {match.Value}");
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"Error extracting values from lore line: {ex.Message}");
        }
        
        return values;
    }
    
    private void DisplayIslandSummary(string islandName, int ranking, List<string> loreLines)
    {
        try
        {
            LogToConsole("*** ISLAND DATA SUMMARY ***");
            LogToConsole($"Island: {islandName}");
            LogToConsole($"Rank: #{ranking}");
            LogToConsole($"Lore Lines: {loreLines.Count}");
            
            foreach (var line in loreLines)
            {
                LogToConsole($"  - {line}");
            }
            
            LogToConsole("*** END SUMMARY ***");
        }
        catch (Exception ex)
        {
            LogToConsole($"Error displaying island summary: {ex.Message}");
        }
    }
    
    private string ExtractCleanIslandName(string displayName)
    {
        if (string.IsNullOrEmpty(displayName))
            return "";
        
        string clean = RemoveFormatting(displayName);
        
        // Extract from "#1: Revenants (0 points)" format
        if (clean.Contains("#") && clean.Contains(":"))
        {
            int colonIndex = clean.IndexOf(":");
            if (colonIndex > 0 && colonIndex < clean.Length - 1)
            {
                string afterColon = clean.Substring(colonIndex + 1).Trim();
                int openParen = afterColon.IndexOf("(");
                if (openParen > 0)
                {
                    afterColon = afterColon.Substring(0, openParen).Trim();
                }
                return afterColon;
            }
        }
        
        return clean;
    }
    
    private int ExtractRanking(string displayName)
    {
        if (string.IsNullOrEmpty(displayName))
            return 0;
        
        string clean = RemoveFormatting(displayName);
        
        if (clean.StartsWith("#"))
        {
            int colonIndex = clean.IndexOf(":");
            if (colonIndex > 1)
            {
                string rankText = clean.Substring(1, colonIndex - 1).Trim();
                if (int.TryParse(rankText, out int rank))
                {
                    return rank;
                }
            }
        }
        
        return 0;
    }
    
    private string RemoveFormatting(string text)
    {
        if (string.IsNullOrEmpty(text))
            return "";
        
        string result = text;
        while (result.Contains("§") && result.Length > result.IndexOf("§") + 1)
        {
            int formatIndex = result.IndexOf("§");
            if (formatIndex < result.Length - 1)
            {
                result = result.Remove(formatIndex, 2);
            }
            else
            {
                result = result.Remove(formatIndex, 1);
            }
        }
        return result;
    }
    
    public override void Update()
    {
        // Minimal update
    }
}
