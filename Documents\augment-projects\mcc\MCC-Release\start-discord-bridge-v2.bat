@echo off
echo ========================================
echo MCC Discord Bridge Bot v2.0
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

REM Check if required packages are installed
echo Checking required packages...
python -c "import aiohttp, json, asyncio" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    pip install aiohttp
    if errorlevel 1 (
        echo ERROR: Failed to install required packages
        pause
        exit /b 1
    )
)

echo ✅ All dependencies are ready
echo.

REM Check if MCC bot files exist
if not exist "multi-phase-bot.cs" (
    echo WARNING: multi-phase-bot.cs not found
    echo Make sure you're running this from the MCC folder
)

if not exist "discord-bridge-v2.py" (
    echo ERROR: discord-bridge-v2.py not found
    echo This file should be in the same folder as this batch file
    pause
    exit /b 1
)

echo ========================================
echo Starting Discord Bridge Bot v2.0...
echo ========================================
echo.
echo 📁 Monitoring files:
echo   - discord_queue.json (MCC bot output)
echo   - discord_embeds.json (configuration)
echo.
echo 🎯 Target Discord channels:
echo   - ISTOP: 1401451296711507999
echo   - LB: 1401451313216094383
echo.
echo 💡 Instructions:
echo   1. Keep this window open
echo   2. Start MCC bot: /script multi-phase-bot
echo   3. Bot will automatically send Discord messages
echo.
echo Press Ctrl+C to stop the bot
echo.

REM Start the Discord bridge bot
python discord-bridge-v2.py

echo.
echo ========================================
echo Discord Bridge Bot v2.0 stopped
echo ========================================
pause
