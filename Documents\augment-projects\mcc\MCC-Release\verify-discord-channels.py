#!/usr/bin/env python3
"""
Discord Channel Verification Script
Helps you find the correct channel IDs in your Discord server
"""

import asyncio
import discord
from discord.ext import commands

# Your Discord credentials
DISCORD_BOT_TOKEN = "MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.GGZ14v.N21m2G5muOQwLtB9QqVci4iPFzGI_Y-1quRj6k"
TARGET_GUILD_ID = 1354262430221340874  # Your Guild ID

class ChannelVerifier(commands.Bot):
    """Bot to verify channel access and find correct IDs"""
    
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        super().__init__(command_prefix='!', intents=intents)
    
    async def on_ready(self):
        """Called when bot connects"""
        print(f"\n🤖 {self.user} connected to Discord!")
        print(f"🔗 Connected to {len(self.guilds)} server(s)")
        
        # Find your specific server
        target_guild = self.get_guild(TARGET_GUILD_ID)
        
        if not target_guild:
            print(f"\n❌ ERROR: Bot is not in server with ID {TARGET_GUILD_ID}")
            print("🔗 Please use this link to add the bot to your server:")
            print(f"https://discord.com/api/oauth2/authorize?client_id=1401449503609065493&permissions=274877908032&scope=bot%20applications.commands")
            print("\nAfter adding the bot, run this script again.")
            await self.close()
            return
        
        print(f"\n✅ Found target server: '{target_guild.name}'")
        print(f"👥 Members: {target_guild.member_count}")
        print(f"📁 Channels: {len(target_guild.channels)}")
        
        # List all channels
        print(f"\n📋 ALL CHANNELS IN '{target_guild.name}':")
        print("=" * 60)
        
        text_channels = []
        voice_channels = []
        categories = []
        
        for channel in target_guild.channels:
            if isinstance(channel, discord.TextChannel):
                text_channels.append(channel)
            elif isinstance(channel, discord.VoiceChannel):
                voice_channels.append(channel)
            elif isinstance(channel, discord.CategoryChannel):
                categories.append(channel)
        
        # Show categories
        if categories:
            print("\n📂 CATEGORIES:")
            for category in categories:
                print(f"   📂 {category.name} (ID: {category.id})")
        
        # Show text channels
        if text_channels:
            print("\n💬 TEXT CHANNELS:")
            for channel in text_channels:
                category_name = channel.category.name if channel.category else "No Category"
                permissions = channel.permissions_for(target_guild.me)
                can_send = "✅" if permissions.send_messages else "❌"
                can_read = "✅" if permissions.read_messages else "❌"
                
                print(f"   {can_send} #{channel.name}")
                print(f"      ID: {channel.id}")
                print(f"      Category: {category_name}")
                print(f"      Permissions: Read {can_read} | Send {can_send}")
                print()
        
        # Show voice channels
        if voice_channels:
            print("🔊 VOICE CHANNELS:")
            for channel in voice_channels:
                category_name = channel.category.name if channel.category else "No Category"
                print(f"   🔊 {channel.name} (ID: {channel.id}) - {category_name}")
        
        # Check specific channels you're looking for
        print("\n🎯 CHECKING TARGET CHANNELS:")
        print("=" * 60)
        
        target_channels = {
            "ISTOP": 1401451296711507999,
            "LB": 1401451313216094383
        }
        
        for channel_name, channel_id in target_channels.items():
            channel = target_guild.get_channel(channel_id)
            if channel:
                permissions = channel.permissions_for(target_guild.me)
                can_send = permissions.send_messages
                can_read = permissions.read_messages
                
                print(f"✅ {channel_name}: Found '#{channel.name}'")
                print(f"   ID: {channel_id}")
                print(f"   Can read: {'✅' if can_read else '❌'}")
                print(f"   Can send: {'✅' if can_send else '❌'}")
                
                if not can_read or not can_send:
                    print(f"   ⚠️ Missing permissions! Check channel settings.")
            else:
                print(f"❌ {channel_name}: Channel ID {channel_id} not found in this server")
        
        # Suggest channels that might be what you're looking for
        print("\n💡 SUGGESTED CHANNELS FOR MCC BOT:")
        print("=" * 60)
        
        keywords = ['island', 'top', 'leaderboard', 'lb', 'istop', 'mcc', 'minecraft']
        suggestions = []
        
        for channel in text_channels:
            channel_name_lower = channel.name.lower()
            for keyword in keywords:
                if keyword in channel_name_lower:
                    permissions = channel.permissions_for(target_guild.me)
                    if permissions.send_messages and permissions.read_messages:
                        suggestions.append((channel, keyword))
                    break
        
        if suggestions:
            print("These channels might be good for your MCC bot:")
            for channel, keyword in suggestions:
                print(f"   💡 #{channel.name} (ID: {channel.id}) - matches '{keyword}'")
        else:
            print("No channels found matching common MCC keywords.")
            print("You may need to create channels or check channel names.")
        
        print(f"\n🔧 CONFIGURATION UPDATE:")
        print("=" * 60)
        print("Update your bot configuration with these channel IDs:")
        print("CHANNELS = {")
        
        # Try to find the best matches
        istop_channel = None
        lb_channel = None
        
        for channel in text_channels:
            name_lower = channel.name.lower()
            if 'istop' in name_lower or ('island' in name_lower and 'top' in name_lower):
                istop_channel = channel
            elif 'lb' in name_lower or 'leaderboard' in name_lower:
                lb_channel = channel
        
        if istop_channel:
            print(f'    "ISTOP": {istop_channel.id},  # #{istop_channel.name}')
        else:
            print(f'    "ISTOP": YOUR_ISTOP_CHANNEL_ID,  # Update with correct ID')
        
        if lb_channel:
            print(f'    "LB": {lb_channel.id}      # #{lb_channel.name}')
        else:
            print(f'    "LB": YOUR_LB_CHANNEL_ID      # Update with correct ID')
        
        print("}")
        
        print(f"\n✅ Channel verification complete!")
        print("Update your bot configuration and restart it.")
        
        await self.close()

async def main():
    """Main function"""
    print("🔍 Discord Channel Verification Tool")
    print("=" * 50)
    print(f"🎯 Target Guild ID: {TARGET_GUILD_ID}")
    print("🔗 Checking bot access and finding channel IDs...")
    print("=" * 50)
    
    bot = ChannelVerifier()
    
    try:
        await bot.start(DISCORD_BOT_TOKEN)
    except discord.LoginFailure:
        print("❌ ERROR: Invalid bot token!")
        print("Please check your DISCORD_BOT_TOKEN")
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    asyncio.run(main())
