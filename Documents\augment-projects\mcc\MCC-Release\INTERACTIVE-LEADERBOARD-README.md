# 🏆 Interactive Leaderboard System

## Overview
The Multi-Phase Bot now features an interactive leaderboard system that displays player rankings with clickable buttons for different categories.

## Features

### 📊 Main Leaderboard Display
- **Title**: "Top 10 Players" (configurable to "Top 12 Players" if needed)
- **Format**: `#1: PlayerName - Score/Value`
- **Content**: Shows top 10 players from the first available category
- **Layout**: Full-width display (not inline columns)

### 🔘 Interactive Buttons
- **Button Row**: Displays below the main embed
- **Categories**: Each button represents a leaderboard category
- **Labels**: Shortened names (e.g., "Mobs", "Runes", "Pouches", "Votes")
- **Functionality**: Click to view top 10 players for that specific category

### 📋 Supported Categories
- **Mobs Killed** → "Mobs" button
- **Runes Identified** → "Runes" button  
- **Pouches Opened** → "Pouches" button
- **Votes** → "Votes" button
- **Quests Completed** → "Quests" button
- **Experience Gained** → "XP" button
- **Blocks Placed** → "Blocks" button
- **<PERSON>augh<PERSON>** → "Fishing" button
- **Items Sold** → "Sales" button
- **Money Earned** → "Money" button

## Implementation Details

### 🔧 Code Changes Made

#### 1. Enhanced `CreateLeaderboardEmbed()` Method
```csharp
- Shows top 10 players from first available category
- Adds interactive button components
- Uses full-width display format
- Updated title and description
```

#### 2. Added `CreateLeaderboardButtons()` Method
```csharp
- Generates Discord button components
- Handles up to 25 buttons (5 rows × 5 buttons)
- Uses category slot as unique button ID
- Shortens category names for button labels
```

#### 3. Added `CreateCategorySpecificEmbed()` Method
```csharp
- Creates category-specific top 10 player displays
- Triggered by button interactions
- Shows detailed rankings for selected category
```

#### 4. Added Button Interaction Handler
```csharp
- `HandleLeaderboardButtonInteraction()` method
- Processes button clicks by custom_id
- Returns appropriate category-specific embed
```

### 📊 Data Structure
The existing `LeaderboardCategory` and `PlayerRanking` classes support the new format:

```csharp
public class LeaderboardCategory
{
    public int Slot { get; set; }
    public string CategoryName { get; set; }
    public List<PlayerRanking> Rankings { get; set; }
    public DateTime ExtractedAt { get; set; }
}

public class PlayerRanking
{
    public int Rank { get; set; }
    public string PlayerName { get; set; }
    public string Value { get; set; }
    public int Points { get; set; }
}
```

## Discord Integration

### 🔗 Button Interaction Flow
1. **Main Embed**: Shows overview with buttons
2. **Button Click**: User clicks category button
3. **Interaction**: Discord sends button interaction to bridge
4. **Response**: Bot returns category-specific embed
5. **Display**: Discord shows top 10 for that category

### 📝 Button Custom IDs
- **Real Data**: `leaderboard_{slot}` (e.g., `leaderboard_13`)
- **Sample Data**: `sample_{category}` (e.g., `sample_mobs`)

### 🎨 Discord Embed Format
```json
{
  "embeds": [{
    "title": "🏆 Top 10 Players",
    "description": "Player rankings extracted at 14:30:25\n*Click buttons below to view different categories*",
    "fields": [{
      "name": "🏆 Mobs Killed",
      "value": "#1: PlayerName - 1,234 mobs\n#2: PlayerName - 1,156 mobs\n...",
      "inline": false
    }]
  }],
  "components": [{
    "type": 1,
    "components": [
      {"type": 2, "style": 2, "label": "Mobs", "custom_id": "leaderboard_13"},
      {"type": 2, "style": 2, "label": "Runes", "custom_id": "leaderboard_15"}
    ]
  }]
}
```

## Testing

### 🧪 Test Script
Run `test-leaderboard-interactive.py` to verify:
- Interactive button presence
- Player ranking format
- Button custom IDs
- Embed structure

### ✅ Expected Results
- Title shows "Top 10 Players"
- Rankings show "#1: PlayerName - Score" format
- Buttons appear below embed
- Button clicks show category-specific data

## Configuration

### 📝 Title Customization
To change from "Top 10 Players" to "Top 12 Players":
```csharp
title = "🏆 Top 12 Players"
```

### 🔢 Player Count Adjustment
To show 12 players instead of 10:
```csharp
for (int i = 0; i < Math.Min(12, category.Rankings.Count); i++)
```

### 🏷️ Button Label Customization
Modify the `ShortenCategoryName()` method to change button labels.

## Troubleshooting

### ❌ No Buttons Showing
- Check Discord bridge supports button components
- Verify `components` array in embed JSON
- Ensure button type = 2 and action row type = 1

### ❌ No Player Rankings
- Verify Phase 3 leaderboard extraction captures player data
- Check `PlayerRanking` objects are populated
- Ensure `Rankings` list contains data

### ❌ Button Interactions Not Working
- Implement Discord interaction webhook handling
- Use `HandleLeaderboardButtonInteraction()` method
- Process button `custom_id` to determine category

## Future Enhancements

### 🚀 Potential Improvements
- **Pagination**: Handle more than 10 players
- **Filtering**: Filter by time period or criteria
- **Sorting**: Different sorting options
- **Ephemeral Responses**: User-specific button responses
- **Auto-refresh**: Periodic button embed updates

### 🔧 Discord Bridge Requirements
The Discord bridge needs to:
1. Support Discord button components
2. Handle button interaction webhooks
3. Call `HandleLeaderboardButtonInteraction()` method
4. Send ephemeral responses for button clicks
