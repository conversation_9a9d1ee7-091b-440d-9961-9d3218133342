//MCCScript 1.0

/* Test script for inventory timing and detection debugging
 * This script focuses on the exact timing issue where inventory opens
 * while the bot is in the wrong state
 */

MCC.LoadBot(new InventoryTimingTestBot());

//MCCScript Extensions

public class InventoryTimingTestBot : ChatBot
{
    private int messageCount = 0;
    private bool inventoryHandlingEnabled = false;
    private DateTime lastInventoryCheck = DateTime.MinValue;
    
    public override void Initialize()
    {
        LogToConsole("=== Inventory Timing Test Bot ===");
        LogToConsole("*** DEBUGGING INVENTORY DETECTION TIMING ***");
        
        // Check if inventory handling is enabled
        inventoryHandlingEnabled = GetInventoryEnabled();
        LogToConsole($"Inventory Handling Enabled: {inventoryHandlingEnabled}");
        
        if (!inventoryHandlingEnabled)
        {
            LogToConsole("*** CRITICAL: INVENTORY HANDLING IS DISABLED! ***");
            LogToConsole("Enable it in MinecraftClient.ini: InventoryHandling = true");
        }
        
        LogToConsole("This bot will:");
        LogToConsole("1. Log EVERY message with detailed analysis");
        LogToConsole("2. Detect inventory messages immediately");
        LogToConsole("3. Check for open inventories periodically");
        LogToConsole("4. Show exact timing of inventory events");
        LogToConsole("Try running /istop to test the timing issue");
        LogToConsole("==========================================");
    }
    
    public override void GetText(string text)
    {
        try
        {
            messageCount++;
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Log EVERY message with timestamp
            DateTime now = DateTime.Now;
            LogToConsole($"[{now:HH:mm:ss.fff}] MSG #{messageCount}: '{text}'");
            LogToConsole($"[{now:HH:mm:ss.fff}] Clean: '{cleanText}'");
            
            // Check message type
            bool isChatMessage = IsChatMessage(cleanText, ref message, ref username);
            bool isPrivateMessage = IsPrivateMessage(cleanText, ref message, ref username);
            
            LogToConsole($"[{now:HH:mm:ss.fff}] Type: {(isChatMessage ? "CHAT" : isPrivateMessage ? "PM" : "SYSTEM/SERVER")}");
            
            // PRIORITY: Check for inventory messages
            if (cleanText.Contains("Inventory"))
            {
                LogToConsole($"[{now:HH:mm:ss.fff}] *** INVENTORY MESSAGE DETECTED ***");
                LogToConsole($"[{now:HH:mm:ss.fff}] *** THIS IS THE CRITICAL MESSAGE ***");
                LogToConsole($"[{now:HH:mm:ss.fff}] Raw: '{text}'");
                LogToConsole($"[{now:HH:mm:ss.fff}] Clean: '{cleanText}'");
                LogToConsole($"[{now:HH:mm:ss.fff}] Chat: {isChatMessage}, PM: {isPrivateMessage}");
                
                if (cleanText.Contains("opened"))
                {
                    LogToConsole($"[{now:HH:mm:ss.fff}] *** INVENTORY OPENED DETECTED ***");
                    HandleInventoryOpened(now);
                }
                else if (cleanText.Contains("closed"))
                {
                    LogToConsole($"[{now:HH:mm:ss.fff}] *** INVENTORY CLOSED DETECTED ***");
                }
                else
                {
                    LogToConsole($"[{now:HH:mm:ss.fff}] Other inventory message");
                }
            }
            
            // Check for /istop related messages
            if (cleanText.ToLower().Contains("istop") || cleanText.ToLower().Contains("island"))
            {
                LogToConsole($"[{now:HH:mm:ss.fff}] *** ISTOP-RELATED MESSAGE ***");
                LogToConsole($"[{now:HH:mm:ss.fff}] Text: '{cleanText}'");
            }
            
            // Check for "Unknown command" messages
            if (cleanText.Contains("Unknown command"))
            {
                LogToConsole($"[{now:HH:mm:ss.fff}] *** UNKNOWN COMMAND DETECTED ***");
                LogToConsole($"[{now:HH:mm:ss.fff}] This means /istop failed");
            }
            
            LogToConsole($"[{now:HH:mm:ss.fff}] --- End Message #{messageCount} ---");
            
        }
        catch (Exception ex)
        {
            LogToConsole($"[ERROR] Error processing message #{messageCount}: {ex.Message}");
        }
    }
    
    private void HandleInventoryOpened(DateTime timestamp)
    {
        try
        {
            LogToConsole($"[{timestamp:HH:mm:ss.fff}] *** PROCESSING INVENTORY OPENED ***");
            
            if (!inventoryHandlingEnabled)
            {
                LogToConsole($"[{timestamp:HH:mm:ss.fff}] ❌ Cannot access inventory - handling disabled");
                return;
            }
            
            LogToConsole($"[{timestamp:HH:mm:ss.fff}] ✅ Inventory handling enabled, checking inventories...");
            
            var inventories = GetInventories();
            LogToConsole($"[{timestamp:HH:mm:ss.fff}] Found {inventories.Count} open inventories");
            
            if (inventories.Count > 0)
            {
                LogToConsole($"[{timestamp:HH:mm:ss.fff}] *** SUCCESS: INVENTORY IS ACCESSIBLE ***");
                
                foreach (var kvp in inventories)
                {
                    var id = kvp.Key;
                    var inventory = kvp.Value;
                    LogToConsole($"[{timestamp:HH:mm:ss.fff}] Inventory {id}: {inventory.Items.Count} items, Type: {inventory.Type}");
                    
                    // Check for island data in expected slots
                    int[] islandSlots = { 13, 21, 23, 29, 31 };
                    int foundItems = 0;
                    
                    foreach (int slot in islandSlots)
                    {
                        if (inventory.Items.ContainsKey(slot))
                        {
                            var item = inventory.Items[slot];
                            foundItems++;
                            LogToConsole($"[{timestamp:HH:mm:ss.fff}] Slot {slot}: {item.Type} - '{item.DisplayName}'");
                        }
                    }
                    
                    LogToConsole($"[{timestamp:HH:mm:ss.fff}] Found {foundItems} items in island data slots");
                    
                    if (foundItems > 0)
                    {
                        LogToConsole($"[{timestamp:HH:mm:ss.fff}] *** THIS LOOKS LIKE ISLAND DATA! ***");
                    }
                }
            }
            else
            {
                LogToConsole($"[{timestamp:HH:mm:ss.fff}] ❌ No inventories found - timing issue?");
                LogToConsole($"[{timestamp:HH:mm:ss.fff}] The inventory might have opened and closed too quickly");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[{timestamp:HH:mm:ss.fff}] ❌ Error handling inventory opened: {ex.Message}");
        }
    }
    
    public override void Update()
    {
        static int updateCount = 0;
        updateCount++;
        
        // Check for open inventories every 50 ticks (approximately 5 seconds)
        if (updateCount % 50 == 0)
        {
            DateTime now = DateTime.Now;
            
            if (inventoryHandlingEnabled)
            {
                try
                {
                    var inventories = GetInventories();
                    if (inventories.Count > 0)
                    {
                        LogToConsole($"[{now:HH:mm:ss.fff}] [UPDATE] Found {inventories.Count} open inventories");
                        
                        foreach (var kvp in inventories)
                        {
                            var inventory = kvp.Value;
                            LogToConsole($"[{now:HH:mm:ss.fff}] [UPDATE] Inventory {kvp.Key}: {inventory.Items.Count} items");
                        }
                    }
                }
                catch (Exception ex)
                {
                    LogToConsole($"[{now:HH:mm:ss.fff}] [UPDATE] Error checking inventories: {ex.Message}");
                }
            }
        }
        
        // Log status every 200 ticks (approximately 20 seconds)
        if (updateCount % 200 == 0)
        {
            DateTime now = DateTime.Now;
            LogToConsole($"[{now:HH:mm:ss.fff}] [STATUS] Bot running - {messageCount} messages processed");
            LogToConsole($"[{now:HH:mm:ss.fff}] [STATUS] Inventory handling: {inventoryHandlingEnabled}");
        }
    }
}
