//MCCScript 1.0

/* Multi-Phase Bot - Complete 3-Phase Server Automation System
 * This bot implements a comprehensive multi-phase system for server automation
 *
 * Phase 1: Periodic /istop command with skyblock fallback
 * - Runs every 10 minutes (600 seconds)
 * - Sends /istop command
 * - If command fails, tries /skyblock then retries /istop
 * - <PERSON>les timing and state management properly
 *
 * Phase 2: Island data extraction from /istop GUI
 * - Extracts top 5 island data from inventory slots
 * - Tracks changes over time
 * - Saves historical data
 *
 * Phase 3: Leaderboard data extraction sequence
 * - Closes island inventory using MCC API CloseInventory() method
 * - Opens leaderboard with /lb command
 * - Extracts complete JSON/NBT data from all leaderboard items
 * - Comprehensive logging and error handling
 *
 * Phase 4: Discord integration with automated embed posting
 * - Sends structured embeds to Discord channels for each phase
 * - Updates embeds with real-time data from Phase 2 and Phase 3
 * - Handles Discord API rate limiting and error recovery
 * - Provides comprehensive status reporting and notifications
 */

MCC.LoadBot(new MultiPhaseBot());

//MCCScript Extensions

public class MultiPhaseBot : ChatBot
{
    // === CONFIGURATION ===
    private const int TASK_INTERVAL_SECONDS = 600; // 10 minutes
    private const int SKYBLOCK_LOAD_WAIT_TICKS = 40; // 4 seconds (30-50 ticks range)
    private const string UNKNOWN_COMMAND_RESPONSE = "Unknown command. Type \"/help\" for help.";
    private const bool ENABLE_DEBUG_LOGGING = true; // Set to false to disable debug logs
    
    // === STATE MANAGEMENT ===
    private enum BotState
    {
        Idle,
        WaitingForIstopResponse,
        ProcessingSkyblockLoad,
        WaitingForIstopRetry,
        WaitingForInventoryOpen,
        ExtractingIslandData,
        WaitingForInventoryClose,
        // Phase 3 states
        ClosingIslandInventory,
        WaitingForLeaderboardCommand,
        WaitingForLeaderboardOpen,
        ExtractingLeaderboardData,
        ClosingLeaderboardInventory,
        // Skyblock error handling
        WaitingForSkyblockCooldown,
        Error
    }
    
    private BotState currentState = BotState.Idle;
    private DateTime nextTaskRun = DateTime.Now;
    private DateTime stateChangeTime = DateTime.Now;
    private int tickCounter = 0;
    private int skyblockLoadWaitTicks = 0;
    private int retryAttempts = 0;
    private const int MAX_RETRY_ATTEMPTS = 3;
    
    // === PHASE MANAGEMENT ===
    private int currentPhase = 1;
    private bool phase1Enabled = true;
    private bool phase2Enabled = true;
    private bool phase3Enabled = true;
    private bool phase4Enabled = true; // Discord integration

    // === PHASE 4 DISCORD CONFIGURATION ===
    private const string DISCORD_BOT_TOKEN = "MTQwMTQ0OTUwMzYwOTA2NTQ5Mw.Gd7AJ8.w0YR7GFuBpMgbu5LqXys7iA1b-W3w7fIbC2wO4";
    private const string DISCORD_CLIENT_ID = "1401449503609065493";
    private const string ISTOP_CHANNEL_ID = "1401451296711507999";
    private const string LB_CHANNEL_ID = "1401451313216094383";

    // Discord state management
    private DateTime lastDiscordUpdate = DateTime.MinValue;
    private string lastIstopMessageId = "";
    private string lastIslandMessageId = "persistent-island-message-id"; // Persistent island message ID
    private DateTime lastIslandMessageTime = DateTime.MinValue;
    private string lastLeaderboardMessageId = "aaeda853-5c3d-44c9-9264-9ed9b7378356"; // Persistent leaderboard message ID
    private DateTime lastLeaderboardMessageTime = DateTime.MinValue;
    private bool discordConnectionActive = false;

    // Message update configuration
    private const int MESSAGE_UPDATE_THRESHOLD_MINUTES = 15;
    private const int INTERACTION_TIMEOUT_SECONDS = 3;

    // === 10-MINUTE SCHEDULING CONFIGURATION ===
    private bool schedulingEnabled = true;  // Enable automatic 10-minute cycles
    private const int CYCLE_INTERVAL_SECONDS = 600; // 10 minutes
    private System.Threading.Timer scheduledTimer;
    private int cycleCount = 0;
    private DateTime lastCycleTime = DateTime.MinValue;

    // === DEDICATED DISCORD OUTPUT FILE ===
    private const string DISCORD_OUTPUT_FILE = "discord_embeds.json";
    private const string DISCORD_QUEUE_FILE = "discord_queue.json";
    private readonly object fileWriteLock = new object();

    // === PHASE 2 DATA STRUCTURES ===
    private readonly int[] islandDataSlots = { 13, 21, 23, 29, 31 }; // Top 5 island slots
    private Dictionary<string, IslandData> currentIslandData = new Dictionary<string, IslandData>();
    private Dictionary<string, IslandData> previousIslandData = new Dictionary<string, IslandData>();
    private List<HistoricalIslandData> historicalIslandData = new List<HistoricalIslandData>();
    private DateTime lastDataExtraction = DateTime.MinValue;
    private const string HISTORICAL_DATA_FILE = "island_historical_data.json";

    // === STATIC DATA PERSISTENCE (shared across bot instances) ===
    private static Dictionary<string, IslandData> staticPreviousIslandData = new Dictionary<string, IslandData>();

    // === HISTORICAL DATA MANAGEMENT ===
    private readonly string historicalDataFile = "island_historical_data.json";
    private List<HistoricalIslandData> historicalData = new List<HistoricalIslandData>();
    private readonly int maxHistoricalDays = 30; // Keep 30 days of data

    // === PHASE 3 DATA STRUCTURES ===
    private Dictionary<string, object> leaderboardData = new Dictionary<string, object>();
    private Dictionary<string, LeaderboardCategory> leaderboardCategories = new Dictionary<string, LeaderboardCategory>();
    private DateTime lastLeaderboardExtraction = DateTime.MinValue;

    // Target slots for leaderboard data extraction
    private readonly int[] leaderboardSlots = { 4, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53 };

    public override void Initialize()
    {
        LogToConsole("=== MULTI-PHASE BOT INITIALIZATION ===");
        LogToConsole("Multi-Phase Bot initialized successfully");
        LogToConsole($"Phase 1 enabled: {phase1Enabled}");
        LogToConsole($"Phase 2 enabled: {phase2Enabled}");
        LogToConsole($"Phase 3 enabled: {phase3Enabled}");
        LogToConsole($"Phase 4 enabled: {phase4Enabled}");

        // Test monetary formatting
        if (ENABLE_DEBUG_LOGGING)
        {
            TestMonetaryFormatting();
        }

        // Load historical data for growth calculations
        LoadHistoricalData();

        if (phase4Enabled)
        {
            InitializeDiscordIntegration();
        }

        // Initialize 10-minute scheduling
        if (schedulingEnabled)
        {
            InitializeScheduling();
        }

        LogToConsole("Bot is now running in Idle state");
        LogToConsole("=== INITIALIZATION COMPLETE ===");
    }

    public override void Update()
    {
        tickCounter++;
        DateTime currentTime = DateTime.Now;
        
        // Phase 1: Periodic istop task
        if (phase1Enabled)
        {
            HandlePhase1(currentTime);
        }
        
        // Future phases can be added here
        // if (phase2Enabled) HandlePhase2(currentTime);
    }
    
    // === PHASE 1 & 2 METHODS ===
    
    private void HandlePhase1(DateTime currentTime)
    {
        switch (currentState)
        {
            case BotState.Idle:
                HandleIdleState(currentTime);
                break;
                
            case BotState.WaitingForIstopResponse:
                HandleWaitingForIstopResponse(currentTime);
                break;
                
            case BotState.ProcessingSkyblockLoad:
                HandleProcessingSkyblockLoad();
                break;
                
            case BotState.WaitingForIstopRetry:
                HandleWaitingForIstopRetry(currentTime);
                break;
                
            case BotState.WaitingForInventoryOpen:
                HandleWaitingForInventoryOpen(currentTime);
                break;

            case BotState.ExtractingIslandData:
                HandleExtractingIslandData(currentTime);
                break;

            case BotState.WaitingForInventoryClose:
                HandleWaitingForInventoryClose(currentTime);
                break;

            // Phase 3 states
            case BotState.ClosingIslandInventory:
                HandleClosingIslandInventory(currentTime);
                break;

            case BotState.WaitingForLeaderboardCommand:
                HandleWaitingForLeaderboardCommand(currentTime);
                break;

            case BotState.WaitingForLeaderboardOpen:
                HandleWaitingForLeaderboardOpen(currentTime);
                break;

            case BotState.ExtractingLeaderboardData:
                HandleExtractingLeaderboardData(currentTime);
                break;

            case BotState.ClosingLeaderboardInventory:
                HandleClosingLeaderboardInventory(currentTime);
                break;

            // Skyblock error handling
            case BotState.WaitingForSkyblockCooldown:
                HandleWaitingForSkyblockCooldown(currentTime);
                break;

            case BotState.Error:
                HandleErrorState(currentTime);
                break;
        }
    }
    
    private void HandleIdleState(DateTime currentTime)
    {
        if (nextTaskRun <= currentTime)
        {
            LogToConsole($"[Phase 1] Starting periodic task at {currentTime:HH:mm:ss}");
            SendIstopCommand();
        }
    }
    
    private void HandleWaitingForIstopResponse(DateTime currentTime)
    {
        // Timeout after 10 seconds if no response
        if ((currentTime - stateChangeTime).TotalSeconds > 10)
        {
            LogToConsole("[Phase 1] Timeout waiting for /istop response");

            // Skip Discord notification for /istop timeout to reduce channel clutter

            // If Phase 2 is enabled, assume /istop worked and wait for inventory
            if (phase2Enabled)
            {
                LogToConsole("[Phase 2] Assuming /istop worked, transitioning to wait for inventory...");
                ChangeState(BotState.WaitingForInventoryOpen);
            }
            else
            {
                LogToConsole("[Phase 1] Phase 2 disabled, returning to idle");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
    }
    
    private void HandleProcessingSkyblockLoad()
    {
        skyblockLoadWaitTicks++;
        
        if (skyblockLoadWaitTicks >= SKYBLOCK_LOAD_WAIT_TICKS)
        {
            LogToConsole($"[Phase 1] Skyblock load wait complete ({skyblockLoadWaitTicks} ticks), retrying /istop");
            skyblockLoadWaitTicks = 0;
            SendIstopCommand();
            ChangeState(BotState.WaitingForIstopRetry);
        }
    }
    
    private void HandleWaitingForIstopRetry(DateTime currentTime)
    {
        // Timeout after 10 seconds if no response
        if ((currentTime - stateChangeTime).TotalSeconds > 10)
        {
            LogToConsole("[Phase 1] Timeout waiting for /istop retry response");

            // If Phase 2 is enabled, assume /istop worked and wait for inventory
            if (phase2Enabled)
            {
                LogToConsole("[Phase 2] Assuming /istop worked, transitioning to wait for inventory...");
                ChangeState(BotState.WaitingForInventoryOpen);
            }
            else
            {
                LogToConsole("[Phase 1] Phase 2 disabled, returning to idle");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
    }

    private void HandleWaitingForSkyblockCooldown(DateTime currentTime)
    {
        // Wait for 60 seconds before retrying Skyblock command
        double elapsedSeconds = (currentTime - stateChangeTime).TotalSeconds;

        if (elapsedSeconds >= 60)
        {
            LogToConsole("[Phase 1] Skyblock cooldown complete, retrying /skyblock command");
            SendText("/skyblock");
            ChangeState(BotState.ProcessingSkyblockLoad);
        }
        else
        {
            // Log progress every 10 seconds
            if (((int)elapsedSeconds) % 10 == 0 && tickCounter % 200 == 0)
            {
                int remainingSeconds = 60 - (int)elapsedSeconds;
                LogToConsole($"[Phase 1] Skyblock cooldown: {remainingSeconds} seconds remaining");
            }
        }
    }

    private void HandleErrorState(DateTime currentTime)
    {
        // Wait 30 seconds before returning to idle
        if ((currentTime - stateChangeTime).TotalSeconds > 30)
        {
            LogToConsole("[Phase 1] Error state timeout, returning to idle");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }
    
    // === PHASE 2 METHODS ===
    
    private void HandleWaitingForInventoryOpen(DateTime currentTime)
    {
        // Check if inventory is available
        if (GetInventoryEnabled())
        {
            var inventories = GetInventories();
            if (inventories != null && inventories.Count > 1)
            {
                LogToConsole($"[Phase 2] Inventory opened with {inventories.Count} containers");
                ChangeState(BotState.ExtractingIslandData);
                return;
            }
        }
        
        // Timeout after 15 seconds
        if ((currentTime - stateChangeTime).TotalSeconds > 15)
        {
            LogToConsole("[Phase 2] Timeout waiting for inventory to open");

            // Check if Phase 3 is enabled
            if (phase3Enabled)
            {
                LogToConsole("[Phase 3] Phase 3 enabled, transitioning to Phase 3...");
                ChangeState(BotState.ClosingIslandInventory);
            }
            else
            {
                LogToConsole("[Phase 2] Phase 3 disabled, returning to idle state");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
    }

    private void HandleExtractingIslandData(DateTime currentTime)
    {
        try
        {
            LogToConsole("[Phase 2] Starting island data extraction...");
            ExtractIslandData();

            LogToConsole("[Phase 2] Island data extraction completed");

            // Phase 4: Send Discord notification with island data
            if (phase4Enabled)
            {
                if (currentIslandData.Count > 0)
                {
                    LogToConsole($"[Phase 2] Sending real island data embed ({currentIslandData.Count} islands)");
                    SendIslandEmbed(CreateIslandDataEmbed());
                }
                else
                {
                    LogToConsole("[Phase 2] No real island data found, generating sample data for Discord");
                    SendIslandEmbed(CreateSampleIslandDataEmbed());
                }
            }

            // Check if Phase 3 is enabled
            if (phase3Enabled)
            {
                LogToConsole("[Phase 3] Phase 3 enabled, transitioning to Phase 3...");
                ChangeState(BotState.ClosingIslandInventory);
            }
            else
            {
                LogToConsole("[Phase 2] Phase 3 disabled, waiting for inventory to close...");
                ChangeState(BotState.WaitingForInventoryClose);
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error extracting island data: {ex.Message}");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }

    private void HandleWaitingForInventoryClose(DateTime currentTime)
    {
        // Check if inventory is closed
        if (GetInventoryEnabled())
        {
            var inventories = GetInventories();
            if (inventories == null || inventories.Count <= 1)
            {
                LogToConsole("[Phase 2] Inventory closed naturally");

                // Check if Phase 3 is enabled
                if (phase3Enabled)
                {
                    LogToConsole("[Phase 3] Phase 3 enabled, transitioning to Phase 3...");
                    ChangeState(BotState.ClosingIslandInventory);
                }
                else
                {
                    LogToConsole("[Phase 2] Phase 3 disabled, returning to idle state");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                return;
            }
        }
        
        // Timeout after 30 seconds
        if ((currentTime - stateChangeTime).TotalSeconds > 30)
        {
            LogToConsole("[Phase 2] Timeout waiting for inventory to close");

            // Check if Phase 3 is enabled
            if (phase3Enabled)
            {
                LogToConsole("[Phase 3] Phase 3 enabled, transitioning to Phase 3...");
                ChangeState(BotState.ClosingIslandInventory);
            }
            else
            {
                LogToConsole("[Phase 2] Phase 3 disabled, returning to idle state");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
    }

    // === PHASE 3 METHODS ===

    private void HandleClosingIslandInventory(DateTime currentTime)
    {
        try
        {
            LogToConsole("[Phase 3] Closing island inventory and preparing for leaderboard command...");

            // Find the island inventory to close
            if (GetInventoryEnabled())
            {
                var inventories = GetInventories();
                bool inventoryClosed = false;

                foreach (var kvp in inventories)
                {
                    int inventoryId = kvp.Key;
                    var container = kvp.Value;

                    // Close any non-player inventory (island inventory)
                    if (container.Type != ContainerType.PlayerInventory)
                    {
                        LogToConsole($"[Phase 3] Closing inventory #{inventoryId} using MCC API CloseInventory() method");
                        bool success = CloseInventory(inventoryId);

                        if (success)
                        {
                            LogToConsole($"[Phase 3] Successfully closed inventory #{inventoryId}");
                            inventoryClosed = true;
                        }
                        else
                        {
                            LogToConsole($"[Phase 3] Failed to close inventory #{inventoryId}, trying next inventory");
                        }
                    }
                }

                if (!inventoryClosed)
                {
                    LogToConsole("[Phase 3] No island inventory found to close, proceeding to leaderboard command");
                }
            }
            else
            {
                LogToConsole("[Phase 3] Inventory handling disabled, cannot close inventory");
            }

            // Wait 1-2 seconds for inventory to close
            System.Threading.Thread.Sleep(1500);

            LogToConsole("[Phase 3] Inventory closure completed, transitioning to leaderboard command...");
            ChangeState(BotState.WaitingForLeaderboardCommand);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error closing island inventory: {ex.Message}");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }

    private void HandleWaitingForLeaderboardCommand(DateTime currentTime)
    {
        try
        {
            LogToConsole("[Phase 3] Sending /lb command to open leaderboard...");
            SendText("/lb");

            LogToConsole("[Phase 3] Sent /lb command, waiting for leaderboard to open");
            ChangeState(BotState.WaitingForLeaderboardOpen);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error sending /lb command: {ex.Message}");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }

    private void HandleWaitingForLeaderboardOpen(DateTime currentTime)
    {
        try
        {
            // Check if leaderboard inventory is available
            if (GetInventoryEnabled())
            {
                var inventories = GetInventories();
                if (inventories != null && inventories.Count > 1)
                {
                    LogToConsole($"[Phase 3] Leaderboard inventory opened with {inventories.Count} containers");

                    // Wait 2-3 seconds for leaderboard interface to fully load
                    System.Threading.Thread.Sleep(2500);

                    ChangeState(BotState.ExtractingLeaderboardData);
                    return;
                }
            }

            // Timeout after 15 seconds
            if ((currentTime - stateChangeTime).TotalSeconds > 15)
            {
                LogToConsole("[Phase 3] Timeout waiting for leaderboard to open");
                LogToConsole("[Phase 3] Returning to idle state");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error waiting for leaderboard to open: {ex.Message}");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }

    private void HandleExtractingLeaderboardData(DateTime currentTime)
    {
        try
        {
            LogToConsole("[Phase 3] Starting leaderboard data extraction...");
            ExtractLeaderboardData();

            LogToConsole("[Phase 3] Leaderboard data extraction completed");

            // Phase 4: Send Discord notification with leaderboard data
            if (phase4Enabled)
            {
                if (leaderboardCategories.Count > 0)
                {
                    LogToConsole($"[Phase 3] Sending real leaderboard data embed ({leaderboardCategories.Count} categories)");
                    SendLeaderboardEmbed(CreateLeaderboardEmbed());
                }
                else
                {
                    LogToConsole("[Phase 3] No real leaderboard data found, generating sample data for Discord");
                    SendLeaderboardEmbed(CreateSampleLeaderboardDataEmbed());
                }
            }

            LogToConsole("[Phase 3] Transitioning to close leaderboard inventory");
            ChangeState(BotState.ClosingLeaderboardInventory);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error extracting leaderboard data: {ex.Message}");
            LogToConsole("[Phase 3] Transitioning to close leaderboard inventory after error");
            ChangeState(BotState.ClosingLeaderboardInventory);
        }
    }

    private void HandleClosingLeaderboardInventory(DateTime currentTime)
    {
        try
        {
            LogToConsole("[Phase 3] Closing leaderboard inventory and preparing for cycle completion...");

            bool inventoryClosed = false;

            // Get all open inventories
            var inventories = GetInventories();
            LogToConsole($"[Phase 3] Found {inventories.Count} open inventories");

            foreach (var kvp in inventories)
            {
                int inventoryId = kvp.Key;
                var container = kvp.Value;

                // Close any non-player inventory (leaderboard inventory)
                if (container.Type != ContainerType.PlayerInventory)
                {
                    LogToConsole($"[Phase 3] Closing leaderboard inventory #{inventoryId} using MCC API CloseInventory() method");
                    bool success = CloseInventory(inventoryId);

                    if (success)
                    {
                        LogToConsole($"[Phase 3] Successfully closed leaderboard inventory #{inventoryId}");
                        inventoryClosed = true;
                    }
                    else
                    {
                        LogToConsole($"[Phase 3] Failed to close leaderboard inventory #{inventoryId}");
                    }
                }
            }

            if (!inventoryClosed)
            {
                LogToConsole("[Phase 3] No leaderboard inventory found to close, or already closed");
            }

            LogToConsole("[Phase 3] Leaderboard inventory closure completed, returning to idle state");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error closing leaderboard inventory: {ex.Message}");
            LogToConsole("[Phase 3] Forcing return to idle state");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }

    // === UTILITY METHODS ===

    private void SendIstopCommand()
    {
        LogToConsole("[Phase 1] Sending /istop command");
        SendText("/istop");
        ChangeState(BotState.WaitingForIstopResponse);

        // Skip Discord notification for /istop command to reduce channel clutter
    }

    private void ChangeState(BotState newState)
    {
        if (currentState != newState)
        {
            LogToConsole($"[State] {currentState} -> {newState}");
            currentState = newState;
            stateChangeTime = DateTime.Now;
        }
    }

    private void ScheduleNextTask()
    {
        nextTaskRun = DateTime.Now.AddSeconds(TASK_INTERVAL_SECONDS);
        LogToConsole($"[Phase 1] Next task scheduled for {nextTaskRun:HH:mm:ss}");

        // Phase 4: Send Discord notification for task completion
        if (phase4Enabled)
        {
            // If we're in a scheduled cycle, send cycle completion embed
            if (cycleCount > 0 && lastCycleTime != DateTime.MinValue)
            {
                DateTime cycleEndTime = DateTime.Now;
                TimeSpan cycleDuration = cycleEndTime - lastCycleTime;

                LogToConsole($"✅ === SCHEDULED 10-MINUTE CYCLE #{cycleCount} COMPLETE at {cycleEndTime:HH:mm:ss} ===");
                LogToConsole($"[Cycle {cycleCount}] Duration: {cycleDuration.TotalSeconds:F1} seconds");
                LogToConsole($"[Cycle {cycleCount}] ⏰ Next cycle scheduled for {cycleEndTime.AddMinutes(10):HH:mm:ss}");

                // Skip Discord notification about cycle completion to reduce channel clutter
            }
            else
            {
                // Regular task completion (non-scheduled)
                string completionMessage = "✅ Multi-Phase Task Completed Successfully";
                string details = $"All enabled phases completed. Next task scheduled for {nextTaskRun:HH:mm:ss}";

                // Add data summary if available
                if (currentIslandData.Count > 0 || leaderboardCategories.Count > 0)
                {
                    details += $"\n\n📊 Data Summary:\n• Islands tracked: {currentIslandData.Count}\n• Leaderboard categories: {leaderboardCategories.Count}";
                }

                // Skip task completion status message to reduce channel clutter
            }
        }
    }

    // === CHAT MESSAGE HANDLING ===

    public override void GetText(string text)
    {
        text = GetVerbatim(text);

        // Handle unknown command response
        if (text.Contains(UNKNOWN_COMMAND_RESPONSE))
        {
            if (currentState == BotState.WaitingForIstopResponse)
            {
                LogToConsole("[Phase 1] /istop command not recognized, trying /skyblock");

                // Skip Discord notification for /istop command failure to reduce channel clutter

                SendText("/skyblock");
                ChangeState(BotState.ProcessingSkyblockLoad);
            }
            else if (currentState == BotState.WaitingForIstopRetry)
            {
                LogToConsole("[Phase 1] /istop retry failed, scheduling next task");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }

        // Handle skyblock-related messages
        if (text.Contains("You are not in SkyBlock!") || text.Contains("not in skyblock"))
        {
            if (currentState == BotState.ProcessingSkyblockLoad)
            {
                LogToConsole("[Phase 1] Not in SkyBlock, retrying /istop");
                SendIstopCommand();
                ChangeState(BotState.WaitingForIstopRetry);
            }
        }

        // Handle skyblock cooldown messages
        if (text.Contains("You must wait") && text.Contains("before using this command again"))
        {
            LogToConsole("[Phase 1] Skyblock command on cooldown, waiting...");
            ChangeState(BotState.WaitingForSkyblockCooldown);
        }

        // Handle successful skyblock load
        if (text.Contains("Sending you to") || text.Contains("skyblock") || text.Contains("SkyBlock"))
        {
            if (currentState == BotState.ProcessingSkyblockLoad)
            {
                LogToConsole("[Phase 1] SkyBlock load detected, waiting for completion");
                // Continue waiting for skyblock load to complete
            }
        }
    }

    // === PHASE 4 DISCORD INTEGRATION ===

    private void InitializeDiscordIntegration()
    {
        try
        {
            LogToConsole("[Phase 4] Initializing Discord integration...");
            LogToConsole($"[Phase 4] Bot Token: {DISCORD_BOT_TOKEN.Substring(0, 20)}...");
            LogToConsole($"[Phase 4] ISTOP Channel: {ISTOP_CHANNEL_ID}");
            LogToConsole($"[Phase 4] LB Channel: {LB_CHANNEL_ID}");

            // Explain MCC Discord integration limitations
            LogToConsole("[Phase 4] 📋 MCC Discord Integration Analysis:");
            LogToConsole("[Phase 4] ⚠️ MCC ChatBot scripts run in a sandboxed environment");
            LogToConsole("[Phase 4] ⚠️ Direct HTTP requests (System.Net.WebRequest) not available");
            LogToConsole("[Phase 4] ⚠️ Process execution (System.Diagnostics.Process) restricted");
            LogToConsole("[Phase 4] ✅ This is a security feature to prevent malicious scripts");
            LogToConsole("[Phase 4]");
            LogToConsole("[Phase 4] 🔧 Solution: External Discord Bridge Bot");
            LogToConsole("[Phase 4] ✅ MCC bot generates perfect embeds in simulation mode");
            LogToConsole("[Phase 4] ✅ External Python bot monitors logs and sends to Discord");
            LogToConsole("[Phase 4] ✅ This provides full Discord integration outside MCC sandbox");
            LogToConsole("[Phase 4]");
            LogToConsole("[Phase 4] 🚀 New File-Based Discord Integration:");
            LogToConsole("[Phase 4] 1. MCC bot writes Discord embeds to dedicated files");
            LogToConsole("[Phase 4] 2. External bridge bot monitors these files");
            LogToConsole("[Phase 4] 3. Bridge bot sends embeds to Discord automatically");
            LogToConsole("[Phase 4] 4. No manual log file manipulation required");
            LogToConsole("[Phase 4]");

            // Initialize Discord output files
            InitializeDiscordOutputFile();

            LogToConsole("[Phase 4] Discord integration: File-based mode (structured data output)");
            discordConnectionActive = false; // Always use file-based mode in MCC

            // Test the file-based integration
            TestDiscordFileIntegration();
            LogToConsole("[Phase 4] Discord integration initialized successfully");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error initializing Discord integration: {ex.Message}");
            LogToConsole("[Phase 4] Discord integration disabled for this session");
            phase4Enabled = false;
        }
    }

    private void TestDiscordFileIntegration()
    {
        try
        {
            LogToConsole("[Phase 4] Testing Discord file integration...");

            // Skip initial status message to reduce channel clutter

            discordConnectionActive = true;
            LogToConsole("[Phase 4] ✅ Discord connection test successful");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] ❌ Discord connection test failed: {ex.Message}");
            discordConnectionActive = false;
        }
    }

    private void SendDiscordEmbed(string channelId, string embedJson)
    {
        try
        {
            if (!phase4Enabled)
                return;

            // ENHANCED DEBUG: Track all Discord message generation paths
            LogToConsole($"[ENHANCED DEBUG] SendDiscordEmbed called for channel {channelId}");
            LogToConsole($"[ENHANCED DEBUG] Embed JSON length: {embedJson.Length}");
            LogToConsole($"[ENHANCED DEBUG] Embed JSON preview: {embedJson.Substring(0, Math.Min(100, embedJson.Length))}...");

            // Check for control characters in the embed JSON
            bool hasControlChars = false;
            for (int i = 0; i < embedJson.Length; i++)
            {
                char c = embedJson[i];
                if (c < 32 && c != '\n' && c != '\r' && c != '\t')
                {
                    hasControlChars = true;
                    LogToConsole($"[ENHANCED DEBUG] ❌ CONTROL CHARACTER DETECTED in SendDiscordEmbed at pos {i}: 0x{(int)c:X2}");
                }
            }

            if (!hasControlChars)
            {
                LogToConsole($"[ENHANCED DEBUG] ✅ No control characters detected in SendDiscordEmbed");
            }

            LogToConsole($"[Phase 4] Writing Discord embed to dedicated file for channel {channelId}");

            // Write structured embed data to dedicated file
            WriteDiscordEmbedToFile(channelId, embedJson);

            LogToConsole("[Phase 4] ✅ Discord embed written to file successfully");

            // Update Discord state tracking
            UpdateDiscordMessageTracking(channelId, embedJson);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error writing Discord embed to file: {ex.Message}");
        }
    }

    private void SendLeaderboardEmbed(string embedJson)
    {
        try
        {
            if (!phase4Enabled)
                return;

            DateTime now = DateTime.Now;
            bool shouldUpdate = true; // Always update the persistent leaderboard message

            // Always update the persistent leaderboard message to avoid channel clutter
            LogToConsole($"[Phase 4] Updating persistent leaderboard message (ID: {lastLeaderboardMessageId})");

            // Write leaderboard embed with update/create flag
            WriteLeaderboardEmbedToFile(LB_CHANNEL_ID, embedJson, shouldUpdate, lastLeaderboardMessageId);

            // Update tracking
            lastLeaderboardMessageTime = now;
            LogToConsole("[Phase 4] ✅ Leaderboard embed written to file successfully");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error sending leaderboard embed: {ex.Message}");
        }
    }

    private void SendIslandEmbed(string embedJson)
    {
        try
        {
            if (!phase4Enabled)
                return;

            DateTime now = DateTime.Now;
            bool shouldUpdate = true; // Always update the persistent island message

            // Always update the persistent island message to avoid channel clutter
            LogToConsole($"[Phase 4] Updating persistent island message (ID: {lastIslandMessageId})");

            // Write island embed with update/create flag
            WriteIslandEmbedToFile(LB_CHANNEL_ID, embedJson, shouldUpdate, lastIslandMessageId);

            // Update tracking
            lastIslandMessageTime = now;
            LogToConsole("[Phase 4] ✅ Island embed written to file successfully");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error sending island embed: {ex.Message}");
        }
    }

    private void WriteLeaderboardEmbedToFile(string channelId, string embedJson, bool shouldUpdate, string existingMessageId)
    {
        try
        {
            lock (fileWriteLock)
            {
                string queueFile = "discord_queue.json";
                string messageId = shouldUpdate && !string.IsNullOrEmpty(existingMessageId)
                    ? existingMessageId
                    : System.Guid.NewGuid().ToString();

                // Determine channel name
                string channelName = channelId == ISTOP_CHANNEL_ID ? "ISTOP" : "LB";

                // The embedJson is already a properly formatted JSON string from CreateLeaderboardEmbed()
                // We'll store it as raw JSON for the Discord bridge to parse
                LogToConsole($"[Phase 4] Processing leaderboard embed JSON (length: {embedJson.Length} chars)");

                // Include full leaderboard categories data for button interactions
                var categoriesData = new Dictionary<string, object>();
                foreach (var kvp in leaderboardCategories)
                {
                    var category = kvp.Value;
                    var playersData = new List<object>();

                    // Include top 10 players for each category
                    for (int i = 0; i < Math.Min(10, category.Rankings.Count); i++)
                    {
                        var player = category.Rankings[i];
                        playersData.Add(new
                        {
                            rank = player.Rank,
                            name = RemoveMinecraftColorCodes(player.PlayerName),
                            value = RemoveMinecraftColorCodes(player.Value),
                            points = player.Points
                        });
                    }

                    categoriesData[kvp.Key] = new
                    {
                        slot = category.Slot,
                        category_name = RemoveMinecraftColorCodes(category.CategoryName),
                        players = playersData,
                        extracted_at = category.ExtractedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    };
                }

                var messageData = new
                {
                    id = messageId,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    cycle_number = cycleCount,
                    channel_id = channelId,
                    channel_name = channelName,
                    embed_data_raw = embedJson,
                    leaderboard_categories = categoriesData, // Include full category data
                    status = "pending",
                    created_at = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    message_type = "leaderboard",
                    action = shouldUpdate ? "update" : "create",
                    update_message_id = shouldUpdate ? existingMessageId : null,
                    supports_interactions = true,
                    interaction_handler = "HandleLeaderboardButtonInteraction"
                };

                // Enhanced debug logging before JSON conversion
                LogToConsole($"[Phase 4] Converting message data to JSON for message ID: {messageId.Substring(0, 8)}...");
                WriteControlCharacterDebugLog("MCC_BOT", "BEFORE_ConvertToJsonString", embedJson, messageId);

                string jsonLine = ConvertToJsonString(messageData, messageId);

                // Enhanced debug logging after JSON conversion
                WriteControlCharacterDebugLog("MCC_BOT", "AFTER_ConvertToJsonString", jsonLine, messageId);
                LogToConsole($"[Phase 4] Final JSON length: {jsonLine.Length} characters");

                System.IO.File.AppendAllText(queueFile, jsonLine + Environment.NewLine);

                // Always updating persistent message, no need to change tracking
                LogToConsole($"[Phase 4] Leaderboard embed written to queue: UPDATE - Message ID: {messageId}");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error writing leaderboard embed to file: {ex.Message}");
        }
    }

    private void WriteIslandEmbedToFile(string channelId, string embedJson, bool shouldUpdate, string existingMessageId)
    {
        try
        {
            lock (fileWriteLock)
            {
                string queueFile = "discord_queue.json";
                string messageId = shouldUpdate && !string.IsNullOrEmpty(existingMessageId)
                    ? existingMessageId
                    : System.Guid.NewGuid().ToString();

                // Determine channel name
                string channelName = channelId == ISTOP_CHANNEL_ID ? "ISTOP" : "LB";

                // The embedJson is already a properly formatted JSON string from CreateIslandDataEmbed()
                LogToConsole($"[Phase 4] Processing island embed JSON (length: {embedJson.Length} chars)");

                var messageData = new
                {
                    id = messageId,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    cycle_number = cycleCount,
                    channel_id = channelId,
                    channel_name = channelName,
                    embed_data_raw = embedJson, // Store the raw JSON string
                    status = "pending",
                    created_at = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    message_type = "island",
                    action = shouldUpdate ? "update" : "create",
                    update_message_id = shouldUpdate ? existingMessageId : null,
                    supports_interactions = false
                };

                // Enhanced debug logging before JSON conversion
                LogToConsole($"[Phase 4] Converting island message data to JSON for message ID: {messageId.Substring(0, 8)}...");

                string jsonLine = ConvertToJsonString(messageData, messageId);

                LogToConsole($"[Phase 4] Final island JSON length: {jsonLine.Length} characters");

                System.IO.File.AppendAllText(queueFile, jsonLine + Environment.NewLine);

                // Always updating persistent message, no need to change tracking
                LogToConsole($"[Phase 4] Island embed written to queue: UPDATE - Message ID: {messageId}");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error writing island embed to file: {ex.Message}");
        }
    }

    private void WriteDiscordEmbedToFile(string channelId, string embedJson)
    {
        try
        {
            lock (fileWriteLock)
            {
                // Create structured Discord message data
                // Use proper JSON serialization with custom options to handle newlines
                string messageId = Guid.NewGuid().ToString();
                string timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                string channelName = channelId == ISTOP_CHANNEL_ID ? "ISTOP" : "LB";
                string createdAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                // Use simple manual JSON construction to avoid Unicode encoding issues
                // This approach works with basic .NET Framework available in MCC

                // Clean the embed JSON to prevent Unicode escaping issues
                string cleanEmbedJson = embedJson;

                // Replace problematic Unicode escape sequences with actual characters
                cleanEmbedJson = cleanEmbedJson.Replace("\\uD83D\\uDD04", "🔄"); // 🔄
                cleanEmbedJson = cleanEmbedJson.Replace("\\uD83D\\uDCCA", "📊"); // 📊
                cleanEmbedJson = cleanEmbedJson.Replace("\\u23F3", "⏳");       // ⏳
                cleanEmbedJson = cleanEmbedJson.Replace("\\u23F0", "⏰");       // ⏰
                cleanEmbedJson = cleanEmbedJson.Replace("\\uD83C\\uDFAF", "🎯"); // 🎯
                cleanEmbedJson = cleanEmbedJson.Replace("\\u2705", "✅");       // ✅
                cleanEmbedJson = cleanEmbedJson.Replace("\\u23F1\\uFE0F", "⏱️"); // ⏱️
                cleanEmbedJson = cleanEmbedJson.Replace("\\u2022", "•");        // •

                // DON'T ESCAPE THE JSON! It's already properly formatted!
                // The embedJson parameter is already a valid JSON string
                // Escaping it creates the double-escaped format that breaks parsing

                // Build JSON manually using basic string operations (MCC compatible)
                string messageJson = "{" +
                    "\"id\":\"" + messageId + "\"," +
                    "\"timestamp\":\"" + timestamp + "\"," +
                    "\"cycle_number\":" + cycleCount + "," +
                    "\"channel_id\":\"" + channelId + "\"," +
                    "\"channel_name\":\"" + channelName + "\"," +
                    "\"embed_data\":" + cleanEmbedJson + "," +
                    "\"status\":\"pending\"," +
                    "\"created_at\":\"" + createdAt + "\"" +
                    "}";

                // Append to queue file (one JSON object per line)
                System.IO.File.AppendAllText(DISCORD_QUEUE_FILE, messageJson + "\n");

                LogToConsole($"[Phase 4] Discord message queued: ID {messageId.Substring(0, 8)}...");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error writing to Discord queue file: {ex.Message}");
        }
    }

    private void UpdateDiscordMessageTracking(string channelId, string embedJson)
    {
        try
        {
            // Update last message tracking for status reporting
            if (channelId == ISTOP_CHANNEL_ID)
            {
                lastIstopMessageId = $"msg_{DateTime.Now.Ticks}";
            }
            else if (channelId == LB_CHANNEL_ID)
            {
                // Note: This method is now only used for legacy SendDiscordEmbed calls
                // Island and leaderboard data now use their own tracking in SendIslandEmbed/SendLeaderboardEmbed
                if (embedJson.Contains("Island") || embedJson.Contains("island"))
                {
                    lastIslandMessageId = $"msg_{DateTime.Now.Ticks}";
                }
                else
                {
                    lastLeaderboardMessageId = $"msg_{DateTime.Now.Ticks}";
                }
            }

            lastDiscordUpdate = DateTime.Now;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error updating Discord message tracking: {ex.Message}");
        }
    }

    private void InitializeDiscordOutputFile()
    {
        try
        {
            // Initialize the Discord queue file with metadata
            var initData = new
            {
                initialized_at = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                bot_version = "Multi-Phase Bot v2.0",
                queue_file = DISCORD_QUEUE_FILE,
                channels = new
                {
                    istop = new { id = ISTOP_CHANNEL_ID, name = "ISTOP" },
                    lb = new { id = LB_CHANNEL_ID, name = "LB" }
                },
                format_version = "1.0"
            };

            string initJson = System.Text.Json.JsonSerializer.Serialize(initData, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });

            // Write initialization data to main output file
            System.IO.File.WriteAllText(DISCORD_OUTPUT_FILE, initJson);

            // Initialize empty queue file
            if (!System.IO.File.Exists(DISCORD_QUEUE_FILE))
            {
                System.IO.File.WriteAllText(DISCORD_QUEUE_FILE, "");
            }

            LogToConsole($"[Phase 4] Discord output files initialized:");
            LogToConsole($"[Phase 4] - Main file: {DISCORD_OUTPUT_FILE}");
            LogToConsole($"[Phase 4] - Queue file: {DISCORD_QUEUE_FILE}");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error initializing Discord output files: {ex.Message}");
        }
    }

    private string CreateBotStatusEmbed(string title, string description, int color)
    {
        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = title,
                    description = description,
                    color = color,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    footer = new
                    {
                        text = "Multi-Phase Bot",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/bot-icon.png"
                    },
                    fields = new[]
                    {
                        new
                        {
                            name = "🔄 Current State",
                            value = currentState.ToString(),
                            inline = true
                        },
                        new
                        {
                            name = "📊 Active Phases",
                            value = $"Phase 1: {(phase1Enabled ? "✅" : "❌")}\\nPhase 2: {(phase2Enabled ? "✅" : "❌")}\\nPhase 3: {(phase3Enabled ? "✅" : "❌")}\\nPhase 4: {(phase4Enabled ? "✅" : "❌")}",
                            inline = true
                        },
                        new
                        {
                            name = "⏰ Next Task",
                            value = nextTaskRun.ToString("HH:mm:ss"),
                            inline = true
                        }
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private string CreateIstopStatusEmbed(string status, bool success, string details = "")
    {
        int color = success ? 0x00FF00 : 0xFF0000; // Green for success, red for failure
        string emoji = success ? "✅" : "❌";

        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = $"{emoji} /istop Command Status",
                    description = status,
                    color = color,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "📝 Details",
                            value = string.IsNullOrEmpty(details) ? "No additional details" : details,
                            inline = false
                        },
                        new
                        {
                            name = "🕐 Execution Time",
                            value = DateTime.Now.ToString("HH:mm:ss"),
                            inline = true
                        },
                        new
                        {
                            name = "🔄 Current State",
                            value = currentState.ToString(),
                            inline = true
                        }
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private string CreateIslandDataEmbed()
    {
        var fields = new List<object>();

        // Sort islands by ranking
        var sortedIslands = currentIslandData.Values.OrderBy(i => i.Ranking).ToList();

        // Add island data fields
        foreach (var island in sortedIslands)
        {
            // Clean the island name further
            string cleanName = island.Name;
            if (cleanName.StartsWith("#") && cleanName.Contains(":"))
            {
                // Extract just the island name part
                string[] parts = cleanName.Split(':');
                if (parts.Length >= 2)
                {
                    cleanName = parts[1].Trim();
                }
            }

            // Format values with fallbacks
            string allTimeValue = !string.IsNullOrEmpty(island.AllTimeValue) &&
                                  island.AllTimeValue != "Data not available" &&
                                  island.AllTimeValue != "Extracting..."
                ? island.AllTimeValue
                : (island.AllTimeValue == "Extracting..." ? "⏳ Extracting..." : "❌ Not available");

            string weeklyValue = !string.IsNullOrEmpty(island.WeeklyValue) &&
                                 island.WeeklyValue != "Data not available" &&
                                 island.WeeklyValue != "Extracting..."
                ? island.WeeklyValue
                : (island.WeeklyValue == "Extracting..." ? "⏳ Extracting..." : "❌ Not available");

            string todayChange = !string.IsNullOrEmpty(island.TodayChange) &&
                                 island.TodayChange != "Data not available" &&
                                 island.TodayChange != "Extracting..."
                ? island.TodayChange
                : (island.TodayChange == "Extracting..." ? "⏳ Extracting..." : "❌ No change data");

            // Calculate growth metrics
            string growthMetrics = CalculateGrowthMetrics(island);

            fields.Add(new
            {
                name = $"🏝️ #{island.Ranking}: {cleanName}",
                value = $"**All Time:** {allTimeValue}\\n**Weekly:** {weeklyValue}\\n**Growth:** {growthMetrics}",
                inline = false
            });
        }

        if (fields.Count == 0)
        {
            fields.Add(new
            {
                name = "📊 Status",
                value = "No island data available - waiting for /istop inventory to open",
                inline = false
            });
        }

        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = "🏝️ Top Islands Data",
                    description = $"Island rankings extracted at {DateTime.Now:HH:mm:ss}",
                    color = 0x0099FF,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = fields.ToArray(),
                    footer = new
                    {
                        text = $"Phase 2 • {currentIslandData.Count} islands tracked • Updated every 10 minutes",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private string CreateLeaderboardEmbed()
    {
        // ENHANCED DEBUG: Track leaderboard embed creation
        LogToConsole($"[ENHANCED DEBUG] CreateLeaderboardEmbed called at {DateTime.Now:HH:mm:ss.fff}");
        LogToConsole($"[ENHANCED DEBUG] Available categories: {leaderboardCategories.Count}");

        var fields = new List<object>();

        // Get the first category with rankings to show overall top 10 players
        var firstCategoryWithData = leaderboardCategories.Values.FirstOrDefault(c => c.Rankings.Count > 0);

        if (firstCategoryWithData != null)
        {
            LogToConsole($"[ENHANCED DEBUG] Using category: '{firstCategoryWithData.CategoryName}' with {firstCategoryWithData.Rankings.Count} players");
        }

        if (firstCategoryWithData != null)
        {
            // Show top 12 players from the first available category as the main display
            var topPlayers = new List<string>();
            for (int i = 0; i < Math.Min(12, firstCategoryWithData.Rankings.Count); i++)
            {
                var player = firstCategoryWithData.Rankings[i];

                // ENHANCED DEBUG: Log raw player data before cleaning
                LogToConsole($"[ENHANCED DEBUG] Processing player {i+1}: Raw name='{player.PlayerName}', Raw value='{player.Value}'");

                string cleanPlayerName = RemoveMinecraftColorCodes(player.PlayerName);
                string cleanValue = RemoveMinecraftColorCodes(player.Value);

                // ENHANCED DEBUG: Log cleaned player data
                LogToConsole($"[ENHANCED DEBUG] Player {i+1} cleaned: Name='{cleanPlayerName}', Value='{cleanValue}'");

                topPlayers.Add($"#{player.Rank}: {cleanPlayerName} - {cleanValue}");
            }

            fields.Add(new
            {
                name = $"🏆 {RemoveMinecraftColorCodes(firstCategoryWithData.CategoryName)}",
                value = string.Join("\\n", topPlayers),
                inline = false
            });
        }
        else
        {
            fields.Add(new
            {
                name = "📊 Status",
                value = "No leaderboard data available",
                inline = false
            });
        }

        // Create interactive buttons for each category
        var buttons = CreateLeaderboardButtons();

        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = "🏆 Top 12 Players",
                    description = $"Player rankings extracted at {DateTime.Now:HH:mm:ss}\\n*Click buttons below to view different categories*",
                    color = 0xFFD700,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = fields.ToArray(),
                    footer = new
                    {
                        text = $"Phase 3 • {leaderboardCategories.Count} categories available",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                    }
                }
            },
            components = buttons
        };

        return ConvertToJsonString(embed);
    }

    // === SAMPLE DATA GENERATION (FALLBACK) ===

    private string CreateSampleIslandDataEmbed()
    {
        // Generate sample island data with realistic values that change over time
        var currentTime = DateTime.Now;
        var minute = currentTime.Minute;
        var hour = currentTime.Hour;

        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = "🏝️ Top Islands Data - Sample Update",
                    description = $"Sample island rankings (no real data available) - {currentTime:HH:mm:ss}",
                    color = 0x00FF7F, // Spring green to indicate sample data
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "🏝️ #1: Revenants",
                            value = $"**All Time:** $2.{97 + (minute % 10)}B\\n**Weekly:** $1.{2 + (hour % 5)}B\\n**Today:** +${31 + (minute % 20)}.{34 + (minute % 66)}M (+{1.5 + (minute % 10) * 0.1:F1}%)",
                            inline = true
                        },
                        new
                        {
                            name = "🏝️ #2: ████NIP████",
                            value = $"**All Time:** $1.{40 + (minute % 5)}B\\n**Weekly:** ${800 + (hour * 10)}M\\n**Today:** +${15 + (minute % 10)}.{2 + (minute % 98)}M (+{1.1 + (minute % 8) * 0.1:F1}%)",
                            inline = true
                        },
                        new
                        {
                            name = "🏝️ #3: MindControl",
                            value = $"**All Time:** ${599 + (minute % 50)}.{93 + (minute % 7)}M\\n**Weekly:** ${400 + (hour * 5)}M\\n**Today:** +${8 + (minute % 15)}.{15 + (minute % 85)}M (+{2.7 + (minute % 5) * 0.2:F1}%)",
                            inline = true
                        },
                        new
                        {
                            name = "🏝️ #4: SkyblockIsland",
                            value = $"**All Time:** ${234 + (minute % 30)}.{56 + (minute % 44)}M\\n**Weekly:** ${150 + (hour * 3)}M\\n**Today:** +${2 + (minute % 8)}.{1 + (minute % 99)}M (+{0.9 + (minute % 6) * 0.1:F1}%)",
                            inline = true
                        },
                        new
                        {
                            name = "🏝️ #5: TestIsland",
                            value = $"**All Time:** ${89 + (minute % 20)}.{12 + (minute % 88)}M\\n**Weekly:** ${45 + (hour * 2)}M\\n**Today:** +${1 + (minute % 5)}.{5 + (minute % 95)}M (+{1.7 + (minute % 4) * 0.3:F1}%)",
                            inline = true
                        }
                    },
                    footer = new
                    {
                        text = "Phase 2 • Sample Data • No real SkyBlock connection",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private object[] CreateLeaderboardButtons()
    {
        var buttons = new List<object>();
        var buttonRows = new List<object>();

        int buttonCount = 0;
        foreach (var kvp in leaderboardCategories)
        {
            // Skip slot_4 (Top 12 Players) as it's redundant with the main display
            if (kvp.Key == "slot_4")
                continue;

            var category = kvp.Value;
            if (category.Rankings.Count > 0)
            {
                string cleanCategoryName = RemoveMinecraftColorCodes(category.CategoryName);

                // Create button with shortened label for Discord's character limit
                string buttonLabel = ShortenCategoryName(cleanCategoryName);
                string buttonId = $"leaderboard_{kvp.Key}"; // Use category key as unique ID

                buttons.Add(new
                {
                    type = 2, // Button type
                    style = 2, // Secondary style (gray)
                    label = buttonLabel,
                    custom_id = buttonId
                });

                buttonCount++;

                // Discord allows max 5 buttons per row, max 5 rows
                if (buttonCount % 5 == 0 || buttonCount >= 25)
                {
                    buttonRows.Add(new
                    {
                        type = 1, // Action Row type
                        components = buttons.ToArray()
                    });
                    buttons.Clear();
                }
            }
        }

        // Add remaining buttons if any
        if (buttons.Count > 0)
        {
            buttonRows.Add(new
            {
                type = 1, // Action Row type
                components = buttons.ToArray()
            });
        }

        return buttonRows.ToArray();
    }

    private string ShortenCategoryName(string categoryName)
    {
        // Discord button labels have a 80 character limit
        var shortNames = new Dictionary<string, string>
        {
            { "Mobs Killed", "Mobs" },
            { "Runes Identified", "Runes" },
            { "Pouches Opened", "Pouches" },
            { "Votes", "Votes" },
            { "Quests Completed", "Quests" },
            { "Experience Gained", "XP" },
            { "Blocks Placed", "Blocks" },
            { "Fish Caught", "Fishing" },
            { "Items Sold", "Sales" },
            { "Money Earned", "Money" }
        };

        foreach (var kvp in shortNames)
        {
            if (categoryName.Contains(kvp.Key))
                return kvp.Value;
        }

        // Fallback: truncate to 20 characters
        return categoryName.Length > 20 ? categoryName.Substring(0, 20) : categoryName;
    }

    private string CreateSampleLeaderboardDataEmbed()
    {
        // Generate sample leaderboard data with realistic values that change over time
        var currentTime = DateTime.Now;
        var minute = currentTime.Minute;
        var hour = currentTime.Hour;

        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = "🏆 Top 10 Players (Sample)",
                    description = $"Sample player rankings (no real data available) - {currentTime:HH:mm:ss}\\n*Click buttons below to view different categories*",
                    color = 0xFFD700, // Gold
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "🏆 Mobs Killed",
                            value = $"#1: minidomo ({1777730 + (minute * 100):,} mobs)\\n#2: DaSimsGamer ({141602 + (minute * 50):,} mobs)\\n#3: JustABanana777 ({130897 + (minute * 25):,} mobs)",
                            inline = true
                        },
                        new
                        {
                            name = "🏆 Scrolls Completed",
                            value = $"#1: Jaxair ({34 + (minute % 5)} scrolls)\\n#2: Farrnado ({31 + (minute % 3)} scrolls)\\n#3: Roleeb ({25 + (minute % 2)} scrolls)",
                            inline = true
                        },
                        new
                        {
                            name = "🏆 Gold Shards Collected",
                            value = $"#1: FlactioON ({13210 + (minute * 10):,} shards)\\n#2: MostlyMissing ({8945 + (minute * 5):,} shards)\\n#3: LastRez ({7234 + (minute * 3):,} shards)",
                            inline = true
                        },
                        new
                        {
                            name = "🏆 Blocks Broken",
                            value = $"#1: BlockBreaker99 ({2456789 + (hour * 1000):,} blocks)\\n#2: MiningMaster ({1234567 + (hour * 500):,} blocks)\\n#3: StoneDestroyer ({987654 + (hour * 250):,} blocks)",
                            inline = true
                        },
                        new
                        {
                            name = "🏆 Items Crafted",
                            value = $"#1: CraftingKing ({567890 + (hour * 100):,} items)\\n#2: ItemMaker ({345678 + (hour * 75):,} items)\\n#3: CraftMaster ({234567 + (hour * 50):,} items)",
                            inline = true
                        },
                        new
                        {
                            name = "🏆 Fish Caught",
                            value = $"#1: FishingPro ({123456 + (hour * 25):,} fish)\\n#2: AngleMaster ({98765 + (hour * 20):,} fish)\\n#3: SeaHunter ({76543 + (hour * 15):,} fish)",
                            inline = true
                        }
                    },
                    footer = new
                    {
                        text = "Phase 3 • Sample Data • No real SkyBlock connection",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                    }
                }
            },
            components = new object[]
            {
                new
                {
                    type = 1, // Action Row type
                    components = new object[]
                    {
                        new { type = 2, style = 2, label = "Mobs", custom_id = "sample_mobs" },
                        new { type = 2, style = 2, label = "Runes", custom_id = "sample_runes" },
                        new { type = 2, style = 2, label = "Pouches", custom_id = "sample_pouches" },
                        new { type = 2, style = 2, label = "Votes", custom_id = "sample_votes" },
                        new { type = 2, style = 2, label = "Quests", custom_id = "sample_quests" }
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private string CreateCategorySpecificEmbed(string categoryKey)
    {
        if (!leaderboardCategories.ContainsKey(categoryKey))
        {
            return CreateErrorEmbed("Category not found", "The requested leaderboard category could not be found.");
        }

        var category = leaderboardCategories[categoryKey];
        var fields = new List<object>();

        if (category.Rankings.Count > 0)
        {
            // Show top 10 players for this specific category
            var topPlayers = new List<string>();
            for (int i = 0; i < Math.Min(10, category.Rankings.Count); i++)
            {
                var player = category.Rankings[i];
                string cleanPlayerName = RemoveMinecraftColorCodes(player.PlayerName);
                string cleanValue = RemoveMinecraftColorCodes(player.Value);
                topPlayers.Add($"#{player.Rank}: {cleanPlayerName} - {cleanValue}");
            }

            fields.Add(new
            {
                name = $"🏆 Top 10 Players",
                value = string.Join("\\n", topPlayers),
                inline = false
            });
        }
        else
        {
            fields.Add(new
            {
                name = "📊 Status",
                value = "No player data available for this category",
                inline = false
            });
        }

        string cleanCategoryName = RemoveMinecraftColorCodes(category.CategoryName);
        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = $"🏆 {cleanCategoryName} - Top 10 Players",
                    description = $"Rankings extracted at {category.ExtractedAt:HH:mm:ss}",
                    color = 0x00FF00,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = fields.ToArray(),
                    footer = new
                    {
                        text = $"Category: {cleanCategoryName} • {category.Rankings.Count} players tracked",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private string CreateErrorEmbed(string title, string description)
    {
        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = $"❌ {title}",
                    description = description,
                    color = 0xFF0000,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private object ParseEmbedJsonString(string embedJson)
    {
        try
        {
            LogToConsole($"[Phase 4] Parsing embed JSON string (length: {embedJson.Length} chars)");

            // Since MCC doesn't have full JSON parsing capabilities, we'll extract
            // the key information manually and reconstruct the embed structure

            // The embedJson should contain the complete embed structure
            // For now, we'll pass it through as a raw string and let the Discord bridge handle it

            // Create a simple structure that preserves the original JSON
            return new
            {
                raw_embed_json = embedJson,
                requires_parsing = true,
                embed_type = "leaderboard_with_buttons"
            };
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error parsing embed JSON: {ex.Message}");
            return new { error = "Failed to parse embed JSON", raw_data = embedJson };
        }
    }

    private object ConvertJsonStringToObject(string jsonString)
    {
        try
        {
            // For MCC environment, we need to pass the JSON string directly
            // The Discord bridge will handle the actual JSON parsing
            // We'll create a simple wrapper that preserves the original JSON

            LogToConsole($"[Phase 4] Converting JSON string to object (length: {jsonString.Length} chars)");

            // Instead of parsing, we'll create a structure that tells the bridge
            // to use the raw JSON string for the embed data
            return new
            {
                raw_json = jsonString,
                parsed = false,
                note = "Raw JSON preserved for Discord bridge processing"
            };
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error handling JSON string: {ex.Message}");
            return new { error = "Failed to handle JSON", raw_json = jsonString };
        }
    }

    // Enhanced Discord button interaction handler with proper response format
    public string HandleLeaderboardButtonInteraction(string buttonId, string userId = "", string userName = "")
    {
        try
        {
            LogToConsole($"[Phase 4] Handling leaderboard button interaction: {buttonId} from user {userName} ({userId})");

            string embedContent = "";

            // Parse button ID to get category key
            if (buttonId.StartsWith("leaderboard_"))
            {
                string categoryKey = buttonId.Replace("leaderboard_", "");
                embedContent = CreateCategorySpecificEmbed(categoryKey);
                LogToConsole($"[Phase 4] Generated category-specific embed for: {categoryKey}");
            }
            else if (buttonId.StartsWith("sample_"))
            {
                // Handle sample button interactions
                embedContent = CreateSampleCategoryEmbed(buttonId);
                LogToConsole($"[Phase 4] Generated sample category embed for: {buttonId}");
            }
            else
            {
                embedContent = CreateErrorEmbed("Invalid Button", "The button interaction could not be processed.");
                LogToConsole($"[Phase 4] Invalid button ID format: {buttonId}");
            }

            // Create proper Discord interaction response
            var interactionResponse = CreateInteractionResponse(embedContent, true); // ephemeral = true

            // Write interaction response to queue for Discord bridge to handle
            WriteInteractionResponseToFile(buttonId, interactionResponse, userId, userName);

            return interactionResponse;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error handling button interaction: {ex.Message}");
            var errorResponse = CreateInteractionResponse(
                CreateErrorEmbed("Interaction Error", "An error occurred while processing the button interaction."),
                true
            );
            WriteInteractionResponseToFile(buttonId, errorResponse, userId, userName);
            return errorResponse;
        }
    }

    private string CreateInteractionResponse(string embedContent, bool ephemeral = true)
    {
        try
        {
            var response = new
            {
                type = 4, // CHANNEL_MESSAGE_WITH_SOURCE
                data = new
                {
                    embeds = ExtractEmbedsFromContent(embedContent),
                    flags = ephemeral ? 64 : 0 // EPHEMERAL flag
                }
            };

            return ConvertToJsonString(response);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error creating interaction response: {ex.Message}");
            return "{}";
        }
    }

    private object[] ExtractEmbedsFromContent(string embedContent)
    {
        try
        {
            // Simple extraction - in a full implementation you'd parse the JSON properly
            return new object[]
            {
                new
                {
                    title = "Category Rankings",
                    description = "Player rankings for selected category",
                    color = 0x00FF00
                }
            };
        }
        catch
        {
            return new object[] { new { title = "Error", description = "Failed to extract embed data" } };
        }
    }

    private void WriteInteractionResponseToFile(string buttonId, string responseJson, string userId, string userName)
    {
        try
        {
            lock (fileWriteLock)
            {
                string interactionFile = "discord_interactions.json";

                var interactionData = new
                {
                    id = System.Guid.NewGuid().ToString(),
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    button_id = buttonId,
                    user_id = userId,
                    user_name = userName,
                    response = responseJson,
                    status = "pending",
                    created_at = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    interaction_type = "button_click",
                    ephemeral = true
                };

                string jsonLine = ConvertToJsonString(interactionData);
                System.IO.File.AppendAllText(interactionFile, jsonLine + Environment.NewLine);

                LogToConsole($"[Phase 4] Interaction response written to file: {buttonId}");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error writing interaction response to file: {ex.Message}");
        }
    }

    private string CreateSampleCategoryEmbed(string buttonId)
    {
        string categoryName;
        string[] players;

        // Handle different sample categories without dynamic types
        switch (buttonId)
        {
            case "sample_mobs":
                categoryName = "Mobs Killed";
                players = new[] { "#1: Player1 - 1,234 mobs", "#2: Player2 - 1,156 mobs", "#3: Player3 - 1,089 mobs" };
                break;
            case "sample_runes":
                categoryName = "Runes Identified";
                players = new[] { "#1: Player4 - 567 runes", "#2: Player5 - 543 runes", "#3: Player6 - 521 runes" };
                break;
            case "sample_pouches":
                categoryName = "Pouches Opened";
                players = new[] { "#1: Player7 - 890 pouches", "#2: Player8 - 876 pouches", "#3: Player9 - 854 pouches" };
                break;
            case "sample_votes":
                categoryName = "Votes";
                players = new[] { "#1: Player10 - 45 votes", "#2: Player11 - 43 votes", "#3: Player12 - 41 votes" };
                break;
            case "sample_quests":
                categoryName = "Quests Completed";
                players = new[] { "#1: Player13 - 123 quests", "#2: Player14 - 118 quests", "#3: Player15 - 112 quests" };
                break;
            default:
                return CreateErrorEmbed("Sample Category Not Found", "The requested sample category could not be found.");
        }

        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = $"🏆 {categoryName} - Top 10 Players (Sample)",
                    description = $"Sample rankings generated at {DateTime.Now:HH:mm:ss}",
                    color = 0xFFA500,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "🏆 Top Players",
                            value = string.Join("\\n", players),
                            inline = false
                        }
                    },
                    footer = new
                    {
                        text = $"Sample Category: {categoryName}",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                    }
                }
            }
        };

        return ConvertToJsonString(embed);
    }

    private string ConvertToJsonString(object obj)
    {
        return ConvertToJsonString(obj, "unknown");
    }

    private string ConvertToJsonString(object obj, string messageId)
    {
        try
        {
            // Simple JSON serialization for MCC environment
            // This is a basic implementation - in a full environment you'd use Newtonsoft.Json
            string result = SimpleJsonSerializer(obj);

            // Debug logging for control character analysis
            WriteControlCharacterDebugLog("MCC_BOT", "ConvertToJsonString", result, messageId);

            return result;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error converting to JSON: {ex.Message}");
            WriteControlCharacterDebugLog("MCC_BOT", "ConvertToJsonString_ERROR", ex.Message, messageId);
            return "{}";
        }
    }

    private void WriteControlCharacterDebugLog(string component, string context, string jsonData, string messageId)
    {
        try
        {
            string debugFile = "control_character_debug.log";
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");

            // ENHANCED: Add message timing analysis
            string logEntry = $"[{timestamp}] [{component}] [CRITICAL] === ENHANCED DEBUG FOR MESSAGE {messageId} ===\n";
            logEntry += $"[{timestamp}] [{component}] [INFO] Context: {context}\n";
            logEntry += $"[{timestamp}] [{component}] [INFO] Message timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
            logEntry += $"[{timestamp}] [{component}] [INFO] Fixes implemented at: ~2025-08-04 00:46:00\n";
            logEntry += $"[{timestamp}] [{component}] [INFO] This message is: {(DateTime.Now.Hour == 0 && DateTime.Now.Minute >= 46 ? "AFTER fixes (SHOULD BE CLEAN)" : "BEFORE fixes (EXPECTED TO HAVE ISSUES)")}\n";

            // Analyze the JSON for control characters
            var controlChars = new System.Collections.Generic.List<string>();
            for (int i = 0; i < jsonData.Length; i++)
            {
                char c = jsonData[i];
                if (c < 32 && c != '\n' && c != '\r' && c != '\t')
                {
                    controlChars.Add($"pos:{i},char:0x{(int)c:X2},ascii:{(int)c}");
                }
            }

            // Log basic info
            logEntry += $"[{timestamp}] [{component}] [DEBUG]   JSON Length: {jsonData.Length} characters\n";

            if (controlChars.Count > 0)
            {
                logEntry += $"[{timestamp}] [{component}] [CRITICAL]   ❌ FOUND {controlChars.Count} CONTROL CHARACTERS: {string.Join(", ", controlChars.ToArray())}\n";
                logEntry += $"[{timestamp}] [{component}] [CRITICAL]   ❌ THIS INDICATES ENHANCED CLEANING IS NOT WORKING!\n";
            }
            else
            {
                logEntry += $"[{timestamp}] [{component}] [SUCCESS]   ✅ No control characters found - enhanced cleaning working\n";
            }

            // Enhanced analysis around position 91-92
            if (jsonData.Length > 90)
            {
                char char91 = jsonData[90];
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   Character at position 91: '{char91}' (0x{(int)char91:X2}, ASCII:{(int)char91})\n";
                if ((int)char91 < 32 && char91 != '\n' && char91 != '\r' && char91 != '\t')
                {
                    logEntry += $"[{timestamp}] [{component}] [CRITICAL]   ❌ POSITION 91 HAS CONTROL CHARACTER!\n";
                }
            }
            if (jsonData.Length > 91)
            {
                char char92 = jsonData[91];
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   Character at position 92: '{char92}' (0x{(int)char92:X2}, ASCII:{(int)char92})\n";
                if ((int)char92 < 32 && char92 != '\n' && char92 != '\r' && char92 != '\t')
                {
                    logEntry += $"[{timestamp}] [{component}] [CRITICAL]   ❌ POSITION 92 HAS CONTROL CHARACTER!\n";
                }
            }

            // Enhanced hex dump around position 91
            if (jsonData.Length > 85)
            {
                int start = Math.Max(0, 85);
                int end = Math.Min(jsonData.Length, 100);
                string hexSection = jsonData.Substring(start, end - start);
                string hexBytes = "";
                string asciiChars = "";
                for (int i = 0; i < hexSection.Length; i++)
                {
                    hexBytes += $"{(int)hexSection[i]:X2} ";
                    asciiChars += ((int)hexSection[i] >= 32 && (int)hexSection[i] <= 126) ? hexSection[i].ToString() : ".";
                }
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   Hex dump around pos 91 ({start}-{end}):\n";
                logEntry += $"[{timestamp}] [{component}] [DEBUG]     Hex:   {hexBytes}\n";
                logEntry += $"[{timestamp}] [{component}] [DEBUG]     ASCII: {asciiChars}\n";
                logEntry += $"[{timestamp}] [{component}] [DEBUG]     Text:  {hexSection.Replace('\n', '\\').Replace('\r', '\\')}\n";
            }

            // Enhanced JSON preview with control character highlighting
            if (jsonData.Length < 300)
            {
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   Full JSON: {jsonData}\n";
            }
            else
            {
                // Show more context around position 91
                int contextStart = Math.Max(0, 70);
                int contextEnd = Math.Min(jsonData.Length, 120);
                string contextSection = jsonData.Substring(contextStart, contextEnd - contextStart);
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   JSON around pos 91 ({contextStart}-{contextEnd}): {contextSection}\n";
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   JSON start: {jsonData.Substring(0, Math.Min(100, jsonData.Length))}\n";
                logEntry += $"[{timestamp}] [{component}] [DEBUG]   JSON end: {jsonData.Substring(Math.Max(0, jsonData.Length - 100))}\n";
            }

            logEntry += $"[{timestamp}] [{component}] [CRITICAL] === END ENHANCED DEBUG ===\n\n";

            // Write to debug file
            System.IO.File.AppendAllText(debugFile, logEntry);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] Error writing enhanced debug log: {ex.Message}");
        }
    }

    private string CleanControlCharactersFromSourceData(string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        var cleaned = new System.Text.StringBuilder();
        foreach (char c in input)
        {
            if (c < 32 && c != '\n' && c != '\r' && c != '\t') // Control characters except newline, carriage return, tab
            {
                // Replace control characters with safe alternatives or remove them
                switch (c)
                {
                    case '\x00': // Null
                        // Skip null characters
                        break;
                    case '\x07': // Bell
                        // Replace with safe alternative
                        cleaned.Append("[BELL]");
                        break;
                    case '\x08': // Backspace
                        // Replace with safe alternative
                        cleaned.Append("[BS]");
                        break;
                    case '\x0B': // Vertical tab
                        cleaned.Append(" "); // Replace with space
                        break;
                    case '\x0C': // Form feed
                        cleaned.Append(" "); // Replace with space
                        break;
                    case '\x1B': // Escape
                        // Skip escape characters
                        break;
                    default:
                        // For other control characters, skip them
                        break;
                }
            }
            else if (c == 127) // DEL character
            {
                // Skip DEL character
            }
            else
            {
                cleaned.Append(c);
            }
        }

        return cleaned.ToString();
    }

    private string SimpleJsonSerializer(object obj)
    {
        if (obj == null) return "null";

        var type = obj.GetType();

        if (type == typeof(string))
        {
            // Properly escape JSON string values for Discord embeds
            string str = obj.ToString();

            // CRITICAL FIX: Pre-clean control characters from source data
            str = CleanControlCharactersFromSourceData(str);

            // First, handle backslashes (must be done first)
            str = str.Replace("\\", "\\\\");

            // Handle quotes
            str = str.Replace("\"", "\\\"");

            // Handle all control characters (ASCII 0-31) except \n, \r, \t
            // This prevents "Invalid control character" JSON errors
            var cleanedStr = new System.Text.StringBuilder();
            foreach (char c in str)
            {
                if (c < 32) // Control character
                {
                    switch (c)
                    {
                        case '\n': // Newline - handle specially below
                            cleanedStr.Append(c);
                            break;
                        case '\r': // Carriage return
                            cleanedStr.Append("\\r");
                            break;
                        case '\t': // Tab
                            cleanedStr.Append("\\t");
                            break;
                        case '\b': // Backspace
                            cleanedStr.Append("\\b");
                            break;
                        case '\f': // Form feed
                            cleanedStr.Append("\\f");
                            break;
                        default: // Other control characters - escape as unicode
                            cleanedStr.Append($"\\u{(int)c:X4}");
                            break;
                    }
                }
                else if (c == 127) // DEL character
                {
                    cleanedStr.Append("\\u007F");
                }
                else
                {
                    cleanedStr.Append(c);
                }
            }
            str = cleanedStr.ToString();

            // CRITICAL FIX: Proper newline handling for JSON + Discord compatibility
            // The issue: Discord embeds use \\n for line breaks, but JSON requires proper escaping
            //
            // What happens:
            // 1. C# code has: "text\\n*more text*" (\\n = backslash + n for Discord)
            // 2. String contains: "text\n*more text*" (literal backslash + n)
            // 3. JSON needs: "text\\n*more text*" (escaped backslash + n)
            // 4. But we were converting to literal newline, causing JSON parse errors

            // Always escape actual newline characters for JSON
            str = str.Replace("\n", "\\n");

            // Note: \\n in C# source becomes \n in string, which is what Discord expects
            // No additional processing needed - the \n is already correct for Discord

            return $"\"{str}\"";
        }

        if (type == typeof(int) || type == typeof(long) || type == typeof(double) || type == typeof(float))
            return obj.ToString();

        if (type == typeof(bool))
            return obj.ToString().ToLower();

        if (obj is System.Collections.IEnumerable && !(obj is string))
        {
            var items = new List<string>();
            foreach (var item in (System.Collections.IEnumerable)obj)
            {
                items.Add(SimpleJsonSerializer(item));
            }
            return $"[{string.Join(",", items.ToArray())}]";
        }

        // Handle anonymous objects and complex types
        var properties = type.GetProperties();
        var jsonPairs = new List<string>();

        foreach (var prop in properties)
        {
            var value = prop.GetValue(obj, null);
            var jsonValue = SimpleJsonSerializer(value);
            jsonPairs.Add($"\"{prop.Name}\":{jsonValue}");
        }

        return $"{{{string.Join(",", jsonPairs.ToArray())}}}";
    }

    private string ExtractTextFromJsonLore(string jsonLore)
    {
        try
        {
            // Extract readable text from JSON lore format
            // Example: [{"color":"white","text":"$2.94B","italic":false}] -> "$2.94B"

            if (string.IsNullOrEmpty(jsonLore))
                return "";

            // Simple regex to extract text values from JSON
            var textMatches = System.Text.RegularExpressions.Regex.Matches(jsonLore, @"""text"":""([^""]+)""");
            var extractedTexts = new List<string>();

            foreach (System.Text.RegularExpressions.Match match in textMatches)
            {
                string text = match.Groups[1].Value;
                // Skip empty spaces and formatting characters
                if (!string.IsNullOrWhiteSpace(text) && text != " " && text != "◆ ")
                {
                    extractedTexts.Add(text);
                }
            }

            return string.Join("", extractedTexts.ToArray());
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error extracting text from JSON lore: {ex.Message}");
            return jsonLore; // Return original if parsing fails
        }
    }

    private string CleanJsonText(string jsonText)
    {
        try
        {
            if (string.IsNullOrEmpty(jsonText))
                return "";

            // Extract clean text from JSON format
            var textMatches = System.Text.RegularExpressions.Regex.Matches(jsonText, @"""text"":""([^""]+)""");
            var cleanTexts = new List<string>();

            foreach (System.Text.RegularExpressions.Match match in textMatches)
            {
                string text = match.Groups[1].Value;
                // Skip formatting characters and empty spaces
                if (!string.IsNullOrWhiteSpace(text) && text != " " && !text.Contains("◆"))
                {
                    cleanTexts.Add(text);
                }
            }

            return string.Join("", cleanTexts.ToArray()).Trim();
        }
        catch (Exception ex)
        {
            LogToConsole($"Error cleaning JSON text: {ex.Message}");
            return jsonText;
        }
    }

    private bool TestDirectDiscordAPI()
    {
        try
        {
            LogToConsole("[Phase 4] Testing direct Discord API access...");
            LogToConsole("[Phase 4] ⚠️ MCC scripting environment limitations detected");
            LogToConsole("[Phase 4] System.Net.WebRequest classes not available in MCC scripts");
            LogToConsole("[Phase 4] Direct HTTP requests are not supported in MCC ChatBot scripts");
            LogToConsole("[Phase 4] This is a security feature to prevent malicious scripts");

            return false; // Always return false - direct API not possible in MCC
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] ⚠️ Direct API test failed: {ex.Message}");
            return false;
        }
    }

    private bool TryStartDiscordBridge()
    {
        try
        {
            LogToConsole("[Phase 4] Attempting to auto-start Discord bridge bot...");
            LogToConsole("[Phase 4] ⚠️ Process execution may be restricted in MCC scripts");
            LogToConsole("[Phase 4] System.Diagnostics.Process may not be available");
            LogToConsole("[Phase 4] Auto-launch capability depends on MCC security settings");

            // Note: System.Diagnostics.Process may not be available in MCC scripting environment
            // This is another security restriction to prevent scripts from executing arbitrary processes

            LogToConsole("[Phase 4] ⚠️ Auto-launch not available in MCC scripting environment");
            LogToConsole("[Phase 4] Please start the Discord bridge bot manually:");
            LogToConsole("[Phase 4] 1. Open Command Prompt in MCC folder");
            LogToConsole("[Phase 4] 2. Run: start-discord-bridge.bat");
            LogToConsole("[Phase 4] 3. Keep bridge bot running alongside MCC");

            return false; // Always return false - process execution not reliable in MCC
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 4] ⚠️ Could not start bridge bot: {ex.Message}");
            return false;
        }
    }

    // === 10-MINUTE SCHEDULING SYSTEM ===

    private void InitializeScheduling()
    {
        try
        {
            LogToConsole("[Scheduling] Initializing 10-minute automatic scheduling...");
            LogToConsole($"[Scheduling] Cycle interval: {CYCLE_INTERVAL_SECONDS} seconds (10 minutes)");

            // Create timer for 10-minute cycles
            scheduledTimer = new System.Threading.Timer(
                ExecuteScheduledCycle,
                null,
                10000, // Start first cycle after 10 seconds (allow bot to fully initialize)
                CYCLE_INTERVAL_SECONDS * 1000 // 10 minutes in milliseconds
            );

            LogToConsole("[Scheduling] ✅ 10-minute scheduling enabled");
            LogToConsole("[Scheduling] 🔄 Bot will execute full cycle every 10 minutes");
            LogToConsole("[Scheduling] ⏰ First cycle will start in 10 seconds");
            LogToConsole("[Scheduling] 📋 Each cycle triggers: Phase 1 → Phase 2 → Phase 3 → Phase 4");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Scheduling] Error initializing scheduling: {ex.Message}");
            LogToConsole("[Scheduling] Scheduling disabled for this session");
            schedulingEnabled = false;
        }
    }

    private void ExecuteScheduledCycle(object state)
    {
        try
        {
            cycleCount++;
            DateTime cycleStartTime = DateTime.Now;
            lastCycleTime = cycleStartTime;

            LogToConsole($"🔄 === SCHEDULED 10-MINUTE CYCLE #{cycleCount} START at {cycleStartTime:HH:mm:ss} ===");

            // Clean up any open inventories before starting new cycle
            CleanupInventoriesBeforeCycle();

            // Skip Discord notification about cycle start to reduce channel clutter

            // Trigger a new task cycle by resetting the next task time
            LogToConsole($"[Cycle {cycleCount}] Triggering new multi-phase task cycle");

            // Force the bot to start a new cycle immediately
            nextTaskRun = DateTime.Now;

            // Reset to idle state to trigger the normal phase sequence
            if (currentState != BotState.Idle)
            {
                LogToConsole($"[Cycle {cycleCount}] Resetting bot state to Idle to start fresh cycle");
                ChangeState(BotState.Idle);
            }

            LogToConsole($"[Cycle {cycleCount}] Scheduled cycle will execute through normal state machine");
            LogToConsole($"[Cycle {cycleCount}] Bot will automatically execute Phase 1 → Phase 2 → Phase 3 → Phase 4");
            LogToConsole($"[Cycle {cycleCount}] State machine will handle cycle completion when all phases finish");

            // Note: Do NOT send cycle completion here - let the normal state machine handle it
            // The ScheduleNextTask() method will send completion notifications when appropriate
        }
        catch (Exception ex)
        {
            LogToConsole($"[Cycle {cycleCount}] Error during scheduled cycle: {ex.Message}");
        }
    }

    private void CleanupInventoriesBeforeCycle()
    {
        try
        {
            LogToConsole("[Cleanup] 🧹 Cleaning up inventories before starting new cycle...");

            var inventories = GetInventories();
            if (inventories == null || inventories.Count <= 1)
            {
                LogToConsole("[Cleanup] No non-player inventories found to clean up");
                return;
            }

            int closedCount = 0;
            foreach (var kvp in inventories)
            {
                int inventoryId = kvp.Key;
                var container = kvp.Value;

                // Close any non-player inventory
                if (container.Type != ContainerType.PlayerInventory)
                {
                    LogToConsole($"[Cleanup] Closing inventory #{inventoryId} (Type: {container.Type}, Items: {container.Items.Count})");
                    bool success = CloseInventory(inventoryId);

                    if (success)
                    {
                        LogToConsole($"[Cleanup] ✅ Successfully closed inventory #{inventoryId}");
                        closedCount++;
                    }
                    else
                    {
                        LogToConsole($"[Cleanup] ❌ Failed to close inventory #{inventoryId}");
                    }
                }
            }

            LogToConsole($"[Cleanup] Inventory cleanup completed. Closed {closedCount} inventories.");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Cleanup] Error during inventory cleanup: {ex.Message}");
        }
    }

    private string CreateCycleStartEmbed(int cycleNumber, DateTime startTime)
    {
        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = $"🔄 Multi-Phase Bot - 10 Minute Cycle #{cycleNumber}",
                    description = $"Automated data collection cycle started at {startTime:HH:mm:ss}",
                    color = 3447003, // Blue
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "📊 Cycle Status",
                            value = "🔄 Phase 1: Starting /istop command\\n⏳ Phase 2: Pending island data extraction\\n⏳ Phase 3: Pending leaderboard data extraction",
                            inline = false
                        },
                        new
                        {
                            name = "⏰ Cycle Info",
                            value = $"Cycle #{cycleNumber}\nStarted: {startTime:HH:mm:ss}",
                            inline = true
                        },
                        new
                        {
                            name = "🎯 Expected Duration",
                            value = "~30-60 seconds",
                            inline = true
                        }
                    },
                    footer = new
                    {
                        text = "Multi-Phase Bot • 10-Minute Automation",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/bot-icon.png"
                    }
                }
            }
        };

        return System.Text.Json.JsonSerializer.Serialize(embed);
    }

    private string CreateCycleCompleteEmbed(int cycleNumber, TimeSpan duration, DateTime nextCycle)
    {
        var embed = new
        {
            embeds = new[]
            {
                new
                {
                    title = $"✅ Multi-Phase Bot - Cycle #{cycleNumber} Complete",
                    description = $"Automated data collection cycle completed successfully",
                    color = 65280, // Green
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    fields = new[]
                    {
                        new
                        {
                            name = "📊 Cycle Results",
                            value = "✅ Phase 1: /istop command executed\\n✅ Phase 2: Island data extracted\\n✅ Phase 3: Leaderboard data extracted\\n✅ Phase 4: Discord embeds sent",
                            inline = false
                        },
                        new
                        {
                            name = "⏱️ Performance",
                            value = $"Duration: {duration.TotalSeconds:F1}s\nCycle #{cycleNumber}",
                            inline = true
                        },
                        new
                        {
                            name = "⏰ Next Cycle",
                            value = $"Scheduled for {nextCycle:HH:mm:ss}",
                            inline = true
                        }
                    },
                    footer = new
                    {
                        text = "Multi-Phase Bot • 10-Minute Automation",
                        icon_url = "https://cdn.discordapp.com/attachments/123456789/bot-icon.png"
                    }
                }
            }
        };

        return System.Text.Json.JsonSerializer.Serialize(embed);
    }

    // === PHASE 2 DATA EXTRACTION ===

    private void ExtractIslandData()
    {
        try
        {
            LogToConsole("[Phase 2] *** STARTING ISLAND DATA EXTRACTION ***");

            if (!GetInventoryEnabled())
            {
                LogToConsole("[Phase 2] ERROR: Inventory handling is not enabled!");
                LogToConsole("[Phase 2] Skipping Phase 2 and returning to idle...");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
                return;
            }

            var inventories = GetInventories();
            if (inventories == null || inventories.Count <= 1)
            {
                LogToConsole("[Phase 2] No island inventory found");
                LogToConsole("[Phase 2] Completing Phase 2 and returning to idle...");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
                return;
            }

            LogToConsole($"[Phase 2] Found {inventories.Count} inventories, analyzing for island data...");

            // Validate that we have the correct inventory (island data, not leaderboard data)
            if (!ValidateIslandInventory(inventories))
            {
                LogToConsole("[Phase 2] ❌ WRONG INVENTORY DETECTED! This appears to be leaderboard data, not island data.");
                LogToConsole("[Phase 2] Skipping island data extraction to prevent incorrect data processing.");
                LogToConsole("[Phase 2] Completing Phase 2 and returning to idle...");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
                return;
            }

            LogToConsole($"[Phase 2] Found {inventories.Count} inventories, analyzing for island data...");

            // Find the island inventory (not the player inventory)
            Container islandInventory = null;
            int islandInventoryId = -1;

            foreach (var kvp in inventories)
            {
                var container = kvp.Value;
                if (container.Type != ContainerType.PlayerInventory)
                {
                    LogToConsole($"[Phase 2] Checking inventory #{kvp.Key}: Type={container.Type}, Items={container.Items.Count}");

                    // Check if this looks like an island inventory by examining items
                    if (IsIslandInventory(container))
                    {
                        islandInventory = container;
                        islandInventoryId = kvp.Key;
                        LogToConsole($"[Phase 2] ✅ Using inventory #{kvp.Key} (Type: {container.Type}) for island data");
                        break;
                    }
                }
            }

            if (islandInventory == null)
            {
                LogToConsole("[Phase 2] No suitable island inventory found");

                // Fallback: Use any non-player inventory
                foreach (var kvp in inventories)
                {
                    var container = kvp.Value;
                    if (container.Type != ContainerType.PlayerInventory)
                    {
                        LogToConsole($"[Phase 2] ⚠️ FALLBACK: Using inventory #{kvp.Key} as fallback");
                        islandInventory = container;
                        islandInventoryId = kvp.Key;
                        break;
                    }
                }

                if (islandInventory == null)
                {
                    LogToConsole("[Phase 2] No inventories available at all, completing Phase 2...");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                    return;
                }
            }

            LogToConsole($"[Phase 2] ✅ Using inventory #{islandInventoryId} for island data extraction");
            LogToConsole($"[Phase 2] Inventory type: {islandInventory.Type}, Items: {islandInventory.Items.Count}");

            // Extract island data from the selected inventory
            ExtractIslandFromInventory(islandInventory, islandInventoryId);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error in island data extraction: {ex.Message}");
        }
    }

    private bool IsIslandInventory(Container inventory)
    {
        try
        {
            // Check for characteristic island inventory items
            foreach (var kvp in inventory.Items)
            {
                Item item = kvp.Value;
                if (item != null && !string.IsNullOrEmpty(item.DisplayName))
                {
                    string displayName = item.DisplayName.ToLower();

                    // Look for island-specific indicators
                    if (displayName.Contains("top 10 islands") ||
                        displayName.Contains("island ranking") ||
                        displayName.Contains("islands:") ||
                        displayName.Contains("revenants") ||
                        displayName.Contains("fakeduo") ||
                        displayName.Contains("island #"))
                    {
                        LogToConsole($"[Phase 2] Island inventory detected - found item: '{item.DisplayName}'");
                        return true;
                    }

                    // Check for PlayerHead items in typical island slots (13, 21, 23, 29, 31)
                    if (item.Type == ItemType.PlayerHead &&
                        (kvp.Key == 13 || kvp.Key == 21 || kvp.Key == 23 || kvp.Key == 29 || kvp.Key == 31))
                    {
                        // Check if the display name matches island patterns
                        if (displayName.Contains("#1:") || displayName.Contains("#2:") ||
                            displayName.Contains("#3:") || displayName.Contains("#4:") || displayName.Contains("#5:"))
                        {
                            LogToConsole($"[Phase 2] Island inventory detected - found ranked island in slot {kvp.Key}: '{item.DisplayName}'");
                            return true;
                        }
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error checking if inventory is island inventory: {ex.Message}");
            return false;
        }
    }

    private bool ValidateIslandInventory(Dictionary<int, Container> inventories)
    {
        try
        {
            LogToConsole("[Phase 2] 🔍 VALIDATING INVENTORY TYPE...");

            foreach (var kvp in inventories)
            {
                var container = kvp.Value;
                if (container.Type == ContainerType.PlayerInventory)
                    continue;

                LogToConsole($"[Phase 2] Checking inventory #{kvp.Key}: Type={container.Type}, Items={container.Items.Count}");

                // Sample a few items to determine inventory type
                int itemsChecked = 0;
                int leaderboardIndicators = 0;
                int islandIndicators = 0;

                foreach (var itemKvp in container.Items)
                {
                    var item = itemKvp.Value;
                    if (item == null || string.IsNullOrEmpty(item.DisplayName))
                        continue;

                    string cleanName = RemoveMinecraftColorCodes(item.DisplayName).ToLower();
                    LogToConsole($"[Phase 2] Sample item: '{cleanName}'");

                    // Check for leaderboard indicators
                    if (cleanName.Contains("runes identified") ||
                        cleanName.Contains("pouches opened") ||
                        cleanName.Contains("votes") ||
                        cleanName.Contains("quests completed") ||
                        cleanName.Contains("experience gained") ||
                        cleanName.Contains("mobs killed") ||
                        cleanName.Contains("blocks placed") ||
                        cleanName.Contains("fish caught") ||
                        cleanName.Contains("main menu"))
                    {
                        leaderboardIndicators++;
                        LogToConsole($"[Phase 2] ⚠️ LEADERBOARD INDICATOR FOUND: '{cleanName}'");
                    }

                    // Check for island indicators
                    if (cleanName.Contains("top") && cleanName.Contains("islands") ||
                        cleanName.Contains("island #") ||
                        cleanName.Contains("revenants") ||
                        cleanName.Contains("nip") ||
                        cleanName.Contains("minecontrol") ||
                        cleanName.Contains("points"))
                    {
                        islandIndicators++;
                        LogToConsole($"[Phase 2] ✅ ISLAND INDICATOR FOUND: '{cleanName}'");
                    }

                    itemsChecked++;
                    if (itemsChecked >= 10) // Check first 10 items
                        break;
                }

                LogToConsole($"[Phase 2] Validation results: {leaderboardIndicators} leaderboard indicators, {islandIndicators} island indicators");

                // If we found more leaderboard indicators than island indicators, this is wrong inventory
                if (leaderboardIndicators > islandIndicators && leaderboardIndicators > 0)
                {
                    LogToConsole($"[Phase 2] ❌ INVENTORY VALIDATION FAILED: This is a leaderboard inventory!");
                    return false;
                }
            }

            LogToConsole("[Phase 2] ✅ INVENTORY VALIDATION PASSED: This appears to be island data");
            return true;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error validating inventory: {ex.Message}");
            return true; // Default to allowing extraction if validation fails
        }
    }

    private void ExtractIslandFromInventory(Container inventory, int inventoryId)
    {
        try
        {
            LogToConsole($"[Phase 2] Extracting island data from inventory #{inventoryId}");

            // Clear current data
            currentIslandData.Clear();

            // Extract data from each island slot
            foreach (int slot in islandDataSlots)
            {
                if (inventory.Items.ContainsKey(slot))
                {
                    Item item = inventory.Items[slot];
                    if (item != null)
                    {
                        ExtractIslandFromItem(item, slot);
                    }
                }
            }

            // Compare with previous data and log changes
            CompareAndLogChanges();

            // Save current data as previous for next comparison
            SaveCurrentDataAsPrevious();

            LogToConsole($"[Phase 2] Island data extraction completed - extracted {currentIslandData.Count} islands");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error extracting island data from inventory: {ex.Message}");
        }
    }

    private void ExtractIslandFromItem(Item item, int slot)
    {
        try
        {
            if (item == null || string.IsNullOrEmpty(item.DisplayName))
                return;

            LogToConsole($"[Phase 2] Processing slot {slot}: {item.DisplayName}");

            // Clean the display name by removing Minecraft color codes
            string cleanName = RemoveMinecraftColorCodes(item.DisplayName);

            // Parse island data from the item
            var islandData = new IslandData
            {
                Name = cleanName,
                Slot = slot,
                LastUpdated = DateTime.Now
            };

            // Extract ranking from slot position
            int ranking = Array.IndexOf(islandDataSlots, slot) + 1;
            islandData.Ranking = ranking;

            // Try to extract values from NBT lore data
            if (item.NBT != null && item.NBT.ContainsKey("display"))
            {
                var displayData = item.NBT["display"] as Dictionary<string, object>;
                if (displayData != null && displayData.ContainsKey("Lore"))
                {
                    var loreData = displayData["Lore"];
                    if (loreData is System.Collections.IEnumerable loreList && !(loreData is string))
                    {
                        if (ENABLE_DEBUG_LOGGING)
                            LogToConsole($"[Phase 2] DEBUG: Processing lore data for slot {slot}");
                        int loreLineCount = 0;

                        foreach (var loreItem in loreList)
                        {
                            string loreLine = loreItem?.ToString() ?? "";
                            string cleanLore = RemoveMinecraftColorCodes(loreLine).ToLower().Trim();
                            loreLineCount++;

                            if (ENABLE_DEBUG_LOGGING)
                                LogToConsole($"[Phase 2] DEBUG: Lore line {loreLineCount}: '{cleanLore}'");

                            if (cleanLore.Contains("all time") && cleanLore.Contains(":"))
                            {
                                islandData.AllTimeValue = ExtractValueFromLore(loreLine);
                                if (ENABLE_DEBUG_LOGGING)
                                    LogToConsole($"[Phase 2] DEBUG: Found All Time: {islandData.AllTimeValue}");
                            }
                            else if (cleanLore.Contains("weekly") && cleanLore.Contains(":"))
                            {
                                islandData.WeeklyValue = ExtractValueFromLore(loreLine);
                                islandData.WeeklyValueNumeric = ParseNumericValue(islandData.WeeklyValue);
                                if (ENABLE_DEBUG_LOGGING)
                                    LogToConsole($"[Phase 2] DEBUG: Found Weekly: {islandData.WeeklyValue}");
                            }
                            else if (cleanLore.Contains("today") && cleanLore.Contains(":"))
                            {
                                islandData.TodayChange = ExtractValueFromLore(loreLine);
                                if (ENABLE_DEBUG_LOGGING)
                                    LogToConsole($"[Phase 2] DEBUG: Found Today: {islandData.TodayChange}");
                            }
                            // Try alternative patterns for weekly data
                            else if (cleanLore.Contains("week") && cleanLore.Contains(":"))
                            {
                                islandData.WeeklyValue = ExtractValueFromLore(loreLine);
                                islandData.WeeklyValueNumeric = ParseNumericValue(islandData.WeeklyValue);
                                if (ENABLE_DEBUG_LOGGING)
                                    LogToConsole($"[Phase 2] DEBUG: Found Week (alt): {islandData.WeeklyValue}");
                            }
                            // Try to find any monetary values that might be weekly data
                            else if (cleanLore.Contains("$") && (cleanLore.Contains("m") || cleanLore.Contains("b") || cleanLore.Contains("k")))
                            {
                                if (ENABLE_DEBUG_LOGGING)
                                    LogToConsole($"[Phase 2] DEBUG: Found monetary value: '{cleanLore}'");
                                // If we don't have weekly data yet, this might be it
                                if (string.IsNullOrEmpty(islandData.WeeklyValue) && !cleanLore.Contains("all time") && !cleanLore.Contains("today"))
                                {
                                    islandData.WeeklyValue = ExtractValueFromLore(loreLine);
                                    islandData.WeeklyValueNumeric = ParseNumericValue(islandData.WeeklyValue);
                                    if (ENABLE_DEBUG_LOGGING)
                                        LogToConsole($"[Phase 2] DEBUG: Assigned as Weekly (fallback): {islandData.WeeklyValue}");
                                }
                            }
                        }

                        if (ENABLE_DEBUG_LOGGING)
                            LogToConsole($"[Phase 2] DEBUG: Processed {loreLineCount} lore lines for slot {slot}");
                    }
                }
            }

            // If no lore data found, try to extract from display name
            if (string.IsNullOrEmpty(islandData.AllTimeValue))
            {
                ExtractValuesFromDisplayName(cleanName, islandData);
            }

            // Store the island data
            string key = $"slot_{slot}";
            currentIslandData[key] = islandData;

            LogToConsole($"[Phase 2] Extracted island #{ranking}: {islandData.Name}");
            LogToConsole($"[Phase 2]   All Time: {islandData.AllTimeValue}");
            LogToConsole($"[Phase 2]   Weekly: {islandData.WeeklyValue}");
            LogToConsole($"[Phase 2]   Today: {islandData.TodayChange}");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error extracting island from item in slot {slot}: {ex.Message}");
        }
    }

    private string RemoveMinecraftColorCodes(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        // ENHANCED DEBUG: Log when this method is called
        string originalText = text;
        LogToConsole($"[ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {text.Substring(0, Math.Min(50, text.Length))}...");

        string result = text;

        // Remove Minecraft color codes (§ followed by any character)
        for (int i = 0; i < result.Length - 1; i++)
        {
            if (result[i] == '§')
            {
                result = result.Remove(i, 2);
                i--; // Adjust index after removal
            }
        }

        // CRITICAL FIX: Remove control characters that cause JSON parsing errors
        string beforeCleaning = result;
        result = CleanControlCharactersFromSourceData(result);

        // ENHANCED DEBUG: Log control character cleaning results
        if (beforeCleaning != result)
        {
            LogToConsole($"[ENHANCED DEBUG] Control characters cleaned: '{beforeCleaning}' -> '{result}'");

            // Log specific control characters found
            for (int i = 0; i < beforeCleaning.Length; i++)
            {
                char c = beforeCleaning[i];
                if (c < 32 && c != '\n' && c != '\r' && c != '\t')
                {
                    LogToConsole($"[ENHANCED DEBUG] Found control character at pos {i}: 0x{(int)c:X2} (ASCII {(int)c})");
                }
            }
        }
        else
        {
            LogToConsole($"[ENHANCED DEBUG] No control characters found in: {beforeCleaning.Substring(0, Math.Min(30, beforeCleaning.Length))}...");
        }

        // Remove corrupted Unicode characters (������)
        result = result.Replace("������", "");
        result = result.Replace("����", "");
        result = result.Replace("���", "");
        result = result.Replace("��", "");

        // Remove other common corrupted characters
        result = result.Replace("�", "");

        // Clean up multiple spaces
        while (result.Contains("  "))
        {
            result = result.Replace("  ", " ");
        }

        string finalResult = result.Trim();

        // ENHANCED DEBUG: Log final result
        if (originalText != finalResult)
        {
            LogToConsole($"[ENHANCED DEBUG] RemoveMinecraftColorCodes result: '{originalText}' -> '{finalResult}'");
        }

        return finalResult;
    }

    private void ExtractValuesFromDisplayName(string displayName, IslandData islandData)
    {
        try
        {
            // Try to extract island name and basic info from display name
            // Format might be like "#1: Revenants (0 points)" or similar
            if (displayName.Contains(":"))
            {
                string[] parts = displayName.Split(':');
                if (parts.Length >= 2)
                {
                    string namePart = parts[1].Trim();

                    // Remove points info if present
                    if (namePart.Contains("(") && namePart.Contains("points"))
                    {
                        int parenIndex = namePart.IndexOf("(");
                        namePart = namePart.Substring(0, parenIndex).Trim();
                    }

                    // Update the name to be cleaner
                    islandData.Name = $"#{islandData.Ranking}: {namePart}";
                }
            }

            // Set default values if not found in lore
            if (string.IsNullOrEmpty(islandData.AllTimeValue))
                islandData.AllTimeValue = "Extracting...";
            if (string.IsNullOrEmpty(islandData.WeeklyValue))
                islandData.WeeklyValue = "Extracting...";
            if (string.IsNullOrEmpty(islandData.TodayChange))
                islandData.TodayChange = "Extracting...";

            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] DEBUG: Fallback values set for {islandData.Name}");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error extracting values from display name: {ex.Message}");
        }
    }

    private string ExtractValueFromLore(string loreLine)
    {
        try
        {
            // Remove Minecraft color codes first
            string cleanLine = RemoveMinecraftColorCodes(loreLine);
            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] DEBUG: ExtractValueFromLore input: '{loreLine}' -> clean: '{cleanLine}'");

            // First, try to extract clean text from JSON format
            string cleanText = ExtractTextFromJsonLore(cleanLine);

            if (!string.IsNullOrEmpty(cleanText))
            {
                // Find the colon and extract the value after it
                int colonIndex = cleanText.IndexOf(':');
                if (colonIndex >= 0 && colonIndex < cleanText.Length - 1)
                {
                    string result = cleanText.Substring(colonIndex + 1).Trim();
                    if (ENABLE_DEBUG_LOGGING)
                        LogToConsole($"[Phase 2] DEBUG: Extracted from JSON: '{result}'");
                    return result;
                }
                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] DEBUG: No colon found in JSON text: '{cleanText}'");
                return cleanText; // Return full clean text if no colon found
            }

            // Fallback to original method if JSON extraction fails
            int colonIndexOriginal = cleanLine.IndexOf(':');
            if (colonIndexOriginal >= 0 && colonIndexOriginal < cleanLine.Length - 1)
            {
                string result = cleanLine.Substring(colonIndexOriginal + 1).Trim();
                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] DEBUG: Extracted from direct parsing: '{result}'");
                return result;
            }

            // If no colon found, check if the entire line contains monetary values
            if (cleanLine.Contains("$") && (cleanLine.Contains("m") || cleanLine.Contains("b") || cleanLine.Contains("k")))
            {
                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] DEBUG: Found monetary value without colon: '{cleanLine}'");
                return cleanLine;
            }

            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] DEBUG: No extractable value found in: '{cleanLine}'");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error extracting value from lore: {ex.Message}");
        }
        return "";
    }

    private double ParseNumericValue(string value)
    {
        try
        {
            if (string.IsNullOrEmpty(value))
                return 0;

            // Remove common formatting characters
            string cleanValue = value.Replace(",", "").Replace("$", "").Replace(" ", "");

            // Handle suffixes like K, M, B
            double multiplier = 1;
            if (cleanValue.EndsWith("k", StringComparison.OrdinalIgnoreCase))
            {
                multiplier = 1000;
                cleanValue = cleanValue.Substring(0, cleanValue.Length - 1);
            }
            else if (cleanValue.EndsWith("m", StringComparison.OrdinalIgnoreCase))
            {
                multiplier = 1000000;
                cleanValue = cleanValue.Substring(0, cleanValue.Length - 1);
            }
            else if (cleanValue.EndsWith("b", StringComparison.OrdinalIgnoreCase))
            {
                multiplier = 1000000000;
                cleanValue = cleanValue.Substring(0, cleanValue.Length - 1);
            }

            if (double.TryParse(cleanValue, out double result))
            {
                return result * multiplier;
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error parsing numeric value '{value}': {ex.Message}");
        }
        return 0;
    }

    private void CompareAndLogChanges()
    {
        try
        {
            LogToConsole("[Phase 2] Comparing with previous data...");

            // Use static data if available, otherwise use instance data
            var previousData = staticPreviousIslandData.Count > 0 ? staticPreviousIslandData : previousIslandData;

            if (previousData.Count == 0)
            {
                LogToConsole("[Phase 2] No previous data available for comparison");
                return;
            }

            foreach (var kvp in currentIslandData)
            {
                string key = kvp.Key;
                var currentIsland = kvp.Value;

                if (previousData.ContainsKey(key))
                {
                    var previousIsland = previousData[key];

                    // Compare weekly values
                    if (currentIsland.WeeklyValueNumeric != previousIsland.WeeklyValueNumeric)
                    {
                        double change = currentIsland.WeeklyValueNumeric - previousIsland.WeeklyValueNumeric;
                        string changeStr = change > 0 ? $"+{change:F0}" : $"{change:F0}";
                        LogToConsole($"[Phase 2] CHANGE: {currentIsland.Name} weekly: {previousIsland.WeeklyValue} -> {currentIsland.WeeklyValue} ({changeStr})");
                    }
                }
                else
                {
                    LogToConsole($"[Phase 2] NEW: {currentIsland.Name} (#{currentIsland.Ranking})");
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error comparing data: {ex.Message}");
        }
    }

    private void SaveCurrentDataAsPrevious()
    {
        try
        {
            // Save to both instance and static storage
            previousIslandData.Clear();
            staticPreviousIslandData.Clear();

            foreach (var kvp in currentIslandData)
            {
                previousIslandData[kvp.Key] = kvp.Value;
                staticPreviousIslandData[kvp.Key] = kvp.Value;
            }

            lastDataExtraction = DateTime.Now;

            // Save to historical data
            SaveToHistoricalData();

            LogToConsole($"[Phase 2] Saved {currentIslandData.Count} islands as previous data");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error saving current data as previous: {ex.Message}");
        }
    }

    private void SaveToHistoricalData()
    {
        try
        {
            DateTime now = DateTime.Now;

            foreach (var kvp in currentIslandData)
            {
                var island = kvp.Value;
                var historicalEntry = new HistoricalIslandData
                {
                    Timestamp = now,
                    IslandName = island.Name,
                    AllTimeValue = island.AllTimeValue,
                    WeeklyValue = island.WeeklyValue,
                    TodayChange = island.TodayChange,
                    Ranking = island.Ranking,
                    WeeklyValueNumeric = island.WeeklyValueNumeric
                };

                historicalIslandData.Add(historicalEntry);
            }

            // Keep only last 30 days of data (assuming 10-minute intervals = 144 entries per day)
            int maxEntries = 144 * 30; // 30 days
            if (historicalIslandData.Count > maxEntries)
            {
                historicalIslandData = historicalIslandData
                    .OrderByDescending(h => h.Timestamp)
                    .Take(maxEntries)
                    .ToList();
            }

            LogToConsole($"[Phase 2] Added historical data entry. Total entries: {historicalIslandData.Count}");

            // Save to disk for persistence across bot restarts
            SaveHistoricalDataToDisk();
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error saving to historical data: {ex.Message}");
        }
    }

    private void LoadHistoricalData()
    {
        try
        {
            if (!System.IO.File.Exists(HISTORICAL_DATA_FILE))
            {
                LogToConsole("[Phase 2] No historical data file found - starting fresh");
                return;
            }

            string jsonContent = System.IO.File.ReadAllText(HISTORICAL_DATA_FILE);
            if (string.IsNullOrEmpty(jsonContent.Trim()))
            {
                LogToConsole("[Phase 2] Historical data file is empty - starting fresh");
                return;
            }

            // Parse JSON manually since we're in MCC environment
            var loadedData = ParseHistoricalDataJson(jsonContent);

            if (loadedData != null && loadedData.Count > 0)
            {
                historicalIslandData = loadedData;
                LogToConsole($"[Phase 2] ✅ Loaded {historicalIslandData.Count} historical data entries from disk");

                // Show some statistics
                var oldestEntry = historicalIslandData.OrderBy(h => h.Timestamp).FirstOrDefault();
                var newestEntry = historicalIslandData.OrderByDescending(h => h.Timestamp).FirstOrDefault();

                if (oldestEntry != null && newestEntry != null)
                {
                    LogToConsole($"[Phase 2] Historical data range: {oldestEntry.Timestamp:yyyy-MM-dd HH:mm} to {newestEntry.Timestamp:yyyy-MM-dd HH:mm}");

                    var uniqueIslands = historicalIslandData.Select(h => h.IslandName).Distinct().Count();
                    LogToConsole($"[Phase 2] Tracking {uniqueIslands} unique islands in historical data");
                }
            }
            else
            {
                LogToConsole("[Phase 2] No valid historical data found in file - starting fresh");
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error loading historical data: {ex.Message}");
            LogToConsole("[Phase 2] Starting with empty historical data");
            historicalIslandData = new List<HistoricalIslandData>();
        }
    }

    private void SaveHistoricalDataToDisk()
    {
        try
        {
            // Create JSON manually for MCC compatibility
            var jsonBuilder = new System.Text.StringBuilder();
            jsonBuilder.AppendLine("[");

            for (int i = 0; i < historicalIslandData.Count; i++)
            {
                var entry = historicalIslandData[i];
                jsonBuilder.AppendLine("  {");
                jsonBuilder.AppendLine($"    \"Timestamp\": \"{entry.Timestamp:yyyy-MM-ddTHH:mm:ss.fffZ}\",");
                jsonBuilder.AppendLine($"    \"IslandName\": \"{EscapeJsonString(entry.IslandName)}\",");
                jsonBuilder.AppendLine($"    \"AllTimeValue\": \"{EscapeJsonString(entry.AllTimeValue)}\",");
                jsonBuilder.AppendLine($"    \"WeeklyValue\": \"{EscapeJsonString(entry.WeeklyValue)}\",");
                jsonBuilder.AppendLine($"    \"TodayChange\": \"{EscapeJsonString(entry.TodayChange)}\",");
                jsonBuilder.AppendLine($"    \"Ranking\": {entry.Ranking},");
                jsonBuilder.AppendLine($"    \"WeeklyValueNumeric\": {entry.WeeklyValueNumeric}");

                if (i < historicalIslandData.Count - 1)
                    jsonBuilder.AppendLine("  },");
                else
                    jsonBuilder.AppendLine("  }");
            }

            jsonBuilder.AppendLine("]");

            System.IO.File.WriteAllText(HISTORICAL_DATA_FILE, jsonBuilder.ToString());

            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] Historical data saved to disk ({historicalIslandData.Count} entries)");
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error saving historical data to disk: {ex.Message}");
        }
    }

    private List<HistoricalIslandData> ParseHistoricalDataJson(string jsonContent)
    {
        try
        {
            var result = new List<HistoricalIslandData>();

            // Simple JSON parsing for MCC environment
            // Remove brackets and split by entries
            jsonContent = jsonContent.Trim();
            if (jsonContent.StartsWith("[")) jsonContent = jsonContent.Substring(1);
            if (jsonContent.EndsWith("]")) jsonContent = jsonContent.Substring(0, jsonContent.Length - 1);

            // Split by object boundaries
            var entries = jsonContent.Split(new string[] { "},", "}" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var entry in entries)
            {
                if (string.IsNullOrWhiteSpace(entry)) continue;

                var cleanEntry = entry.Trim();
                if (cleanEntry.StartsWith("{")) cleanEntry = cleanEntry.Substring(1);

                var historicalEntry = new HistoricalIslandData();

                // Parse each field
                var lines = cleanEntry.Split('\n');
                foreach (var line in lines)
                {
                    var cleanLine = line.Trim().TrimEnd(',');
                    if (string.IsNullOrEmpty(cleanLine)) continue;

                    if (cleanLine.Contains("\"Timestamp\":"))
                    {
                        var value = ExtractJsonValue(cleanLine);
                        if (DateTime.TryParse(value, out DateTime timestamp))
                            historicalEntry.Timestamp = timestamp;
                    }
                    else if (cleanLine.Contains("\"IslandName\":"))
                    {
                        historicalEntry.IslandName = ExtractJsonValue(cleanLine);
                    }
                    else if (cleanLine.Contains("\"AllTimeValue\":"))
                    {
                        historicalEntry.AllTimeValue = ExtractJsonValue(cleanLine);
                    }
                    else if (cleanLine.Contains("\"WeeklyValue\":"))
                    {
                        historicalEntry.WeeklyValue = ExtractJsonValue(cleanLine);
                    }
                    else if (cleanLine.Contains("\"TodayChange\":"))
                    {
                        historicalEntry.TodayChange = ExtractJsonValue(cleanLine);
                    }
                    else if (cleanLine.Contains("\"Ranking\":"))
                    {
                        var value = ExtractJsonValue(cleanLine);
                        if (int.TryParse(value, out int ranking))
                            historicalEntry.Ranking = ranking;
                    }
                    else if (cleanLine.Contains("\"WeeklyValueNumeric\":"))
                    {
                        var value = ExtractJsonValue(cleanLine);
                        if (double.TryParse(value, out double numericValue))
                            historicalEntry.WeeklyValueNumeric = numericValue;
                    }
                }

                // Only add if we have essential data
                if (!string.IsNullOrEmpty(historicalEntry.IslandName) && historicalEntry.Timestamp != DateTime.MinValue)
                {
                    result.Add(historicalEntry);
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error parsing historical data JSON: {ex.Message}");
            return new List<HistoricalIslandData>();
        }
    }

    private string ExtractJsonValue(string jsonLine)
    {
        try
        {
            // Extract value from "key": "value" or "key": value
            var colonIndex = jsonLine.IndexOf(':');
            if (colonIndex == -1) return "";

            var value = jsonLine.Substring(colonIndex + 1).Trim();

            // Remove quotes if present
            if (value.StartsWith("\"") && value.EndsWith("\""))
                value = value.Substring(1, value.Length - 2);

            return value;
        }
        catch
        {
            return "";
        }
    }

    private string EscapeJsonString(string input)
    {
        if (string.IsNullOrEmpty(input)) return "";

        return input
            .Replace("\\", "\\\\")
            .Replace("\"", "\\\"")
            .Replace("\n", "\\n")
            .Replace("\r", "\\r")
            .Replace("\t", "\\t");
    }

    private string FormatMonetaryValue(double value)
    {
        try
        {
            double absValue = Math.Abs(value);
            string sign = value >= 0 ? "+" : "-";

            if (absValue >= 1000000000) // Billions
            {
                return $"{sign}${absValue / 1000000000:F1}B";
            }
            else if (absValue >= 1000000) // Millions
            {
                return $"{sign}${absValue / 1000000:F1}M";
            }
            else if (absValue >= 1000) // Thousands
            {
                return $"{sign}${absValue / 1000:F1}K";
            }
            else // Less than 1000
            {
                return $"{sign}${absValue:F0}";
            }
        }
        catch
        {
            return value >= 0 ? $"+${value:F0}" : $"-${Math.Abs(value):F0}";
        }
    }

    private void TestMonetaryFormatting()
    {
        // Test the monetary formatting function
        LogToConsole("[Test] Testing monetary formatting:");
        LogToConsole($"[Test] 3680000 -> {FormatMonetaryValue(3680000)}");
        LogToConsole($"[Test] -2100000 -> {FormatMonetaryValue(-2100000)}");
        LogToConsole($"[Test] 1500000000 -> {FormatMonetaryValue(1500000000)}");
        LogToConsole($"[Test] 250000 -> {FormatMonetaryValue(250000)}");
        LogToConsole($"[Test] 500 -> {FormatMonetaryValue(500)}");
        LogToConsole($"[Test] -750 -> {FormatMonetaryValue(-750)}");
    }

    private string CalculateGrowthMetrics(IslandData island)
    {
        try
        {
            if (historicalIslandData.Count == 0)
                return "No historical data available";

            var islandHistory = historicalIslandData
                .Where(h => h.IslandName == island.Name)
                .OrderByDescending(h => h.Timestamp)
                .ToList();

            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] Growth calculation for {island.Name}: {islandHistory.Count} historical entries");

            if (islandHistory.Count < 2)
                return "Insufficient historical data";

            var current = island.WeeklyValueNumeric;
            var metrics = new List<string>();

            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] Current weekly value: ${current:F0}");

            // Last snapshot growth (10 minutes ago)
            if (islandHistory.Count >= 2)
            {
                var lastSnapshot = islandHistory[1].WeeklyValueNumeric;
                var change = current - lastSnapshot;
                string formatted = FormatMonetaryValue(change);
                metrics.Add($"Last: {formatted}");

                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] Last snapshot: ${lastSnapshot:F0} -> Change: ${change:F0} -> Formatted: {formatted}");
            }

            // 1 hour growth (6 snapshots ago at 10-minute intervals)
            var oneHourAgo = islandHistory.FirstOrDefault(h => h.Timestamp <= DateTime.Now.AddHours(-1));
            if (oneHourAgo != null)
            {
                var change = current - oneHourAgo.WeeklyValueNumeric;
                string formatted = FormatMonetaryValue(change);
                metrics.Add($"1h: {formatted}");

                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] 1h ago: ${oneHourAgo.WeeklyValueNumeric:F0} -> Change: ${change:F0} -> Formatted: {formatted}");
            }

            // 24 hour growth (144 snapshots ago at 10-minute intervals)
            var oneDayAgo = islandHistory.FirstOrDefault(h => h.Timestamp <= DateTime.Now.AddDays(-1));
            if (oneDayAgo != null)
            {
                var change = current - oneDayAgo.WeeklyValueNumeric;
                string formatted = FormatMonetaryValue(change);
                metrics.Add($"24h: {formatted}");

                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] 24h ago: ${oneDayAgo.WeeklyValueNumeric:F0} -> Change: ${change:F0} -> Formatted: {formatted}");
            }

            // 3 day growth (432 snapshots ago at 10-minute intervals)
            var threeDaysAgo = islandHistory.FirstOrDefault(h => h.Timestamp <= DateTime.Now.AddDays(-3));
            if (threeDaysAgo != null)
            {
                var change = current - threeDaysAgo.WeeklyValueNumeric;
                string formatted = FormatMonetaryValue(change);
                metrics.Add($"3d: {formatted}");

                if (ENABLE_DEBUG_LOGGING)
                    LogToConsole($"[Phase 2] 3d ago: ${threeDaysAgo.WeeklyValueNumeric:F0} -> Change: ${change:F0} -> Formatted: {formatted}");
            }

            string result = metrics.Count > 0 ? string.Join(" • ", metrics) : "No growth data";

            if (ENABLE_DEBUG_LOGGING)
                LogToConsole($"[Phase 2] Final growth metrics: {result}");

            return result;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 2] Error calculating growth metrics: {ex.Message}");
            return "Error calculating growth";
        }
    }

    // === PHASE 3 DATA EXTRACTION ===

    private void ExtractLeaderboardData()
    {
        try
        {
            LogToConsole("[Phase 3] *** STARTING LEADERBOARD DATA EXTRACTION ***");

            if (!GetInventoryEnabled())
            {
                LogToConsole("[Phase 3] ERROR: Inventory handling is not enabled!");
                LogToConsole("[Phase 3] Skipping Phase 3 and returning to idle...");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
                return;
            }

            var inventories = GetInventories();
            if (inventories == null || inventories.Count <= 1)
            {
                LogToConsole("[Phase 3] No leaderboard inventory found");
                LogToConsole("[Phase 3] Completing Phase 3 and returning to idle...");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
                return;
            }

            LogToConsole($"[Phase 3] Found {inventories.Count} inventories, analyzing for leaderboard data...");

            // Find the leaderboard inventory (not the player inventory)
            Container leaderboardInventory = null;
            int leaderboardInventoryId = -1;

            foreach (var kvp in inventories)
            {
                var container = kvp.Value;
                if (container.Type != ContainerType.PlayerInventory)
                {
                    LogToConsole($"[Phase 3] Checking inventory #{kvp.Key}: Type={container.Type}, Items={container.Items.Count}");

                    // Check if this looks like a leaderboard inventory by examining items
                    if (IsLeaderboardInventory(container))
                    {
                        leaderboardInventory = container;
                        leaderboardInventoryId = kvp.Key;
                        LogToConsole($"[Phase 3] ✅ Using inventory #{kvp.Key} (Type: {container.Type}) for leaderboard data");
                        break;
                    }
                }
            }

            if (leaderboardInventory == null)
            {
                LogToConsole("[Phase 3] No suitable leaderboard inventory found");

                // Fallback: Use any non-player inventory
                foreach (var kvp in inventories)
                {
                    var container = kvp.Value;
                    if (container.Type != ContainerType.PlayerInventory)
                    {
                        LogToConsole($"[Phase 3] ⚠️ FALLBACK: Using inventory #{kvp.Key} as fallback");
                        leaderboardInventory = container;
                        leaderboardInventoryId = kvp.Key;
                        break;
                    }
                }

                if (leaderboardInventory == null)
                {
                    LogToConsole("[Phase 3] No inventories available at all, completing Phase 3...");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                    return;
                }
            }

            LogToConsole($"[Phase 3] ✅ Using inventory #{leaderboardInventoryId} for leaderboard data extraction");
            LogToConsole($"[Phase 3] Inventory type: {leaderboardInventory.Type}, Items: {leaderboardInventory.Items.Count}");

            // Extract JSON data from the leaderboard inventory
            ExtractLeaderboardJsonData(leaderboardInventory, leaderboardInventoryId);
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error in leaderboard data extraction: {ex.Message}");
        }
    }

    private bool IsLeaderboardInventory(Container inventory)
    {
        try
        {
            // Check for characteristic leaderboard inventory items
            foreach (var kvp in inventory.Items)
            {
                Item item = kvp.Value;
                if (item != null && !string.IsNullOrEmpty(item.DisplayName))
                {
                    string displayName = item.DisplayName.ToLower();

                    // Look for leaderboard-specific indicators
                    if (displayName.Contains("leaderboard") ||
                        displayName.Contains("top players") ||
                        displayName.Contains("rankings") ||
                        displayName.Contains("click to close this menu") ||
                        displayName.Contains("main menu"))
                    {
                        LogToConsole($"[Phase 3] Leaderboard inventory detected - found item: '{item.DisplayName}'");
                        return true;
                    }
                }
            }

            return false;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error checking if inventory is leaderboard inventory: {ex.Message}");
            return false;
        }
    }

    private void ExtractLeaderboardJsonData(Container inventory, int inventoryId)
    {
        try
        {
            LogToConsole($"[Phase 3] Extracting structured leaderboard data from inventory #{inventoryId}");
            LogToConsole($"[Phase 3] Inventory Type: {inventory.Type}");
            LogToConsole($"[Phase 3] Inventory Size: {inventory.Items.Count} items");
            LogToConsole($"[Phase 3] Target slots: {string.Join(", ", leaderboardSlots)}");

            // Clear previous leaderboard data
            leaderboardData.Clear();
            leaderboardCategories.Clear();

            // Extract data from specific target slots only
            foreach (int targetSlot in leaderboardSlots)
            {
                if (inventory.Items.ContainsKey(targetSlot))
                {
                    Item item = inventory.Items[targetSlot];
                    if (item != null)
                    {
                        LogToConsole($"[Phase 3] === PROCESSING TARGET SLOT {targetSlot} ===");
                        LogToConsole($"[Phase 3] Item Type: {item.Type}");
                        LogToConsole($"[Phase 3] Display Name: '{item.DisplayName}'");

                        // Extract leaderboard category data
                        var categoryData = ExtractLeaderboardCategory(item, targetSlot);
                        if (categoryData != null)
                        {
                            leaderboardCategories[$"slot_{targetSlot}"] = categoryData;
                            LogToConsole($"[Phase 3] ✅ Extracted {categoryData.CategoryName} leaderboard with {categoryData.Rankings.Count} players");

                            // Log all players for verification (up to 10)
                            for (int i = 0; i < Math.Min(10, categoryData.Rankings.Count); i++)
                            {
                                var player = categoryData.Rankings[i];
                                LogToConsole($"[Phase 3]   #{player.Rank}: {player.PlayerName} ({player.Value}) +{player.Points} points");
                            }
                        }
                        else
                        {
                            LogToConsole($"[Phase 3] ❌ No leaderboard data found in slot {targetSlot}");
                        }

                        LogToConsole($"[Phase 3] === END SLOT {targetSlot} ===");
                    }
                }
                else
                {
                    LogToConsole($"[Phase 3] Target slot {targetSlot} not found in inventory");
                }
            }

            lastLeaderboardExtraction = DateTime.Now;
            LogToConsole($"[Phase 3] Leaderboard data extraction completed");
            LogToConsole($"[Phase 3] Extracted {leaderboardCategories.Count} leaderboard categories");
            // Build category names list manually
            var categoryNames = new List<string>();
            foreach (var category in leaderboardCategories.Values)
            {
                categoryNames.Add(category.CategoryName);
            }
            LogToConsole($"[Phase 3] Categories found: {string.Join(", ", categoryNames.ToArray())}");

        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error extracting leaderboard data: {ex.Message}");
            LogToConsole($"[Phase 3] Stack trace: {ex.StackTrace}");
        }
    }

    private LeaderboardCategory ExtractLeaderboardCategory(Item item, int slot)
    {
        try
        {
            if (item.NBT == null || !item.NBT.ContainsKey("display"))
                return null;

            var displayData = item.NBT["display"] as Dictionary<string, object>;
            if (displayData == null || !displayData.ContainsKey("Lore"))
                return null;

            var loreData = displayData["Lore"];
            if (!(loreData is System.Collections.IEnumerable loreList) || loreData is string)
                return null;

            var category = new LeaderboardCategory
            {
                Slot = slot,
                CategoryName = item.DisplayName,
                Rankings = new List<PlayerRanking>()
            };

            LogToConsole($"[Phase 3] Parsing lore data for category: {category.CategoryName}");

            int loreLineCount = 0;
            int playerCount = 0;

            foreach (var loreItem in loreList)
            {
                string loreLine = loreItem?.ToString() ?? "";
                loreLineCount++;

                if (string.IsNullOrEmpty(loreLine))
                    continue;

                // Parse player ranking from lore line
                var playerRanking = ParsePlayerRanking(loreLine);
                if (playerRanking != null)
                {
                    category.Rankings.Add(playerRanking);
                    playerCount++;
                    LogToConsole($"[Phase 3] DEBUG: Found player #{playerRanking.Rank}: {playerRanking.PlayerName} ({playerRanking.Value})");
                }
            }

            LogToConsole($"[Phase 3] DEBUG: Processed {loreLineCount} lore lines, found {playerCount} players");

            // Sort rankings by rank number
            category.Rankings.Sort((r1, r2) => r1.Rank.CompareTo(r2.Rank));

            return category.Rankings.Count > 0 ? category : null;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error extracting leaderboard category from slot {slot}: {ex.Message}");
            return null;
        }
    }

    private PlayerRanking ParsePlayerRanking(string loreLine)
    {
        try
        {
            // Parse JSON-formatted lore line to extract player ranking
            // Example: {"extra":[{"color":"gray","text":" "},{"color":"#F0C741","text":" #1: "},{"color":"white","text":"minidomo "},{"color":"gray","text":"(1,777,730 mobs) "},{"color":"aqua","text":"+10 points"}],"text":""}

            if (!loreLine.Contains("#") || !loreLine.Contains(":"))
                return null;

            // First extract clean text from JSON
            string cleanText = CleanJsonText(loreLine);
            if (string.IsNullOrEmpty(cleanText))
                cleanText = loreLine; // Fallback to original

            // Extract rank number from clean text
            var rankMatch = System.Text.RegularExpressions.Regex.Match(cleanText, @"#(\d+):");
            if (!rankMatch.Success)
                return null;

            int rank = int.Parse(rankMatch.Groups[1].Value);

            // Extract player name - look for text between rank and parentheses
            string playerName = "";
            var nameMatch = System.Text.RegularExpressions.Regex.Match(cleanText, @"#\d+:\s*([^(]+)\s*\(");
            if (nameMatch.Success)
            {
                playerName = nameMatch.Groups[1].Value.Trim();
            }
            else
            {
                // Fallback: extract from original JSON using patterns
                var namePattern = System.Text.RegularExpressions.Regex.Match(loreLine, @"""color"":""white"",""text"":""([^""]+)\s*""");
                if (namePattern.Success)
                {
                    playerName = namePattern.Groups[1].Value.Trim();
                }
            }

            // Extract value (content in parentheses) from clean text
            var valueMatch = System.Text.RegularExpressions.Regex.Match(cleanText, @"\(([^)]+)\)");
            string value = valueMatch.Success ? valueMatch.Groups[1].Value : "";

            // Extract points from clean text
            var pointsMatch = System.Text.RegularExpressions.Regex.Match(cleanText, @"\+(\d+)\s*points");
            int points = pointsMatch.Success ? int.Parse(pointsMatch.Groups[1].Value) : 0;

            if (!string.IsNullOrEmpty(playerName))
            {
                return new PlayerRanking
                {
                    Rank = rank,
                    PlayerName = CleanControlCharactersFromSourceData(playerName), // CRITICAL FIX: Clean at extraction level
                    Value = CleanControlCharactersFromSourceData(value), // CRITICAL FIX: Clean at extraction level
                    Points = points,
                    RawLoreLine = loreLine
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            LogToConsole($"[Phase 3] Error parsing player ranking from lore: {ex.Message}");
            return null;
        }
    }

    private string ConvertNbtToJsonString(Dictionary<string, object> nbt)
    {
        try
        {
            var sb = new System.Text.StringBuilder();
            sb.AppendLine("{");

            bool first = true;
            foreach (var kvp in nbt)
            {
                if (!first)
                    sb.AppendLine(",");
                first = false;

                sb.Append($"  \"{kvp.Key}\": ");

                if (kvp.Value == null)
                {
                    sb.Append("null");
                }
                else if (kvp.Value is string)
                {
                    sb.Append($"\"{kvp.Value}\"");
                }
                else if (kvp.Value is Dictionary<string, object> dict)
                {
                    sb.Append(ConvertNbtToJsonString(dict).Replace("\n", "\n  "));
                }
                else if (kvp.Value is System.Collections.IEnumerable enumerable && !(kvp.Value is string))
                {
                    sb.Append("[");
                    bool firstItem = true;
                    foreach (var item in enumerable)
                    {
                        if (!firstItem)
                            sb.Append(", ");
                        firstItem = false;

                        if (item is string)
                            sb.Append($"\"{item}\"");
                        else
                            sb.Append(item?.ToString() ?? "null");
                    }
                    sb.Append("]");
                }
                else
                {
                    sb.Append(kvp.Value.ToString());
                }
            }

            sb.AppendLine();
            sb.Append("}");

            return sb.ToString();
        }
        catch (Exception ex)
        {
            return $"{{\"error\": \"Failed to convert NBT to JSON: {ex.Message}\"}}";
        }
    }
}

// === ISLAND DATA STRUCTURE ===
public class IslandData
{
    public string Name { get; set; } = "";
    public string AllTimeValue { get; set; } = "";
    public string WeeklyValue { get; set; } = "";
    public string TodayChange { get; set; } = "";
    public int Slot { get; set; }
    public int Ranking { get; set; }
    public DateTime LastUpdated { get; set; }
    public double WeeklyValueNumeric { get; set; } // For calculations
}

// === HISTORICAL DATA STRUCTURE ===
public class HistoricalIslandData
{
    public DateTime Timestamp { get; set; }
    public string IslandName { get; set; } = "";
    public string AllTimeValue { get; set; } = "";
    public string WeeklyValue { get; set; } = "";
    public string TodayChange { get; set; } = "";
    public int Ranking { get; set; }
    public double WeeklyValueNumeric { get; set; } // For calculations
}

// === LEADERBOARD DATA STRUCTURES ===
public class LeaderboardCategory
{
    public int Slot { get; set; }
    public string CategoryName { get; set; } = "";
    public List<PlayerRanking> Rankings { get; set; } = new List<PlayerRanking>();
    public DateTime ExtractedAt { get; set; } = DateTime.Now;
}

public class PlayerRanking
{
    public int Rank { get; set; }
    public string PlayerName { get; set; } = "";
    public string Value { get; set; } = "";
    public int Points { get; set; }
    public string RawLoreLine { get; set; } = "";
}
