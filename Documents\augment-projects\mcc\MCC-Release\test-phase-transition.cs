//MCCScript 1.0

/* Test script for Phase 1 to Phase 2 transition debugging
 * This script focuses specifically on testing the state transition
 * from successful /istop to Phase 2 inventory detection
 */

MCC.LoadBot(new PhaseTransitionTestBot());

//MCCScript Extensions

public class PhaseTransitionTestBot : ChatBot
{
    // === TEST CONFIGURATION ===
    private const int TASK_INTERVAL_SECONDS = 30; // 30 seconds for testing
    private const string UNKNOWN_COMMAND_RESPONSE = "Unknown command. Type \"/help\" for help.";
    
    // === STATE MANAGEMENT ===
    private enum BotState
    {
        Idle,
        WaitingForIstopResponse,
        WaitingForIstopRetry,
        WaitingForInventoryOpen,
        ExtractingIslandData,
        WaitingForInventoryClose,
        Error
    }
    
    private BotState currentState = BotState.Idle;
    private DateTime nextTaskRun = DateTime.Now.AddSeconds(10);
    private DateTime stateChangeTime = DateTime.Now;
    private bool phase2Enabled = true; // Force Phase 2 enabled for testing
    
    public override void Initialize()
    {
        LogToConsole("=== Phase Transition Test Bot ===");
        LogToConsole("*** TESTING PHASE 1 -> PHASE 2 TRANSITION ***");
        LogToConsole($"Phase 2 Enabled: {phase2Enabled}");
        LogToConsole($"First task scheduled for: {nextTaskRun:HH:mm:ss}");
        LogToConsole("=========================================");
    }
    
    public override void Update()
    {
        DateTime currentTime = DateTime.Now;
        
        switch (currentState)
        {
            case BotState.Idle:
                if (nextTaskRun <= currentTime)
                {
                    LogToConsole($"[TEST] Starting /istop task at {currentTime:HH:mm:ss}");
                    SendIstopCommand();
                }
                break;
                
            case BotState.WaitingForIstopResponse:
                if ((currentTime - stateChangeTime).TotalSeconds > 8)
                {
                    LogToConsole("[TEST] Timeout on /istop, assuming success and checking Phase 2");
                    if (phase2Enabled)
                    {
                        LogToConsole("[TEST] Phase 2 enabled, transitioning to wait for inventory");
                        ChangeState(BotState.WaitingForInventoryOpen);
                    }
                    else
                    {
                        ChangeState(BotState.Idle);
                        ScheduleNextTask();
                    }
                }
                break;
                
            case BotState.WaitingForIstopRetry:
                if ((currentTime - stateChangeTime).TotalSeconds > 8)
                {
                    LogToConsole("[TEST] Timeout on /istop retry, assuming success and checking Phase 2");
                    if (phase2Enabled)
                    {
                        LogToConsole("[TEST] Phase 2 enabled, transitioning to wait for inventory");
                        ChangeState(BotState.WaitingForInventoryOpen);
                    }
                    else
                    {
                        ChangeState(BotState.Idle);
                        ScheduleNextTask();
                    }
                }
                break;
                
            case BotState.WaitingForInventoryOpen:
                if ((currentTime - stateChangeTime).TotalSeconds > 20)
                {
                    LogToConsole("[TEST] Timeout waiting for inventory, skipping Phase 2");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
                
            case BotState.ExtractingIslandData:
                if ((currentTime - stateChangeTime).TotalSeconds > 10)
                {
                    LogToConsole("[TEST] Timeout during data extraction, closing inventory");
                    CloseInventory();
                }
                break;
                
            case BotState.WaitingForInventoryClose:
                if ((currentTime - stateChangeTime).TotalSeconds > 10)
                {
                    LogToConsole("[TEST] Timeout waiting for inventory close, assuming closed");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
        }
    }
    
    private void SendIstopCommand()
    {
        try
        {
            LogToConsole("[TEST] Sending /istop command");
            SendText("/istop");
            ChangeState(BotState.WaitingForIstopResponse);
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error sending /istop: {ex.Message}");
            ChangeState(BotState.Error);
        }
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";
            
            // Log ALL server responses for debugging
            if (!IsChatMessage(cleanText, ref message, ref username) && 
                !IsPrivateMessage(cleanText, ref message, ref username))
            {
                LogToConsole($"[TEST] [State: {currentState}] Server: {cleanText}");
                HandleServerResponse(cleanText);
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error processing text: {ex.Message}");
        }
    }
    
    private void HandleServerResponse(string response)
    {
        // Check for inventory messages in ANY state
        if (response.Contains("Inventory"))
        {
            LogToConsole($"[TEST] *** INVENTORY MESSAGE DETECTED *** in state {currentState}");
            LogToConsole($"[TEST] Full message: '{response}'");
            
            if (response.Contains("opened"))
            {
                LogToConsole("[TEST] *** INVENTORY OPENED DETECTED ***");
                if (currentState == BotState.WaitingForInventoryOpen)
                {
                    LogToConsole("[TEST] Perfect! We were waiting for this. Starting data extraction...");
                    ChangeState(BotState.ExtractingIslandData);
                    ExtractIslandData();
                }
                else
                {
                    LogToConsole($"[TEST] WARNING: Inventory opened but we're in state {currentState}!");
                    LogToConsole("[TEST] Force transitioning to data extraction...");
                    ChangeState(BotState.ExtractingIslandData);
                    ExtractIslandData();
                }
            }
            else if (response.Contains("content"))
            {
                LogToConsole("[TEST] *** INVENTORY CONTENT DETECTED ***");
                LogToConsole("[TEST] This is where we would parse island data");
                CloseInventory();
            }
            else if (response.Contains("closed"))
            {
                LogToConsole("[TEST] *** INVENTORY CLOSED DETECTED ***");
                LogToConsole("[TEST] Phase 2 complete, returning to idle");
                ChangeState(BotState.Idle);
                ScheduleNextTask();
            }
        }
        
        // Handle /istop responses
        if (currentState == BotState.WaitingForIstopResponse || currentState == BotState.WaitingForIstopRetry)
        {
            if (response.Contains(UNKNOWN_COMMAND_RESPONSE))
            {
                LogToConsole("[TEST] /istop not recognized, trying /skyblock");
                SendText("/skyblock");
                ChangeState(BotState.WaitingForIstopRetry);
            }
            else if (response.Contains("istop") || response.Contains("stop"))
            {
                LogToConsole("[TEST] *** /istop SUCCESSFUL ***");
                LogToConsole($"[TEST] Phase 2 enabled: {phase2Enabled}");
                
                if (phase2Enabled)
                {
                    LogToConsole("[TEST] *** TRANSITIONING TO PHASE 2 ***");
                    ChangeState(BotState.WaitingForInventoryOpen);
                }
                else
                {
                    LogToConsole("[TEST] Phase 2 disabled, returning to idle");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
            }
        }
    }
    
    private void ExtractIslandData()
    {
        try
        {
            LogToConsole("[TEST] *** REQUESTING INVENTORY DATA ***");
            SendText("/inventory 1 list");
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error requesting inventory: {ex.Message}");
            CloseInventory();
        }
    }
    
    private void CloseInventory()
    {
        try
        {
            LogToConsole("[TEST] *** CLOSING INVENTORY ***");
            SendText("/inventory 1 close");
            ChangeState(BotState.WaitingForInventoryClose);
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error closing inventory: {ex.Message}");
            ChangeState(BotState.Idle);
            ScheduleNextTask();
        }
    }
    
    private void ChangeState(BotState newState)
    {
        if (currentState != newState)
        {
            LogToConsole($"[TEST] *** STATE CHANGE: {currentState} -> {newState} ***");
            currentState = newState;
            stateChangeTime = DateTime.Now;
        }
    }
    
    private void ScheduleNextTask()
    {
        nextTaskRun = DateTime.Now.AddSeconds(TASK_INTERVAL_SECONDS);
        LogToConsole($"[TEST] Next task: {nextTaskRun:HH:mm:ss}");
    }
}
