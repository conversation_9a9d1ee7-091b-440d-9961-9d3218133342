#!/usr/bin/env python3
"""
Quick Test for Discord Bridge Bot
Tests basic functionality without full setup
"""

import json
import sys
from pathlib import Path

def test_python_setup():
    """Test if Python and required modules are available"""
    print("🧪 Testing Python Setup...")
    
    # Test Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Test required modules
    required_modules = ['aiohttp', 'aiofiles', 'watchdog']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} available")
        except ImportError:
            print(f"❌ {module} missing")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n📦 Install missing modules:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def test_configuration():
    """Test if configuration file is valid"""
    print("\n🔧 Testing Configuration...")
    
    config_file = Path("discord-bridge-config.json")
    if not config_file.exists():
        print("❌ Configuration file not found")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Check required fields
        required_fields = [
            ("discord", "bot_token"),
            ("discord", "channels", "istop"),
            ("discord", "channels", "lb"),
            ("monitoring", "mcc_log_file")
        ]
        
        for field_path in required_fields:
            current = config
            for field in field_path:
                if field not in current:
                    print(f"❌ Missing config field: {'.'.join(field_path)}")
                    return False
                current = current[field]
        
        print("✅ Configuration file valid")
        print(f"✅ Bot token: {config['discord']['bot_token'][:20]}...")
        print(f"✅ ISTOP channel: {config['discord']['channels']['istop']}")
        print(f"✅ LB channel: {config['discord']['channels']['lb']}")
        print(f"✅ Log file: {config['monitoring']['mcc_log_file']}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in config file: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def test_log_file():
    """Test if MCC log file exists and contains Discord content"""
    print("\n📄 Testing Log File...")
    
    log_file = Path("inventory2.txt")
    if not log_file.exists():
        print("❌ MCC log file not found")
        print("💡 Run MCC multi-phase bot first to generate log entries")
        return False
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for Discord content patterns
        discord_patterns = [
            "[Phase 4] Channel:",
            "[Phase 4] Content:",
            "embeds"
        ]
        
        found_patterns = []
        for pattern in discord_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        print(f"✅ Log file exists ({len(content)} characters)")
        print(f"✅ Found {len(found_patterns)}/{len(discord_patterns)} Discord patterns")
        
        if found_patterns:
            print("✅ Log file contains Discord embed content")
            return True
        else:
            print("⚠️ No Discord content found in log file")
            print("💡 Run MCC multi-phase bot to generate Discord embeds")
            return False
            
    except Exception as e:
        print(f"❌ Error reading log file: {e}")
        return False

def test_discord_api_format():
    """Test if we can parse Discord embed format from log"""
    print("\n🔍 Testing Discord Embed Parsing...")
    
    # Sample Discord embed content from MCC log
    sample_content = '''[MCC] [MultiPhaseBot] [Phase 4] Channel: 1401451296711507999
[MCC] [MultiPhaseBot] [Phase 4] Content: {"embeds":[{"title":"✅ Test Message","description":"Test embed","color":65280}]}'''
    
    try:
        # Extract channel
        import re
        channel_match = re.search(r'\[Phase 4\] Channel: (\d+)', sample_content)
        if channel_match:
            channel_id = channel_match.group(1)
            print(f"✅ Channel extraction: {channel_id}")
        else:
            print("❌ Channel extraction failed")
            return False
        
        # Extract embed content
        content_match = re.search(r'\[Phase 4\] Content: (.+)', sample_content)
        if content_match:
            embed_json = content_match.group(1)
            embed_data = json.loads(embed_json)
            print(f"✅ Embed parsing: {embed_data['embeds'][0]['title']}")
        else:
            print("❌ Embed content extraction failed")
            return False
        
        print("✅ Discord embed parsing working")
        return True
        
    except Exception as e:
        print(f"❌ Embed parsing error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Discord Bridge Bot - Quick Test")
    print("=" * 50)
    
    tests = [
        ("Python Setup", test_python_setup),
        ("Configuration", test_configuration),
        ("Log File", test_log_file),
        ("Embed Parsing", test_discord_api_format)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🚀 All tests passed! Discord bridge bot is ready to run.")
        print("💡 Start with: python discord-bridge-bot.py")
    else:
        print("\n⚠️ Some tests failed. Please fix issues before running bridge bot.")
        
        if not results[0][1]:  # Python setup failed
            print("\n📥 Next steps:")
            print("1. Install Python 3.8+ from https://python.org")
            print("2. Install dependencies: pip install -r requirements.txt")
            print("3. Run this test again: python quick-test-bridge.py")

if __name__ == "__main__":
    main()
