//MCCScript 1.0

/* Test script for Multi-Phase Bot
 * This script demonstrates how to test the multi-phase bot with shorter intervals
 * for testing purposes. Change TASK_INTERVAL_SECONDS to 30 for quick testing.
 */

MCC.LoadBot(new TestMultiPhaseBot());

//MCCScript Extensions

public class TestMultiPhaseBot : ChatBot
{
    // === TEST CONFIGURATION (shorter intervals for testing) ===
    private const int TASK_INTERVAL_SECONDS = 30; // 30 seconds for testing (change to 600 for production)
    private const int SKYBLOCK_LOAD_WAIT_TICKS = 20; // 2 seconds for testing
    private const string UNKNOWN_COMMAND_RESPONSE = "Unknown command. Type \"/help\" for help.";
    
    // === STATE MANAGEMENT ===
    private enum BotState
    {
        Idle,
        WaitingForIstopResponse,
        ProcessingSkyblockLoad,
        WaitingForIstopRetry,
        Error
    }
    
    private BotState currentState = BotState.Idle;
    private DateTime nextTaskRun = DateTime.Now.AddSeconds(5); // Start after 5 seconds
    private DateTime stateChangeTime = DateTime.Now;
    private int tickCounter = 0;
    private int skyblockLoadWaitTicks = 0;
    private int retryAttempts = 0;
    private const int MAX_RETRY_ATTEMPTS = 2; // Reduced for testing
    
    public override void Initialize()
    {
        LogToConsole("=== TEST Multi-Phase Bot Initialized ===");
        LogToConsole("*** THIS IS A TEST VERSION WITH SHORT INTERVALS ***");
        LogToConsole($"Task Interval: {TASK_INTERVAL_SECONDS} seconds (TEST MODE)");
        LogToConsole($"Skyblock Load Wait: {SKYBLOCK_LOAD_WAIT_TICKS} ticks (TEST MODE)");
        LogToConsole($"First task scheduled for: {nextTaskRun:HH:mm:ss}");
        LogToConsole("==========================================");
    }
    
    public override void Update()
    {
        tickCounter++;
        DateTime currentTime = DateTime.Now;
        
        switch (currentState)
        {
            case BotState.Idle:
                if (nextTaskRun <= currentTime)
                {
                    LogToConsole($"[TEST] Starting task at {currentTime:HH:mm:ss}");
                    SendIstopCommand();
                }
                break;
                
            case BotState.WaitingForIstopResponse:
                if ((currentTime - stateChangeTime).TotalSeconds > 5) // Shorter timeout for testing
                {
                    LogToConsole("[TEST] Timeout waiting for /istop response");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
                
            case BotState.ProcessingSkyblockLoad:
                skyblockLoadWaitTicks++;
                if (skyblockLoadWaitTicks >= SKYBLOCK_LOAD_WAIT_TICKS)
                {
                    LogToConsole($"[TEST] Skyblock wait complete, retrying /istop");
                    skyblockLoadWaitTicks = 0;
                    SendIstopCommand();
                    ChangeState(BotState.WaitingForIstopRetry);
                }
                break;
                
            case BotState.WaitingForIstopRetry:
                if ((currentTime - stateChangeTime).TotalSeconds > 5)
                {
                    LogToConsole("[TEST] Timeout on retry, assuming success");
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
                
            case BotState.Error:
                if ((currentTime - stateChangeTime).TotalSeconds > 30) // Shorter error recovery
                {
                    LogToConsole("[TEST] Recovering from error state");
                    retryAttempts = 0;
                    ChangeState(BotState.Idle);
                    ScheduleNextTask();
                }
                break;
        }
    }
    
    private void SendIstopCommand()
    {
        try
        {
            LogToConsole("[TEST] Sending /istop command");
            SendText("/istop");
            ChangeState(BotState.WaitingForIstopResponse);
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error sending /istop: {ex.Message}");
            ChangeState(BotState.Error);
        }
    }
    
    public override void GetText(string text)
    {
        try
        {
            string cleanText = GetVerbatim(text);
            string message = "";
            string username = "";

            // Log all server responses for testing
            if (!IsChatMessage(cleanText, ref message, ref username) &&
                !IsPrivateMessage(cleanText, ref message, ref username))
            {
                LogToConsole($"[TEST] Server response: {cleanText}");

                if (currentState == BotState.WaitingForIstopResponse ||
                    currentState == BotState.WaitingForIstopRetry)
                {
                    if (cleanText.Contains(UNKNOWN_COMMAND_RESPONSE))
                    {
                        LogToConsole("[TEST] /istop not recognized, trying /skyblock");
                        SendText("/skyblock");
                        ChangeState(BotState.ProcessingSkyblockLoad);
                    }
                    else if (cleanText.Contains("istop") || cleanText.Contains("stop"))
                    {
                        LogToConsole("[TEST] /istop command successful!");
                        ChangeState(BotState.Idle);
                        ScheduleNextTask();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            LogToConsole($"[TEST] Error processing text: {ex.Message}");
        }
    }
    
    private void ChangeState(BotState newState)
    {
        if (currentState != newState)
        {
            LogToConsole($"[TEST] State: {currentState} -> {newState}");
            currentState = newState;
            stateChangeTime = DateTime.Now;
        }
    }
    
    private void ScheduleNextTask()
    {
        nextTaskRun = DateTime.Now.AddSeconds(TASK_INTERVAL_SECONDS);
        retryAttempts = 0;
        LogToConsole($"[TEST] Next task: {nextTaskRun:HH:mm:ss}");
    }
}
