#!/usr/bin/env python3
"""
Analyze the specific problematic message a69a7ebe
"""

import json
from datetime import datetime

def analyze_specific_message():
    """Analyze the problematic message a69a7ebe in detail"""
    
    target_id = "ab872802"
    
    print("🔍 ANALYZING PROBLEMATIC MESSAGE ab872802")
    print("=" * 60)
    
    try:
        with open("discord_queue.json", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        target_message = None
        for line in lines:
            if line.strip():
                try:
                    message = json.loads(line.strip())
                    if message.get('id', '').startswith(target_id):
                        target_message = message
                        break
                except json.JSONDecodeError:
                    continue
        
        if not target_message:
            print(f"❌ Message {target_id} not found in queue")
            return
        
        print(f"✅ Found message: {target_message['id']}")
        print(f"📅 Timestamp: {target_message.get('timestamp', 'N/A')}")
        print(f"📅 Created at: {target_message.get('created_at', 'N/A')}")
        print(f"📊 Message type: {target_message.get('message_type', 'N/A')}")
        print(f"📺 Channel: {target_message.get('channel_name', 'N/A')}")
        
        # Timing analysis
        fixes_time = datetime(2025, 8, 4, 0, 46, 0)
        created_at = target_message.get('created_at', '')
        if created_at:
            msg_time = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
            time_diff = (msg_time - fixes_time).total_seconds()
            
            print(f"🕐 Message created: {msg_time}")
            print(f"🕐 Fixes implemented: {fixes_time}")
            print(f"⏱️ Time difference: {time_diff:.1f} seconds")
            
            if time_diff > 0:
                print(f"🚨 CRITICAL: Message created {time_diff:.1f} seconds AFTER fixes!")
                print(f"   This means enhanced cleaning should have been applied!")
            else:
                print(f"ℹ️ Message created {abs(time_diff):.1f} seconds BEFORE fixes")
        
        # Analyze embed_data_raw
        embed_data_raw = target_message.get('embed_data_raw', '')
        if embed_data_raw:
            print(f"\n📊 EMBED_DATA_RAW ANALYSIS:")
            print(f"   Length: {len(embed_data_raw)} characters")
            
            # Find control characters
            control_chars = []
            for i, char in enumerate(embed_data_raw):
                if ord(char) < 32 and char not in ['\n', '\r', '\t']:
                    control_chars.append({
                        'position': i,
                        'char': repr(char),
                        'ord': ord(char),
                        'hex': f"0x{ord(char):02X}"
                    })
            
            if control_chars:
                print(f"   ❌ FOUND {len(control_chars)} CONTROL CHARACTERS:")
                for ctrl in control_chars:
                    print(f"      Position {ctrl['position']}: {ctrl['char']} (ord: {ctrl['ord']}, hex: {ctrl['hex']})")
                
                # Check position 91-92 specifically
                if len(embed_data_raw) > 90:
                    char_91 = embed_data_raw[90]
                    print(f"   📍 Character at position 91: {repr(char_91)} (ord: {ord(char_91)}, hex: 0x{ord(char_91):02X})")
                    if ord(char_91) < 32 and char_91 not in ['\n', '\r', '\t']:
                        print(f"      🚨 POSITION 91 HAS CONTROL CHARACTER!")
                
                if len(embed_data_raw) > 91:
                    char_92 = embed_data_raw[91]
                    print(f"   📍 Character at position 92: {repr(char_92)} (ord: {ord(char_92)}, hex: 0x{ord(char_92):02X})")
                    if ord(char_92) < 32 and char_92 not in ['\n', '\r', '\t']:
                        print(f"      🚨 POSITION 92 HAS CONTROL CHARACTER!")
                
                # Show context around position 91
                if len(embed_data_raw) > 85:
                    start = max(0, 85)
                    end = min(len(embed_data_raw), 100)
                    context = embed_data_raw[start:end]
                    
                    print(f"\n   📍 CONTEXT AROUND POSITION 91 ({start}-{end}):")
                    print(f"      Text: {repr(context)}")
                    
                    # Hex dump
                    hex_bytes = ' '.join(f"{ord(c):02X}" for c in context)
                    print(f"      Hex:  {hex_bytes}")
                    
                    # ASCII representation
                    ascii_chars = ''.join(c if 32 <= ord(c) <= 126 else '.' for c in context)
                    print(f"      ASCII: {ascii_chars}")
            else:
                print(f"   ✅ No control characters found")
            
            # Try to parse the JSON to see what fails
            print(f"\n🧪 JSON PARSING TEST:")
            try:
                parsed = json.loads(embed_data_raw)
                print(f"   ✅ JSON parsing successful")
                
                # Look for player names in the parsed data
                embeds = parsed.get('embeds', [])
                if embeds:
                    fields = embeds[0].get('fields', [])
                    for field in fields:
                        value = field.get('value', '')
                        if 'points' in value:  # This looks like player data
                            print(f"   📊 Player data field: {value[:100]}...")
                            
                            # Check for control characters in player names
                            for i, char in enumerate(value):
                                if ord(char) < 32 and char not in ['\n', '\r', '\t']:
                                    print(f"      ❌ Control character in player data at pos {i}: {repr(char)}")
                
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON parsing failed: {e}")
                print(f"      Error at position: {getattr(e, 'pos', 'unknown')}")
        
        else:
            print(f"\n📊 No embed_data_raw field found")
        
        # Check if this is a leaderboard message
        if target_message.get('message_type') == 'leaderboard':
            print(f"\n🏆 LEADERBOARD MESSAGE ANALYSIS:")
            print(f"   This message should have gone through CreateLeaderboardEmbed()")
            print(f"   Player names should have been cleaned by RemoveMinecraftColorCodes()")
            print(f"   If control characters are present, the enhanced cleaning failed!")
        
        print(f"\n🔧 DEBUGGING RECOMMENDATIONS:")
        if time_diff > 0 and control_chars:
            print(f"   1. Check MCC bot logs for 'ENHANCED DEBUG' messages")
            print(f"   2. Verify RemoveMinecraftColorCodes() was called for player names")
            print(f"   3. Check if CleanControlCharactersFromSourceData() is working")
            print(f"   4. Look for raw player data before cleaning")
        elif time_diff <= 0:
            print(f"   1. This is an old message from before fixes - expected to have issues")
            print(f"   2. Focus on newer messages created after 00:46:00")
        
    except Exception as e:
        print(f"❌ Error analyzing message: {e}")

if __name__ == "__main__":
    analyze_specific_message()
