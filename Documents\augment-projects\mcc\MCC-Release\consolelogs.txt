Minecraft Console Client v1.20.4 - for MC 1.4.6 to 1.20.4 - Github.com/MCCTeam
Resolving hub.mc-complex.com...
Cached session is still valid for Lowyn.
Using Minecraft version 1.20.4 (protocol v765)
Cached profile key is still valid for Lowyn.
[MCC] Version is supported.
Logging in...
[MCC] Checking Session...
[MCC] Server was successfully joined.
Type '/quit' to leave the server.
[MCC] [Script] Starting compilation for .\multi-phase-bot.cs...
▌Hub 7 [Member] Jjinhoo: t
[MCC] [Script] Compilation done with no errors.
[MCC] [MultiPhaseBot] === MULTI-PHASE BOT INITIALIZATION ===
[MCC] [MultiPhaseBot] Multi-Phase Bot initialized successfully
[MCC] [MultiPhaseBot] Phase 1 enabled: True
[MCC] [MultiPhaseBot] Phase 2 enabled: True
[MCC] [MultiPhaseBot] Phase 3 enabled: True
[MCC] [MultiPhaseBot] Phase 4 enabled: True
[MCC] [MultiPhaseBot] [Test] Testing monetary formatting:
[MCC] [MultiPhaseBot] [Test] 3680000 -> +$3.7M
[MCC] [MultiPhaseBot] [Test] -2100000 -> -$2.1M
[MCC] [MultiPhaseBot] [Test] 1500000000 -> +$1.5B
[MCC] [MultiPhaseBot] [Test] 250000 -> +$250.0K
[MCC] [MultiPhaseBot] [Test] 500 -> +$500
[MCC] [MultiPhaseBot] [Test] -750 -> -$750
[MCC] [MultiPhaseBot] [Phase 2] ✅ Loaded 411 historical data entries from disk
[MCC] [MultiPhaseBot] [Phase 2] Historical data range: 2025-07-25 00:46 to 2025-08-04 19:35
[MCC] [MultiPhaseBot] [Phase 2] Tracking 11 unique islands in historical data
[MCC] [MultiPhaseBot] [Phase 4] Initializing Discord integration...
[MCC] [MultiPhaseBot] [Phase 4] Bot Token: MTQwMTQ0OTUwMzYwOTA2...
[MCC] [MultiPhaseBot] [Phase 4] ISTOP Channel: 1401451296711507999
[MCC] [MultiPhaseBot] [Phase 4] LB Channel: 1401451313216094383
[MCC] [MultiPhaseBot] [Phase 4] 📋 MCC Discord Integration Analysis:
[MCC] [MultiPhaseBot] [Phase 4] ⚠️ MCC ChatBot scripts run in a sandboxed environment
[MCC] [MultiPhaseBot] [Phase 4] ⚠️ Direct HTTP requests (System.Net.WebRequest) not available
[MCC] [MultiPhaseBot] [Phase 4] ⚠️ Process execution (System.Diagnostics.Process) restricted
[MCC] [MultiPhaseBot] [Phase 4] ✅ This is a security feature to prevent malicious scripts
[MCC] [MultiPhaseBot] [Phase 4]
[MCC] [MultiPhaseBot] [Phase 4] 🔧 Solution: External Discord Bridge Bot
[MCC] [MultiPhaseBot] [Phase 4] ✅ MCC bot generates perfect embeds in simulation mode
[MCC] [MultiPhaseBot] [Phase 4] ✅ External Python bot monitors logs and sends to Discord
[MCC] [MultiPhaseBot] [Phase 4] ✅ This provides full Discord integration outside MCC sandbox
[MCC] [MultiPhaseBot] [Phase 4]
[MCC] [MultiPhaseBot] [Phase 4] 🚀 New File-Based Discord Integration:
[MCC] [MultiPhaseBot] [Phase 4] 1. MCC bot writes Discord embeds to dedicated files
[MCC] [MultiPhaseBot] [Phase 4] 2. External bridge bot monitors these files
[MCC] [MultiPhaseBot] [Phase 4] 3. Bridge bot sends embeds to Discord automatically
[MCC] [MultiPhaseBot] [Phase 4] 4. No manual log file manipulation required
[MCC] [MultiPhaseBot] [Phase 4]
[MCC] [MultiPhaseBot] [Phase 4] Discord output files initialized:
[MCC] [MultiPhaseBot] [Phase 4] - Main file: discord_embeds.json
[MCC] [MultiPhaseBot] [Phase 4] - Queue file: discord_queue.json
[MCC] [MultiPhaseBot] [Phase 4] Discord integration: File-based mode (structured data output)
[MCC] [MultiPhaseBot] [Phase 4] Testing Discord file integration...
[MCC] [MultiPhaseBot] [Phase 4] ✅ Discord connection test successful
[MCC] [MultiPhaseBot] [Phase 4] Discord integration initialized successfully
[MCC] [MultiPhaseBot] [Scheduling] Initializing 10-minute automatic scheduling...
[MCC] [MultiPhaseBot] [Scheduling] Cycle interval: 600 seconds (10 minutes)
[MCC] [MultiPhaseBot] [Scheduling] ✅ 10-minute scheduling enabled
[MCC] [MultiPhaseBot] [Scheduling] 🔄 Bot will execute full cycle every 10 minutes
[MCC] [MultiPhaseBot] [Scheduling] ⏰ First cycle will start in 10 seconds
[MCC] [MultiPhaseBot] [Scheduling] 📋 Each cycle triggers: Phase 1 → Phase 2 → Phase 3 → Phase 4
[MCC] [MultiPhaseBot] Bot is now running in Idle state
[MCC] [MultiPhaseBot] === INITIALIZATION COMPLETE ===
[MCC] [Script] Script continues to run.
[MCC] [MultiPhaseBot] [Phase 1] Starting periodic task at 13:45:12
[MCC] [MultiPhaseBot] [Phase 1] Sending /istop command
[MCC] [MultiPhaseBot] Sending '/istop'
[MCC] [MultiPhaseBot] [State] Idle -> WaitingForIstopResponse
▌Unknown command. Type "/help" for help.
[MCC] [MultiPhaseBot] [Phase 1] /istop command not recognized, trying /skyblock
[MCC] [MultiPhaseBot] Sending '/skyblock'
[MCC] [MultiPhaseBot] [State] WaitingForIstopResponse -> ProcessingSkyblockLoad
▌✔ Sending you to Skyblock...
[MCC] [MultiPhaseBot] [Phase 1] SkyBlock load detected, waiting for completion
▌! Don't forget to vote to be eligible to receive rewards from our vote party.
▌[Horror] [Noble] BATBL00D: offering 500k for a lead
▌
▌ COMPLEX SKYBLOCK 1.21.4
▌   ▪ Website: mc-complex.com
▌   ▪ Store: store.mc-complex.com
▌   ▪ Discord: discord.gg/ComplexVanilla
▌   ▪ Rules: bit.ly/ComplexVanillaRules
▌
▌[Voter] [Pharaoh] brizzler*: do /shop string
[MCC] [MultiPhaseBot] [Phase 1] Skyblock load wait complete (40 ticks), retrying /istop
[MCC] [MultiPhaseBot] [Phase 1] Sending /istop command
[MCC] [MultiPhaseBot] Sending '/istop'
[MCC] [MultiPhaseBot] [State] ProcessingSkyblockLoad -> WaitingForIstopResponse
[MCC] [MultiPhaseBot] [State] WaitingForIstopResponse -> WaitingForIstopRetry
[MCC] Inventory # 1 opened:
[MCC] Use /inventory to interact with it.
▌(/dailies) ⭐ You have 1 Daily Reward available!
▌(/verify)  Verify your Discord to receive a free reward!
▌(/bidwar) ✔ The Bid War for  Gold Shard  (x64) has ended with no bids.
▌[Member] VexingRacer5: ...i didnt get a single sapling
▌[Member] 0Derpy: what can you use gc for
▌RUNES ✔ [Scribe] B0tWally's Green Thumb VIII was applied to: ������ Pool Party Hoe ������! (100% chance)
▌[#2] [Jolteon ������] [Pharaoh➕] seabarrel007: i will pay it back in a day
[MCC] [MultiPhaseBot] 🔄 === SCHEDULED 10-MINUTE CYCLE #1 START at 13:45:22 ===
[MCC] [MultiPhaseBot] [Cleanup] 🧹 Cleaning up inventories before starting new cycle...
[MCC] [MultiPhaseBot] [Cleanup] Closing inventory #1 (Type: Generic_9x6, Items: 55)
[MCC] [MultiPhaseBot] [Cleanup] ✅ Successfully closed inventory #1
[MCC] [MultiPhaseBot] [Cleanup] Inventory cleanup completed. Closed 1 inventories.
[MCC] [MultiPhaseBot] [Cycle 1] Triggering new multi-phase task cycle
[MCC] [MultiPhaseBot] [Cycle 1] Resetting bot state to Idle to start fresh cycle
[MCC] [MultiPhaseBot] [State] WaitingForIstopRetry -> Idle
[MCC] [MultiPhaseBot] [Cycle 1] Scheduled cycle will execute through normal state machine
[MCC] [MultiPhaseBot] [Cycle 1] Bot will automatically execute Phase 1 → Phase 2 → Phase 3 → Phase 4
[MCC] [MultiPhaseBot] [Cycle 1] State machine will handle cycle completion when all phases finish
[MCC] [MultiPhaseBot] [Phase 1] Starting periodic task at 13:45:22
[MCC] [MultiPhaseBot] [Phase 1] Sending /istop command
[MCC] [MultiPhaseBot] Sending '/istop'
[MCC] [MultiPhaseBot] [State] Idle -> WaitingForIstopResponse
[MCC] Inventory # 2 opened:
[MCC] Use /inventory to interact with it.
▌(/bidwar) ✔ A Bid War for $ Sell Wand $ is starting at $1,000!
▌ [Pharaoh➕] DommeDaggoe killed [Pharaoh➕] Hyfr* with [������ Grinch Sword ������]! 
▌[#2] [ᴡᴏʀᴅʟᴇ] [Pharaoh➕] Hyfr*: die
[MCC] [MultiPhaseBot] [Phase 1] Timeout waiting for /istop response
[MCC] [MultiPhaseBot] [Phase 2] Assuming /istop worked, transitioning to wait for inventory...
[MCC] [MultiPhaseBot] [State] WaitingForIstopResponse -> WaitingForInventoryOpen
[MCC] [MultiPhaseBot] [Phase 2] Inventory opened with 2 containers
[MCC] [MultiPhaseBot] [State] WaitingForInventoryOpen -> ExtractingIslandData
[MCC] [MultiPhaseBot] [Phase 2] Starting island data extraction...
[MCC] [MultiPhaseBot] [Phase 2] *** STARTING ISLAND DATA EXTRACTION ***
[MCC] [MultiPhaseBot] [Phase 2] Found 2 inventories, analyzing for island data...
[MCC] [MultiPhaseBot] [Phase 2] 🔍 VALIDATING INVENTORY TYPE...
[MCC] [MultiPhaseBot] [Phase 2] Checking inventory #2: Type=Generic_9x6, Items=55
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Click to close this menu....
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Click to close this menu....
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Click to close this menu.' -> 'Click to close this menu.'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'click to close this menu.'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Top 10 Islands:...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Top 10 Islands:...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Top 10 Islands:' -> 'Top 10 Islands:'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'top 10 islands:'
[MCC] [MultiPhaseBot] [Phase 2] ✅ ISLAND INDICATOR FOUND: 'top 10 islands:'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Complex Gaming...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Complex Gaming' -> 'Complex Gaming'
[MCC] [MultiPhaseBot] [Phase 2] Sample item: 'complex gaming'
[MCC] [MultiPhaseBot] [Phase 2] Validation results: 0 leaderboard indicators, 1 island indicators
[MCC] [MultiPhaseBot] [Phase 2] ✅ INVENTORY VALIDATION PASSED: This appears to be island data
[MCC] [MultiPhaseBot] [Phase 2] Found 2 inventories, analyzing for island data...
[MCC] [MultiPhaseBot] [Phase 2] Checking inventory #2: Type=Generic_9x6, Items=55
[MCC] [MultiPhaseBot] [Phase 2] Island inventory detected - found item: 'Top 10 Islands:'
[MCC] [MultiPhaseBot] [Phase 2] ✅ Using inventory #2 (Type: Generic_9x6) for island data
[MCC] [MultiPhaseBot] [Phase 2] ✅ Using inventory #2 for island data extraction
[MCC] [MultiPhaseBot] [Phase 2] Inventory type: Generic_9x6, Items: 55
[MCC] [MultiPhaseBot] [Phase 2] Extracting island data from inventory #2
[MCC] [MultiPhaseBot] [Phase 2] Processing slot 13: #1: Revenants (10 points)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: #1: Revenants (10 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: #1: Revenants (10 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: '#1: Revenants (10 points)' -> '#1: Revenants (10 points)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processing lore data for slot 13
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"#8EC3CF","underlined":false,"t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"#8EC3CF","...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 1: '{"extra":[{"color":"#8ec3cf","underlined":false,"text":"value earned:","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 2: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#f0c741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"today's change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$453.65m ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+27.8%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#F0C741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$453.65M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+27.8%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#F0C741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$453.65M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+27.8%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '+$453.65M (+27.8%)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Today: +$453.65M (+27.8%)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 3: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#f0c741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"this week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$2.09b","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#F0C741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$2.09B","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#F0C741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$2.09B","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$2.09B'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Week (alt): $2.09B
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 4: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#f0c741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"all time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$2.89b","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#F0C741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$2.89B","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#F0C741","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$2.89B","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$2.89B'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found All Time: $2.89B
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ""...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ""...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 5: '""'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 6: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"→","italic":false},{"text":" ","italic":false},{"color":"green","underlined":true,"text":"click","italic":false},{"color":"#8ec3cf","underlined":false,"text":" to ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","underlined":true,"text":"view","italic":false},{"color":"#8ec3cf","underlined":false,"text":" this island's warps!","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processed 6 lore lines for slot 13
[MCC] [MultiPhaseBot] [Phase 2] Extracted island #1: #1: Revenants (10 points)
[MCC] [MultiPhaseBot] [Phase 2]   All Time: $2.89B
[MCC] [MultiPhaseBot] [Phase 2]   Weekly: $2.09B
[MCC] [MultiPhaseBot] [Phase 2]   Today: +$453.65M (+27.8%)
[MCC] [MultiPhaseBot] [Phase 2] Processing slot 21: #2: ������NIP������ (9 points)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: #2: ������NIP������ (9 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: #2: ������NIP������ (9 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: '#2: ������NIP������ (9 points)' -> '#2: NIP (9 points)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processing lore data for slot 21
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"#8EC3CF","underlined":false,"t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"#8EC3CF","...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 1: '{"extra":[{"color":"#8ec3cf","underlined":false,"text":"value earned:","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 2: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#dedede","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"today's change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$51.7m ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+5.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#DEDEDE","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$51.7M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+5.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#DEDEDE","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$51.7M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+5.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '+$51.7M (+5.4%)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Today: +$51.7M (+5.4%)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 3: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#dedede","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"this week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$1b","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#DEDEDE","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$1B","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#DEDEDE","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$1B","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$1B'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Week (alt): $1B
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 4: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#dedede","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"all time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$1.53b","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#DEDEDE","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$1.53B","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#DEDEDE","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$1.53B","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$1.53B'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found All Time: $1.53B
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ""...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ""...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 5: '""'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 6: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"→","italic":false},{"text":" ","italic":false},{"color":"green","underlined":true,"text":"click","italic":false},{"color":"#8ec3cf","underlined":false,"text":" to ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","underlined":true,"text":"view","italic":false},{"color":"#8ec3cf","underlined":false,"text":" this island's warps!","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processed 6 lore lines for slot 21
[MCC] [MultiPhaseBot] [Phase 2] Extracted island #2: #2: NIP (9 points)
[MCC] [MultiPhaseBot] [Phase 2]   All Time: $1.53B
[MCC] [MultiPhaseBot] [Phase 2]   Weekly: $1B
[MCC] [MultiPhaseBot] [Phase 2]   Today: +$51.7M (+5.4%)
[MCC] [MultiPhaseBot] [Phase 2] Processing slot 23: #3: MineControl (8 points)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: #3: MineControl (8 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: #3: MineControl (8 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: '#3: MineControl (8 points)' -> '#3: MineControl (8 points)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processing lore data for slot 23
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"#8EC3CF","underlined":false,"t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"#8EC3CF","...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 1: '{"extra":[{"color":"#8ec3cf","underlined":false,"text":"value earned:","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 2: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#e39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"today's change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$55.4m ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+21.1%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#E39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$55.4M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+21.1%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#E39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$55.4M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+21.1%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '+$55.4M (+21.1%)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Today: +$55.4M (+21.1%)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 3: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#e39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"this week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$317.37m","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#E39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$317.37M","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#E39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$317.37M","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$317.37M'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Week (alt): $317.37M
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 4: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#e39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"all time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$611.26m","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#E39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$611.26M","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#E39856","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$611.26M","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$611.26M'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found All Time: $611.26M
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ""...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ""...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 5: '""'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 6: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"→","italic":false},{"text":" ","italic":false},{"color":"green","underlined":true,"text":"click","italic":false},{"color":"#8ec3cf","underlined":false,"text":" to ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","underlined":true,"text":"view","italic":false},{"color":"#8ec3cf","underlined":false,"text":" this island's warps!","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processed 6 lore lines for slot 23
[MCC] [MultiPhaseBot] [Phase 2] Extracted island #3: #3: MineControl (8 points)
[MCC] [MultiPhaseBot] [Phase 2]   All Time: $611.26M
[MCC] [MultiPhaseBot] [Phase 2]   Weekly: $317.37M
[MCC] [MultiPhaseBot] [Phase 2]   Today: +$55.4M (+21.1%)
[MCC] [MultiPhaseBot] [Phase 2] Processing slot 29: #4: LARP❒ (6 points)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: #4: LARP❒ (6 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: #4: LARP❒ (6 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: '#4: LARP❒ (6 points)' -> '#4: LARP❒ (6 points)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processing lore data for slot 29
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"#8EC3CF","underlined":false,"t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"#8EC3CF","...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 1: '{"extra":[{"color":"#8ec3cf","underlined":false,"text":"value earned:","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 2: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#c4de5b","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"today's change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$59.67m ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+34.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#C4DE5B","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$59.67M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+34.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#C4DE5B","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$59.67M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+34.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '+$59.67M (+34.4%)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Today: +$59.67M (+34.4%)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 3: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#c4de5b","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"this week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$232.87m","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#C4DE5B","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$232.87M","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#C4DE5B","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$232.87M","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$232.87M'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Week (alt): $232.87M
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 4: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#c4de5b","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"all time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$247.2m","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#C4DE5B","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$247.2M","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#C4DE5B","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$247.2M","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$247.2M'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found All Time: $247.2M
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ""...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ""...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 5: '""'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 6: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"→","italic":false},{"text":" ","italic":false},{"color":"green","underlined":true,"text":"click","italic":false},{"color":"#8ec3cf","underlined":false,"text":" to ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","underlined":true,"text":"view","italic":false},{"color":"#8ec3cf","underlined":false,"text":" this island's warps!","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processed 6 lore lines for slot 29
[MCC] [MultiPhaseBot] [Phase 2] Extracted island #4: #4: LARP❒ (6 points)
[MCC] [MultiPhaseBot] [Phase 2]   All Time: $247.2M
[MCC] [MultiPhaseBot] [Phase 2]   Weekly: $232.87M
[MCC] [MultiPhaseBot] [Phase 2]   Today: +$59.67M (+34.4%)
[MCC] [MultiPhaseBot] [Phase 2] Processing slot 31: #5: MostLoved (3 points)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: #5: MostLoved (3 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: #5: MostLoved (3 points)...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: '#5: MostLoved (3 points)' -> '#5: MostLoved (3 points)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processing lore data for slot 31
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"#8EC3CF","underlined":false,"t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"#8EC3CF","...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 1: '{"extra":[{"color":"#8ec3cf","underlined":false,"text":"value earned:","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 2: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7cc3d9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"today's change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$24.86m ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+20.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7CC3D9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$24.86M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+20.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7CC3D9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"Today's Change: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"+$24.86M ","italic":false},{"color":"dark_gray","text":"(","italic":false},{"color":"green","text":"+20.4%","italic":false},{"color":"dark_gray","text":")","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '+$24.86M (+20.4%)'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Today: +$24.86M (+20.4%)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 3: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7cc3d9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"this week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$146.86m","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7CC3D9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$146.86M","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7CC3D9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"This Week: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$146.86M","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$146.86M'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found Week (alt): $146.86M
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 4: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7cc3d9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8ec3cf","underlined":false,"text":"all time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$146.86m","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: ExtractValueFromLore input: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7CC3D9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$146.86M","italic":false}],"text":""}' -> clean: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#7CC3D9","underlined":false,"text":"◆ ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"#8EC3CF","underlined":false,"text":"All Time: ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"white","text":"$146.86M","italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Extracted from JSON: '$146.86M'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Found All Time: $146.86M
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ""...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ""...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 5: '""'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: {"extra":[{"color":"gray","underlined":false,"text...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: {"extra":[{"color":"gray","und...
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Lore line 6: '{"extra":[{"color":"gray","underlined":false,"text":" ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","text":"→","italic":false},{"text":" ","italic":false},{"color":"green","underlined":true,"text":"click","italic":false},{"color":"#8ec3cf","underlined":false,"text":" to ","bold":false,"strikethrough":false,"obfuscated":false,"italic":false},{"color":"green","underlined":true,"text":"view","italic":false},{"color":"#8ec3cf","underlined":false,"text":" this island's warps!","bold":false,"strikethrough":false,"obfuscated":false,"italic":false}],"text":""}'
[MCC] [MultiPhaseBot] [Phase 2] DEBUG: Processed 6 lore lines for slot 31
[MCC] [MultiPhaseBot] [Phase 2] Extracted island #5: #5: MostLoved (3 points)
[MCC] [MultiPhaseBot] [Phase 2]   All Time: $146.86M
[MCC] [MultiPhaseBot] [Phase 2]   Weekly: $146.86M
[MCC] [MultiPhaseBot] [Phase 2]   Today: +$24.86M (+20.4%)
[MCC] [MultiPhaseBot] [Phase 2] Comparing with previous data...
[MCC] [MultiPhaseBot] [Phase 2] No previous data available for comparison
[MCC] [MultiPhaseBot] [Phase 2] Added historical data entry. Total entries: 416
[MCC] [MultiPhaseBot] [Phase 2] Historical data saved to disk (416 entries)
[MCC] [MultiPhaseBot] [Phase 2] Saved 5 islands as previous data
[MCC] [MultiPhaseBot] [Phase 2] Island data extraction completed - extracted 5 islands
[MCC] [MultiPhaseBot] [Phase 2] Island data extraction completed
[MCC] [MultiPhaseBot] [Phase 2] Sending real island data embed (5 islands)
[MCC] [MultiPhaseBot] [Phase 2] Growth calculation for #1: Revenants (10 points): 76 historical entries
[MCC] [MultiPhaseBot] [Phase 2] Current weekly value: $2090000000
[MCC] [MultiPhaseBot] [Phase 2] Last snapshot: $1450000000 -> Change: $640000000 -> Formatted: +$640.0M
[MCC] [MultiPhaseBot] [Phase 2] 1h ago: $1450000000 -> Change: $640000000 -> Formatted: +$640.0M
[MCC] [MultiPhaseBot] [Phase 2] 24h ago: $1440000000 -> Change: $650000000 -> Formatted: +$650.0M
[MCC] [MultiPhaseBot] [Phase 2] 3d ago: $1280000000 -> Change: $810000000 -> Formatted: +$810.0M
[MCC] [MultiPhaseBot] [Phase 2] Final growth metrics: Last: +$640.0M • 1h: +$640.0M • 24h: +$650.0M • 3d: +$810.0M
[MCC] [MultiPhaseBot] [Phase 2] Growth calculation for #2: NIP (9 points): 76 historical entries
[MCC] [MultiPhaseBot] [Phase 2] Current weekly value: $1000000000
[MCC] [MultiPhaseBot] [Phase 2] Last snapshot: $866490000 -> Change: $133510000 -> Formatted: +$133.5M
[MCC] [MultiPhaseBot] [Phase 2] 1h ago: $866490000 -> Change: $133510000 -> Formatted: +$133.5M
[MCC] [MultiPhaseBot] [Phase 2] 24h ago: $862000000 -> Change: $138000000 -> Formatted: +$138.0M
[MCC] [MultiPhaseBot] [Phase 2] 3d ago: $795030000 -> Change: $204970000 -> Formatted: +$205.0M
[MCC] [MultiPhaseBot] [Phase 2] Final growth metrics: Last: +$133.5M • 1h: +$133.5M • 24h: +$138.0M • 3d: +$205.0M
[MCC] [MultiPhaseBot] [Phase 2] Growth calculation for #3: MineControl (8 points): 76 historical entries
[MCC] [MultiPhaseBot] [Phase 2] Current weekly value: $317370000
[MCC] [MultiPhaseBot] [Phase 2] Last snapshot: $241020000 -> Change: $76350000 -> Formatted: +$76.3M
[MCC] [MultiPhaseBot] [Phase 2] 1h ago: $241020000 -> Change: $76350000 -> Formatted: +$76.3M
[MCC] [MultiPhaseBot] [Phase 2] 24h ago: $239640000 -> Change: $77730000 -> Formatted: +$77.7M
[MCC] [MultiPhaseBot] [Phase 2] 3d ago: $218450000 -> Change: $98920000 -> Formatted: +$98.9M
[MCC] [MultiPhaseBot] [Phase 2] Final growth metrics: Last: +$76.3M • 1h: +$76.3M • 24h: +$77.7M • 3d: +$98.9M
[MCC] [MultiPhaseBot] [Phase 2] Growth calculation for #4: LARP❒ (6 points): 19 historical entries
[MCC] [MultiPhaseBot] [Phase 2] Current weekly value: $232870000
[MCC] [MultiPhaseBot] [Phase 2] Last snapshot: $145030000 -> Change: $87840000 -> Formatted: +$87.8M
[MCC] [MultiPhaseBot] [Phase 2] 1h ago: $145030000 -> Change: $87840000 -> Formatted: +$87.8M
[MCC] [MultiPhaseBot] [Phase 2] 24h ago: $143560000 -> Change: $89310000 -> Formatted: +$89.3M
[MCC] [MultiPhaseBot] [Phase 2] 3d ago: $121300000 -> Change: $111570000 -> Formatted: +$111.6M
[MCC] [MultiPhaseBot] [Phase 2] Final growth metrics: Last: +$87.8M • 1h: +$87.8M • 24h: +$89.3M • 3d: +$111.6M
[MCC] [MultiPhaseBot] [Phase 2] Growth calculation for #5: MostLoved (3 points): 71 historical entries
[MCC] [MultiPhaseBot] [Phase 2] Current weekly value: $146860000
[MCC] [MultiPhaseBot] [Phase 2] Last snapshot: $113430000 -> Change: $33430000 -> Formatted: +$33.4M
[MCC] [MultiPhaseBot] [Phase 2] 1h ago: $113430000 -> Change: $33430000 -> Formatted: +$33.4M
[MCC] [MultiPhaseBot] [Phase 2] 24h ago: $112940000 -> Change: $33920000 -> Formatted: +$33.9M
[MCC] [MultiPhaseBot] [Phase 2] 3d ago: $105520000 -> Change: $41340000 -> Formatted: +$41.3M
[MCC] [MultiPhaseBot] [Phase 2] Final growth metrics: Last: +$33.4M • 1h: +$33.4M • 24h: +$33.9M • 3d: +$41.3M
[MCC] [MultiPhaseBot] [Phase 4] Updating persistent island message (ID: persistent-island-message-id)
[MCC] [MultiPhaseBot] [Phase 4] Processing island embed JSON (length: 1216 chars)
[MCC] [MultiPhaseBot] [Phase 4] Converting island message data to JSON for message ID: persiste...
[MCC] [MultiPhaseBot] [Phase 4] Final island JSON length: 1659 characters
[MCC] [MultiPhaseBot] [Phase 4] Island embed written to queue: UPDATE - Message ID: persistent-island-message-id
[MCC] [MultiPhaseBot] [Phase 4] ✅ Island embed written to file successfully
[MCC] [MultiPhaseBot] [Phase 3] Phase 3 enabled, transitioning to Phase 3...
[MCC] [MultiPhaseBot] [State] ExtractingIslandData -> ClosingIslandInventory
[MCC] [MultiPhaseBot] [Phase 3] Closing island inventory and preparing for leaderboard command...
[MCC] [MultiPhaseBot] [Phase 3] Closing inventory #2 using MCC API CloseInventory() method
[MCC] [MultiPhaseBot] [Phase 3] Successfully closed inventory #2
[MCC] [MultiPhaseBot] [Phase 3] Inventory closure completed, transitioning to leaderboard command...
[MCC] [MultiPhaseBot] [State] ClosingIslandInventory -> WaitingForLeaderboardCommand
▌[#2] [Pharaoh➕] DommeDaggoe: DIE
▌[#2] [ᴡᴏʀᴅʟᴇ] [Pharaoh➕] Hyfr*: 
[MCC] [MultiPhaseBot] [Phase 3] Sending /lb command to open leaderboard...
[MCC] [MultiPhaseBot] Sending '/lb'
[MCC] [MultiPhaseBot] [Phase 3] Sent /lb command, waiting for leaderboard to open
[MCC] [MultiPhaseBot] [State] WaitingForLeaderboardCommand -> WaitingForLeaderboardOpen
[MCC] Inventory # 3 opened:
[MCC] Use /inventory to interact with it.
[MCC] [MultiPhaseBot] [Phase 3] Leaderboard inventory opened with 2 containers
[MCC] [MultiPhaseBot] [State] WaitingForLeaderboardOpen -> ExtractingLeaderboardData
▌(/cf) ◆ [Member] Moonly03 made a ↑ Coinflip ↓ for: $10K
[MCC] [MultiPhaseBot] [Phase 3] Starting leaderboard data extraction...
[MCC] [MultiPhaseBot] [Phase 3] *** STARTING LEADERBOARD DATA EXTRACTION ***
[MCC] [MultiPhaseBot] [Phase 3] Found 2 inventories, analyzing for leaderboard data...
[MCC] [MultiPhaseBot] [Phase 3] Checking inventory #3: Type=Generic_9x6, Items=43
[MCC] [MultiPhaseBot] [Phase 3] Leaderboard inventory detected - found item: 'Main Menu'
[MCC] [MultiPhaseBot] [Phase 3] ✅ Using inventory #3 (Type: Generic_9x6) for leaderboard data
[MCC] [MultiPhaseBot] [Phase 3] ✅ Using inventory #3 for leaderboard data extraction
[MCC] [MultiPhaseBot] [Phase 3] Inventory type: Generic_9x6, Items: 43
[MCC] [MultiPhaseBot] [Phase 3] Extracting structured leaderboard data from inventory #3
[MCC] [MultiPhaseBot] [Phase 3] Inventory Type: Generic_9x6
[MCC] [MultiPhaseBot] [Phase 3] Inventory Size: 43 items
[MCC] [MultiPhaseBot] [Phase 3] Target slots: 4, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 4 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Paper
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Top 12 Players:'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Top 12 Players:
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: MostlyMissing 89 points (50 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Jaeger1000 76 points (40 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Nincompoopz 65 points (35 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: LegendVFX 46 points (30 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: KeysFish 44 points (25 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: m0mmyymilk3rs 42 points (20 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: THCPineapple 41 points (15 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Chaneru 35 points (10 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Gerritperrit 34 points (10 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: minidomo 27 points (5 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #11: caractus 25 points (5 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #12: xSisqoo 25 points (5 GC)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 21 lore lines, found 12 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Top 12 Players: leaderboard with 12 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: MostlyMissing 89 points (50 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Jaeger1000 76 points (40 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Nincompoopz 65 points (35 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: LegendVFX 46 points (30 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: KeysFish 44 points (25 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: m0mmyymilk3rs 42 points (20 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: THCPineapple 41 points (15 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Chaneru 35 points (10 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Gerritperrit 34 points (10 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: minidomo 27 points (5 GC) +0 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 4 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 9 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: ZombieHead
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Mobs Killed'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Mobs Killed
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: minidomo (397,226 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: DaSimsGamer (69,917 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Amusin (52,169 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: KeysFish (38,039 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Rhazzel (34,409 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: xamid (32,794 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: Geen (32,045 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: MostlyMissing (32,012 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Nincompoopz (30,116 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: NL_Peanut (25,810 mobs)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Mobs Killed leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: minidomo (397,226 mobs) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: DaSimsGamer (69,917 mobs) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Amusin (52,169 mobs) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: KeysFish (38,039 mobs) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Rhazzel (34,409 mobs) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: xamid (32,794 mobs) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: Geen (32,045 mobs) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: MostlyMissing (32,012 mobs) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Nincompoopz (30,116 mobs) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: NL_Peanut (25,810 mobs) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 9 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 11 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Paper
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Scrolls Completed ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Scrolls Completed ◆ DOUBLE POINTS ◆
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Jaxair (15 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: TheTwoInchPinch (11 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Nincompoopz (11 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Farrnado (11 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Roleeb (9 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: Andtarski (8 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: mattep83 (7 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: YYtheYY (7 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: WektorTR (5 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Rucking (5 scrolls)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Scrolls Completed ◆ DOUBLE POINTS ◆ leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Jaxair (15 scrolls) +20 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: TheTwoInchPinch (11 scrolls) +18 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Nincompoopz (11 scrolls) +16 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Farrnado (11 scrolls) +14 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Roleeb (9 scrolls) +12 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: Andtarski (8 scrolls) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: mattep83 (7 scrolls) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: YYtheYY (7 scrolls) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: WektorTR (5 scrolls) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Rucking (5 scrolls) +2 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 11 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 13 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Paper
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Runes Identified'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Runes Identified
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: LegendVFX (3,388 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Gerritperrit (2,933 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: caractus (2,753 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Jaeger1000 (2,473 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Roexoe (1,738 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: MR_Trigger (1,635 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: IZekt (1,315 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: LogicalPlays (1,264 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Cataclysm69 (895 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: maxappleimac1 (889 runes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Runes Identified leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: LegendVFX (3,388 runes) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Gerritperrit (2,933 runes) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: caractus (2,753 runes) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Jaeger1000 (2,473 runes) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Roexoe (1,738 runes) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: MR_Trigger (1,635 runes) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: IZekt (1,315 runes) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: LogicalPlays (1,264 runes) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Cataclysm69 (895 runes) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: maxappleimac1 (889 runes) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 13 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 15 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: GrassBlock
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Blocks Placed'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Blocks Placed
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: vs1 (55,349 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Rarote (29,972 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: KnownRandom (27,530 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Rossin_1023 (26,416 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Blaze_pvp1 (25,805 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: .IDontRageYT0 (23,643 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: Pore69 (21,894 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Kllua_ (21,702 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: zambino1909 (19,007 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: TheOnlyLegalMex (18,952 blocks)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Blocks Placed leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: vs1 (55,349 blocks) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Rarote (29,972 blocks) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: KnownRandom (27,530 blocks) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Rossin_1023 (26,416 blocks) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Blaze_pvp1 (25,805 blocks) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: .IDontRageYT0 (23,643 blocks) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: Pore69 (21,894 blocks) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Kllua_ (21,702 blocks) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: zambino1909 (19,007 blocks) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: TheOnlyLegalMex (18,952 blocks) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 15 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 17 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Carrot
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Crops Harvested'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Crops Harvested
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Jaeger1000 (2,208,032 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Chaneru (1,003,653 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Silverdogs33 (915,491 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Ahhhyuu (815,677 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: KeysFish (756,877 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: The__Name_ (691,773 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: m0mmyymilk3rs (688,636 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Nincompoopz (539,696 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: GyroGyro (532,949 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Bivestew_ (479,620 crops)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Crops Harvested leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Jaeger1000 (2,208,032 crops) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Chaneru (1,003,653 crops) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Silverdogs33 (915,491 crops) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Ahhhyuu (815,677 crops) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: KeysFish (756,877 crops) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: The__Name_ (691,773 crops) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: m0mmyymilk3rs (688,636 crops) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Nincompoopz (539,696 crops) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: GyroGyro (532,949 crops) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Bivestew_ (479,620 crops) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 17 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 19 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: DiamondOre
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Ores Mined'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Ores Mined
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Gerritperrit (2,207,727 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: LegendVFX (1,958,006 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: caractus (1,727,959 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Jaeger1000 (1,637,077 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Flamming_Sword (1,096,047 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: LogicalPlays (911,209 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: VoidLovesMoney (829,321 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: IZekt (804,498 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: old_nanna (795,944 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Cataclysm69 (661,994 ores)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Ores Mined leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Gerritperrit (2,207,727 ores) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: LegendVFX (1,958,006 ores) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: caractus (1,727,959 ores) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Jaeger1000 (1,637,077 ores) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Flamming_Sword (1,096,047 ores) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: LogicalPlays (911,209 ores) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: VoidLovesMoney (829,321 ores) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: IZekt (804,498 ores) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: old_nanna (795,944 ores) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Cataclysm69 (661,994 ores) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 19 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 21 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: EnderChest
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Pouches Opened'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Pouches Opened
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: MostlyMissing (2,417 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: m0mmyymilk3rs (1,744 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Chaneru (1,664 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: KeysFish (1,663 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: BaconBaconBacon_ (1,652 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: Roexoe (1,626 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: Bozo_Block (1,606 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: THCPineapple (1,548 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: EnchantedHigh5 (1,218 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: FlactioON (1,064 pouches)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Pouches Opened leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: MostlyMissing (2,417 pouches) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: m0mmyymilk3rs (1,744 pouches) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Chaneru (1,664 pouches) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: KeysFish (1,663 pouches) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: BaconBaconBacon_ (1,652 pouches) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: Roexoe (1,626 pouches) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: Bozo_Block (1,606 pouches) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: THCPineapple (1,548 pouches) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: EnchantedHigh5 (1,218 pouches) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: FlactioON (1,064 pouches) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 21 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 23 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Paper
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Votes'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Votes
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Nincompoopz (34 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: _Hyfr (34 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: minidomo (33 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Kat903 (32 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: x_Fluffy_Bunny_x (31 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: BlueFusion_ (30 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: DraGunnXFire (29 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: DaSimsGamer (28 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: ruen03 (28 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Gerritperrit (25 votes)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 19 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Votes leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Nincompoopz (34 votes) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: _Hyfr (34 votes) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: minidomo (33 votes) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Kat903 (32 votes) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: x_Fluffy_Bunny_x (31 votes) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: BlueFusion_ (30 votes) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: DraGunnXFire (29 votes) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: DaSimsGamer (28 votes) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: ruen03 (28 votes) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Gerritperrit (25 votes) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 23 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 25 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: TropicalFishBucket
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Fish Caught'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Fish Caught
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: FantaManBam (27,625 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: WillieTheMan (17,374 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: KR33PY (15,740 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: EnchantedHigh5 (15,529 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Gerritperrit (11,177 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: Klaxonn1903 (10,702 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: Soopraiise (10,462 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: AlitaBoss99 (9,985 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: C0OB (9,788 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: ExtWaffleBossAL (9,744 fish)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Fish Caught leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: FantaManBam (27,625 fish) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: WillieTheMan (17,374 fish) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: KR33PY (15,740 fish) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: EnchantedHigh5 (15,529 fish) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Gerritperrit (11,177 fish) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: Klaxonn1903 (10,702 fish) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: Soopraiise (10,462 fish) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: AlitaBoss99 (9,985 fish) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: C0OB (9,788 fish) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: ExtWaffleBossAL (9,744 fish) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 25 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 27 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Paper
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Envoys Claimed'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Envoys Claimed
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: _Syvix (164 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Jaeger1000 (126 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: MostlyMissing (86 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: m0mmyymilk3rs (46 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: LegendVFX (40 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: THCPineapple (39 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: duhismoke312 (37 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: _IamDepressed_ (29 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: VoidLovesMoney (26 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Ketchup47 (26 envoys)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Envoys Claimed leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: _Syvix (164 envoys) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Jaeger1000 (126 envoys) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: MostlyMissing (86 envoys) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: m0mmyymilk3rs (46 envoys) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: LegendVFX (40 envoys) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: THCPineapple (39 envoys) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: duhismoke312 (37 envoys) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: _IamDepressed_ (29 envoys) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: VoidLovesMoney (26 envoys) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Ketchup47 (26 envoys) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 27 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 29 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Book
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Quests Completed'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Quests Completed
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: DaSimsGamer (11 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: xamid (11 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Farrnado (7 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Geen (6 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Nincompoopz (6 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: SleepiestAgent (2 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: agrajagagrajag (2 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: TallOmnivore129 (2 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: .Pulsebudzv2 (2 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Sleepo_ (2 quests)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Quests Completed leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: DaSimsGamer (11 quests) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: xamid (11 quests) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Farrnado (7 quests) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Geen (6 quests) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Nincompoopz (6 quests) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: SleepiestAgent (2 quests) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: agrajagagrajag (2 quests) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: TallOmnivore129 (2 quests) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: .Pulsebudzv2 (2 quests) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Sleepo_ (2 quests) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 29 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 31 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: ExperienceBottle
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Experience Gained'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Experience Gained
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Jaeger1000 (37,857,615 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: caractus (16,349,052 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Gerritperrit (14,655,771 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Cataclysm69 (13,437,308 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: LegendVFX (12,225,707 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: LogicalPlays (10,026,353 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: TheSerezShow (8,355,729 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: chrisss069 (5,660,376 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: old_nanna (5,547,436 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Jimbob123007 (4,973,055 experience)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Experience Gained leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Jaeger1000 (37,857,615 experience) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: caractus (16,349,052 experience) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Gerritperrit (14,655,771 experience) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Cataclysm69 (13,437,308 experience) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: LegendVFX (12,225,707 experience) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: LogicalPlays (10,026,353 experience) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: TheSerezShow (8,355,729 experience) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: chrisss069 (5,660,376 experience) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: old_nanna (5,547,436 experience) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Jimbob123007 (4,973,055 experience) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 31 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 33 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Paper
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Shop Items Sold'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Shop Items Sold
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Jaeger1000 ($2,765,420,376)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: EnchantedHigh5 ($1,275,100,808)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: LegendVFX ($1,256,508,536)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Nincompoopz ($1,256,483,901)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: SnoCozart ($1,133,642,642)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: KeysFish ($970,778,332)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: m0mmyymilk3rs ($886,700,648)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Silverdogs33 ($774,610,306)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Chaneru ($699,487,404)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: samuello ($669,338,374)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Shop Items Sold leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Jaeger1000 ($2,765,420,376) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: EnchantedHigh5 ($1,275,100,808) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: LegendVFX ($1,256,508,536) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Nincompoopz ($1,256,483,901) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: SnoCozart ($1,133,642,642) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: KeysFish ($970,778,332) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: m0mmyymilk3rs ($886,700,648) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Silverdogs33 ($774,610,306) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Chaneru ($699,487,404) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: samuello ($669,338,374) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 33 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 35 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: NetheriteSword
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'PvP Kills'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: PvP Kills
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: _Syvix (35 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Chaneru (17 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: xSisqoFanBoy2 (13 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Jaeger1000 (12 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: U0w0o (12 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: Cerv (12 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: MineFarge (11 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Akid_43 (10 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Amusin (10 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: wzne (9 kills)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 18 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted PvP Kills leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: _Syvix (35 kills) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Chaneru (17 kills) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: xSisqoFanBoy2 (13 kills) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Jaeger1000 (12 kills) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: U0w0o (12 kills) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: Cerv (12 kills) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: MineFarge (11 kills) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Akid_43 (10 kills) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Amusin (10 kills) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: wzne (9 kills) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 35 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 37 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Clock
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Time Played'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Time Played
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Nincompoopz (1d 11h)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: litsa_kavlitsa69 (1d 3h)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: WLDFakeZz (1d 2h)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Chaneru (1d 1h)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: MrPyrza (1d 1h)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: TheTwoInchPinch (1d 1h)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: SarcasticReaper (23h 53m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: SurfSounds (22h 45m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: MostlyMissing (22h 15m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Farrnado (21h 43m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Time Played leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Nincompoopz (1d 11h) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: litsa_kavlitsa69 (1d 3h) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: WLDFakeZz (1d 2h) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Chaneru (1d 1h) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: MrPyrza (1d 1h) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: TheTwoInchPinch (1d 1h) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: SarcasticReaper (23h 53m) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: SurfSounds (22h 45m) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: MostlyMissing (22h 15m) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Farrnado (21h 43m) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 37 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 39 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: SkeletonSkull
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Time in Pharaoh's Tomb'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Time in Pharaoh's Tomb
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Nincompoopz (17h 33m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: roughjoke95 (12h 7m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: MostlyMissing (9h 44m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Hgcrafted (9h 35m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: .beastieman420 (9h 7m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: THCPineapple (7h 55m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: .Happymatt8663 (7h 52m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: jonnyjamm (7h 31m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: .brayden16t (7h 29m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: kurkland (7h 6m)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Time in Pharaoh's Tomb leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Nincompoopz (17h 33m) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: roughjoke95 (12h 7m) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: MostlyMissing (9h 44m) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Hgcrafted (9h 35m) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: .beastieman420 (9h 7m) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: THCPineapple (7h 55m) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: .Happymatt8663 (7h 52m) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: jonnyjamm (7h 31m) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: .brayden16t (7h 29m) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: kurkland (7h 6m) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 39 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 41 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: CreeperHead
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Pyramid Mobs Killed'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Pyramid Mobs Killed
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: KeysFish (12,377)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: MostlyMissing (9,048)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: .beastieman420 (6,746)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: roughjoke95 (5,798)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Tazah_ (5,447)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: THCPineapple (4,773)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: BluePantherYT (4,572)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Ambrith (4,196)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: MusseBaby (3,780)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: DaSimsGamer (3,612)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Pyramid Mobs Killed leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: KeysFish (12,377) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: MostlyMissing (9,048) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: .beastieman420 (6,746) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: roughjoke95 (5,798) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Tazah_ (5,447) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: THCPineapple (4,773) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: BluePantherYT (4,572) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Ambrith (4,196) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: MusseBaby (3,780) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: DaSimsGamer (3,612) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 41 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 43 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: GoldNugget
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Gold Shards Collected'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Gold Shards Collected
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Soopraiise (8,502)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: Roexoe (5,963)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: ZeroVoids (5,262)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: maxappleimac1 (5,153)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Mr_Gooberville (3,083)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: Tazah_ (3,074)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: MostlyMissing (2,967)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: BluePantherYT (2,254)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Christian359 (2,213)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: THCPineapple (2,149)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Gold Shards Collected leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Soopraiise (8,502) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: Roexoe (5,963) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: ZeroVoids (5,262) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: maxappleimac1 (5,153) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Mr_Gooberville (3,083) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: Tazah_ (3,074) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: MostlyMissing (2,967) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: BluePantherYT (2,254) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Christian359 (2,213) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: THCPineapple (2,149) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 43 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 45 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: RedShulkerBox
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Treasure Chests Opened ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Treasure Chests Opened ◆ DOUBLE POINTS ◆
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: MostlyMissing (6)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: m0mmyymilk3rs (3)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Yuri2l (2)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: lorenab81 (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: PsykkoAU (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: THCPineapple (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: Jaeger1000 (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Jimbob123007 (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: NoWillToLive1 (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Mr_T0Jl (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Treasure Chests Opened ◆ DOUBLE POINTS ◆ leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: MostlyMissing (6) +20 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: m0mmyymilk3rs (3) +18 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Yuri2l (2) +16 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: lorenab81 (1) +14 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: PsykkoAU (1) +12 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: THCPineapple (1) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: Jaeger1000 (1) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Jimbob123007 (1) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: NoWillToLive1 (1) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Mr_T0Jl (1) +2 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 45 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 47 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: LeatherBoots
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Blocks Traveled On Foot'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Blocks Traveled On Foot
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Trener1s (292,821)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: KeysFish (290,931)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Jaeger1000 (271,504)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: LegendVFX (239,406)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Huge4LitreMilk (231,888)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: xSisqoo (212,461)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: _Syvix (176,762)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: _ColdYeti (172,660)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: MostlyMissing (172,469)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Scryia (169,823)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Blocks Traveled On Foot leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Trener1s (292,821) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: KeysFish (290,931) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Jaeger1000 (271,504) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: LegendVFX (239,406) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Huge4LitreMilk (231,888) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: xSisqoo (212,461) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: _Syvix (176,762) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: _ColdYeti (172,660) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: MostlyMissing (172,469) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Scryia (169,823) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 47 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 49 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: Bow
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Minigames Won'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Minigames Won
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: pilot12345 (11)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: KnownRandom (9)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Syfexi (6)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Egestino (4)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: Wh0_Wh0 (2)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: Kingly_gaoler3 (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: kircaaa (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: Odzin__ (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Amusin (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: Nincompoopz (1)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 15 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Minigames Won leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: pilot12345 (11) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: KnownRandom (9) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Syfexi (6) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Egestino (4) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: Wh0_Wh0 (2) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: Kingly_gaoler3 (1) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: kircaaa (1) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: Odzin__ (1) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Amusin (1) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: Nincompoopz (1) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 49 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 51 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: JungleLog
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Logs Chopped'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Logs Chopped
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: Soopraiise (42,077)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: minidomo (19,676)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: old_nanna (14,180)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: maxappleimac1 (12,365)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: kenzie194 (12,263)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: MostlyMissing (11,531)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: xamid (11,324)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: FlactioON (10,334)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: seabarrel007 (9,533)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: JpGoneCrazy (6,848)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 19 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Logs Chopped leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: Soopraiise (42,077) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: minidomo (19,676) +9 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: old_nanna (14,180) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: maxappleimac1 (12,365) +7 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: kenzie194 (12,263) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: MostlyMissing (11,531) +5 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: xamid (11,324) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: FlactioON (10,334) +3 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: seabarrel007 (9,533) +2 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: JpGoneCrazy (6,848) +1 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 51 ===
[MCC] [MultiPhaseBot] [Phase 3] === PROCESSING TARGET SLOT 53 ===
[MCC] [MultiPhaseBot] [Phase 3] Item Type: ZombieHead
[MCC] [MultiPhaseBot] [Phase 3] Display Name: 'Bosses Killed ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [Phase 3] Parsing lore data for category: Bosses Killed ◆ DOUBLE POINTS ◆
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #1: xSisqoo (116)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #2: MostlyMissing (106)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #3: Lil_Bouncy21 (103)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #4: Ketchup47 (93)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #5: THCPineapple (51)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #6: ruined_123 (49)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #7: Scryia (43)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #8: ZeroVoids (38)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #9: Sup3rSweZy (34)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Found player #10: wzne (33)
[MCC] [MultiPhaseBot] [Phase 3] DEBUG: Processed 18 lore lines, found 10 players
[MCC] [MultiPhaseBot] [Phase 3] ✅ Extracted Bosses Killed ◆ DOUBLE POINTS ◆ leaderboard with 10 players
[MCC] [MultiPhaseBot] [Phase 3]   #1: xSisqoo (116) +20 points
[MCC] [MultiPhaseBot] [Phase 3]   #2: MostlyMissing (106) +18 points
[MCC] [MultiPhaseBot] [Phase 3]   #3: Lil_Bouncy21 (103) +16 points
[MCC] [MultiPhaseBot] [Phase 3]   #4: Ketchup47 (93) +14 points
[MCC] [MultiPhaseBot] [Phase 3]   #5: THCPineapple (51) +12 points
[MCC] [MultiPhaseBot] [Phase 3]   #6: ruined_123 (49) +10 points
[MCC] [MultiPhaseBot] [Phase 3]   #7: Scryia (43) +8 points
[MCC] [MultiPhaseBot] [Phase 3]   #8: ZeroVoids (38) +6 points
[MCC] [MultiPhaseBot] [Phase 3]   #9: Sup3rSweZy (34) +4 points
[MCC] [MultiPhaseBot] [Phase 3]   #10: wzne (33) +2 points
[MCC] [MultiPhaseBot] [Phase 3] === END SLOT 53 ===
[MCC] [MultiPhaseBot] [Phase 3] Leaderboard data extraction completed
[MCC] [MultiPhaseBot] [Phase 3] Extracted 24 leaderboard categories
[MCC] [MultiPhaseBot] [Phase 3] Categories found: Top 12 Players:, Mobs Killed, Scrolls Completed ◆ DOUBLE POINTS ◆, Runes Identified, Blocks Placed, Crops Harvested, Ores Mined, Pouches Opened, Votes, Fish Caught, Envoys Claimed, Quests Completed, Experience Gained, Shop Items Sold, PvP Kills, Time Played, Time in Pharaoh's Tomb, Pyramid Mobs Killed, Gold Shards Collected, Treasure Chests Opened ◆ DOUBLE POINTS ◆, Blocks Traveled On Foot, Minigames Won, Logs Chopped, Bosses Killed ◆ DOUBLE POINTS ◆
[MCC] [MultiPhaseBot] [Phase 3] Leaderboard data extraction completed
[MCC] [MultiPhaseBot] [Phase 3] Sending real leaderboard data embed (24 categories)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] CreateLeaderboardEmbed called at 13:45:37.611
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Available categories: 24
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Using category: 'Top 12 Players:' with 12 players
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 1: Raw name='MostlyMissing 89 points', Raw value='50 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing 89 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing 89 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 50 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 50 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 1 cleaned: Name='MostlyMissing 89 points', Value='50 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 2: Raw name='Jaeger1000 76 points', Raw value='40 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000 76 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000 76 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 40 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 40 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 2 cleaned: Name='Jaeger1000 76 points', Value='40 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 3: Raw name='Nincompoopz 65 points', Raw value='35 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz 65 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz 65 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 35 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 35 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 3 cleaned: Name='Nincompoopz 65 points', Value='35 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 4: Raw name='LegendVFX 46 points', Raw value='30 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX 46 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX 46 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 30 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 30 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 4 cleaned: Name='LegendVFX 46 points', Value='30 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 5: Raw name='KeysFish 44 points', Raw value='25 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish 44 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish 44 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 25 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 25 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 5 cleaned: Name='KeysFish 44 points', Value='25 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 6: Raw name='m0mmyymilk3rs 42 points', Raw value='20 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs 42 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs 42 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 20 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 20 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 6 cleaned: Name='m0mmyymilk3rs 42 points', Value='20 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 7: Raw name='THCPineapple 41 points', Raw value='15 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple 41 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple 41 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 15 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 15 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 7 cleaned: Name='THCPineapple 41 points', Value='15 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 8: Raw name='Chaneru 35 points', Raw value='10 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru 35 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru 35 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 8 cleaned: Name='Chaneru 35 points', Value='10 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 9: Raw name='Gerritperrit 34 points', Raw value='10 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit 34 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit 34 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 9 cleaned: Name='Gerritperrit 34 points', Value='10 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 10: Raw name='minidomo 27 points', Raw value='5 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: minidomo 27 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: minidomo 27 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 10 cleaned: Name='minidomo 27 points', Value='5 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 11: Raw name='caractus 25 points', Raw value='5 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: caractus 25 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: caractus 25 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 11 cleaned: Name='caractus 25 points', Value='5 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Processing player 12: Raw name='xSisqoo 25 points', Raw value='5 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xSisqoo 25 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xSisqoo 25 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] Player 12 cleaned: Name='xSisqoo 25 points', Value='5 GC'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Top 12 Players:...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Top 12 Players:...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Top 12 Players:' -> 'Top 12 Players:'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Mobs Killed' -> 'Mobs Killed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Scrolls Completed ◆ DOUBLE POINTS ...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Scrolls Completed ◆ DOUBLE POI...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Scrolls Completed ◆ DOUBLE POINTS ◆' -> 'Scrolls Completed ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Runes Identified...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Runes Identified...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Runes Identified' -> 'Runes Identified'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Blocks Placed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Blocks Placed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Blocks Placed' -> 'Blocks Placed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Crops Harvested...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Crops Harvested...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Crops Harvested' -> 'Crops Harvested'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Ores Mined...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Ores Mined...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Ores Mined' -> 'Ores Mined'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Pouches Opened...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Pouches Opened...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Pouches Opened' -> 'Pouches Opened'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Votes' -> 'Votes'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Fish Caught...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Fish Caught...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Fish Caught' -> 'Fish Caught'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Envoys Claimed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Envoys Claimed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Envoys Claimed' -> 'Envoys Claimed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Quests Completed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Quests Completed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Quests Completed' -> 'Quests Completed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Experience Gained...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Experience Gained...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Experience Gained' -> 'Experience Gained'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Shop Items Sold...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Shop Items Sold...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Shop Items Sold' -> 'Shop Items Sold'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: PvP Kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: PvP Kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'PvP Kills' -> 'PvP Kills'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Time Played...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Time Played...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Time Played' -> 'Time Played'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Time in Pharaoh's Tomb...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Time in Pharaoh's Tomb...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Time in Pharaoh's Tomb' -> 'Time in Pharaoh's Tomb'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Pyramid Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Pyramid Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Pyramid Mobs Killed' -> 'Pyramid Mobs Killed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gold Shards Collected...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gold Shards Collected...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Gold Shards Collected' -> 'Gold Shards Collected'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Treasure Chests Opened ◆ DOUBLE PO...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Treasure Chests Opened ◆ DOUBL...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Treasure Chests Opened ◆ DOUBLE POINTS ◆' -> 'Treasure Chests Opened ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Blocks Traveled On Foot...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Blocks Traveled On Foot...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Blocks Traveled On Foot' -> 'Blocks Traveled On Foot'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Minigames Won...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Minigames Won...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Minigames Won' -> 'Minigames Won'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Logs Chopped...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Logs Chopped...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Logs Chopped' -> 'Logs Chopped'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Bosses Killed ◆ DOUBLE POINTS ...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Bosses Killed ◆ DOUBLE POINTS ...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Bosses Killed ◆ DOUBLE POINTS ◆' -> 'Bosses Killed ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [Phase 4] Updating persistent leaderboard message (ID: aaeda853-5c3d-44c9-9264-9ed9b7378356)
[MCC] [MultiPhaseBot] [Phase 4] Processing leaderboard embed JSON (length: 2741 chars)
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing 89 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing 89 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 50 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 50 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000 76 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000 76 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 40 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 40 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz 65 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz 65 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 35 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 35 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX 46 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX 46 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 30 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 30 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish 44 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish 44 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 25 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 25 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs 42 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs 42 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 20 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 20 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple 41 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple 41 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 15 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 15 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru 35 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru 35 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit 34 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit 34 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: minidomo 27 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: minidomo 27 points...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5 GC...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Top 12 Players:...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Top 12 Players:...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Top 12 Players:' -> 'Top 12 Players:'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: minidomo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: minidomo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 397,226 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 397,226 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 69,917 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 69,917 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Amusin...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Amusin...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 52,169 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 52,169 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 38,039 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 38,039 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Rhazzel...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Rhazzel...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 34,409 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 34,409 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xamid...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xamid...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 32,794 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 32,794 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Geen...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Geen...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 32,045 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 32,045 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 32,012 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 32,012 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 30,116 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 30,116 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: NL_Peanut...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: NL_Peanut...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 25,810 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 25,810 mobs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Mobs Killed' -> 'Mobs Killed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaxair...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaxair...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 15 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 15 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: TheTwoInchPinch...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: TheTwoInchPinch...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Farrnado...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Farrnado...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Roleeb...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Roleeb...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Andtarski...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Andtarski...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 8 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 8 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: mattep83...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: mattep83...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: YYtheYY...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: YYtheYY...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: WektorTR...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: WektorTR...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Rucking...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Rucking...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5 scrolls...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Scrolls Completed ◆ DOUBLE POINTS ...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Scrolls Completed ◆ DOUBLE POI...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Scrolls Completed ◆ DOUBLE POINTS ◆' -> 'Scrolls Completed ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 3,388 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 3,388 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,933 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,933 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: caractus...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: caractus...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,753 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,753 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,473 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,473 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Roexoe...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Roexoe...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,738 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,738 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MR_Trigger...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MR_Trigger...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,635 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,635 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: IZekt...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: IZekt...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,315 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,315 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LogicalPlays...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LogicalPlays...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,264 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,264 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Cataclysm69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Cataclysm69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 895 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 895 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: maxappleimac1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: maxappleimac1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 889 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 889 runes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Runes Identified...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Runes Identified...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Runes Identified' -> 'Runes Identified'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: vs1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: vs1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 55,349 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 55,349 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Rarote...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Rarote...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 29,972 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 29,972 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KnownRandom...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KnownRandom...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 27,530 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 27,530 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Rossin_1023...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Rossin_1023...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 26,416 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 26,416 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Blaze_pvp1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Blaze_pvp1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 25,805 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 25,805 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: .IDontRageYT0...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: .IDontRageYT0...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 23,643 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 23,643 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Pore69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Pore69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 21,894 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 21,894 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Kllua_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Kllua_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 21,702 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 21,702 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: zambino1909...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: zambino1909...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 19,007 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 19,007 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: TheOnlyLegalMex...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: TheOnlyLegalMex...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 18,952 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 18,952 blocks...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Blocks Placed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Blocks Placed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Blocks Placed' -> 'Blocks Placed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,208,032 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,208,032 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,003,653 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,003,653 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Silverdogs33...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Silverdogs33...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 915,491 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 915,491 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Ahhhyuu...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Ahhhyuu...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 815,677 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 815,677 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 756,877 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 756,877 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: The__Name_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: The__Name_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 691,773 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 691,773 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 688,636 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 688,636 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 539,696 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 539,696 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: GyroGyro...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: GyroGyro...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 532,949 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 532,949 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Bivestew_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Bivestew_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 479,620 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 479,620 crops...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Crops Harvested...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Crops Harvested...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Crops Harvested' -> 'Crops Harvested'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,207,727 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,207,727 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,958,006 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,958,006 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: caractus...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: caractus...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,727,959 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,727,959 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,637,077 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,637,077 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Flamming_Sword...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Flamming_Sword...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,096,047 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,096,047 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LogicalPlays...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LogicalPlays...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 911,209 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 911,209 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: VoidLovesMoney...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: VoidLovesMoney...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 829,321 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 829,321 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: IZekt...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: IZekt...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 804,498 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 804,498 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: old_nanna...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: old_nanna...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 795,944 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 795,944 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Cataclysm69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Cataclysm69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 661,994 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 661,994 ores...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Ores Mined...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Ores Mined...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Ores Mined' -> 'Ores Mined'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,417 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,417 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,744 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,744 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,664 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,664 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,663 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,663 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: BaconBaconBacon_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: BaconBaconBacon_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,652 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,652 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Roexoe...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Roexoe...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,626 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,626 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Bozo_Block...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Bozo_Block...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,606 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,606 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,548 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,548 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: EnchantedHigh5...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: EnchantedHigh5...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,218 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,218 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: FlactioON...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: FlactioON...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1,064 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1,064 pouches...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Pouches Opened...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Pouches Opened...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Pouches Opened' -> 'Pouches Opened'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 34 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 34 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: _Hyfr...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: _Hyfr...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 34 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 34 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: minidomo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: minidomo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 33 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 33 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Kat903...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Kat903...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 32 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 32 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: x_Fluffy_Bunny_x...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: x_Fluffy_Bunny_x...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 31 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 31 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: BlueFusion_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: BlueFusion_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 30 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 30 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: DraGunnXFire...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: DraGunnXFire...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 29 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 29 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 28 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 28 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ruen03...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ruen03...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 28 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 28 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 25 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 25 votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Votes...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Votes' -> 'Votes'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: FantaManBam...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: FantaManBam...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 27,625 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 27,625 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: WillieTheMan...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: WillieTheMan...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 17,374 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 17,374 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KR33PY...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KR33PY...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 15,740 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 15,740 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: EnchantedHigh5...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: EnchantedHigh5...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 15,529 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 15,529 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11,177 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11,177 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Klaxonn1903...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Klaxonn1903...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10,702 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10,702 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Soopraiise...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Soopraiise...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10,462 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10,462 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: AlitaBoss99...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: AlitaBoss99...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9,985 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9,985 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: C0OB...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: C0OB...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9,788 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9,788 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ExtWaffleBossAL...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ExtWaffleBossAL...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9,744 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9,744 fish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Fish Caught...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Fish Caught...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Fish Caught' -> 'Fish Caught'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: _Syvix...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: _Syvix...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 164 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 164 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 126 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 126 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 86 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 86 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 46 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 46 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 40 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 40 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 39 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 39 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: duhismoke312...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: duhismoke312...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 37 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 37 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: _IamDepressed_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: _IamDepressed_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 29 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 29 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: VoidLovesMoney...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: VoidLovesMoney...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 26 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 26 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Ketchup47...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Ketchup47...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 26 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 26 envoys...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Envoys Claimed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Envoys Claimed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Envoys Claimed' -> 'Envoys Claimed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xamid...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xamid...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Farrnado...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Farrnado...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Geen...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Geen...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 6 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 6 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 6 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 6 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: SleepiestAgent...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: SleepiestAgent...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: agrajagagrajag...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: agrajagagrajag...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: TallOmnivore129...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: TallOmnivore129...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: .Pulsebudzv2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: .Pulsebudzv2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Sleepo_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Sleepo_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2 quests...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Quests Completed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Quests Completed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Quests Completed' -> 'Quests Completed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 37,857,615 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 37,857,615 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: caractus...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: caractus...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 16,349,052 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 16,349,052 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gerritperrit...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 14,655,771 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 14,655,771 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Cataclysm69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Cataclysm69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 13,437,308 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 13,437,308 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12,225,707 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12,225,707 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LogicalPlays...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LogicalPlays...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10,026,353 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10,026,353 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: TheSerezShow...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: TheSerezShow...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 8,355,729 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 8,355,729 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: chrisss069...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: chrisss069...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,660,376 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,660,376 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: old_nanna...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: old_nanna...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,547,436 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,547,436 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jimbob123007...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jimbob123007...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 4,973,055 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 4,973,055 experience...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Experience Gained...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Experience Gained...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Experience Gained' -> 'Experience Gained'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $2,765,420,376...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $2,765,420,376...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: EnchantedHigh5...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: EnchantedHigh5...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $1,275,100,808...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $1,275,100,808...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $1,256,508,536...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $1,256,508,536...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $1,256,483,901...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $1,256,483,901...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: SnoCozart...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: SnoCozart...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $1,133,642,642...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $1,133,642,642...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $970,778,332...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $970,778,332...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $886,700,648...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $886,700,648...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Silverdogs33...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Silverdogs33...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $774,610,306...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $774,610,306...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $699,487,404...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $699,487,404...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: samuello...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: samuello...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: $669,338,374...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: $669,338,374...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Shop Items Sold...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Shop Items Sold...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Shop Items Sold' -> 'Shop Items Sold'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: _Syvix...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: _Syvix...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 35 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 35 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 17 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 17 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xSisqoFanBoy2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xSisqoFanBoy2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 13 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 13 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: U0w0o...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: U0w0o...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Cerv...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Cerv...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MineFarge...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MineFarge...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Akid_43...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Akid_43...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Amusin...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Amusin...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: wzne...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: wzne...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9 kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: PvP Kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: PvP Kills...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'PvP Kills' -> 'PvP Kills'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1d 11h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1d 11h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: litsa_kavlitsa69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: litsa_kavlitsa69...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1d 3h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1d 3h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: WLDFakeZz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: WLDFakeZz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1d 2h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1d 2h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Chaneru...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1d 1h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1d 1h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MrPyrza...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MrPyrza...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1d 1h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1d 1h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: TheTwoInchPinch...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: TheTwoInchPinch...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1d 1h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1d 1h...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: SarcasticReaper...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: SarcasticReaper...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 23h 53m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 23h 53m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: SurfSounds...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: SurfSounds...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 22h 45m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 22h 45m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 22h 15m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 22h 15m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Farrnado...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Farrnado...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 21h 43m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 21h 43m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Time Played...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Time Played...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Time Played' -> 'Time Played'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 17h 33m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 17h 33m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: roughjoke95...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: roughjoke95...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12h 7m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12h 7m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9h 44m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9h 44m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Hgcrafted...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Hgcrafted...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9h 35m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9h 35m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: .beastieman420...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: .beastieman420...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9h 7m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9h 7m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7h 55m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7h 55m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: .Happymatt8663...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: .Happymatt8663...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7h 52m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7h 52m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: jonnyjamm...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: jonnyjamm...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7h 31m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7h 31m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: .brayden16t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: .brayden16t...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7h 29m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7h 29m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: kurkland...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: kurkland...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 7h 6m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 7h 6m...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Time in Pharaoh's Tomb...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Time in Pharaoh's Tomb...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Time in Pharaoh's Tomb' -> 'Time in Pharaoh's Tomb'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12,377...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12,377...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9,048...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9,048...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: .beastieman420...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: .beastieman420...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 6,746...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 6,746...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: roughjoke95...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: roughjoke95...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,798...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,798...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Tazah_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Tazah_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,447...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,447...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 4,773...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 4,773...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: BluePantherYT...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: BluePantherYT...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 4,572...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 4,572...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Ambrith...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Ambrith...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 4,196...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 4,196...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MusseBaby...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MusseBaby...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 3,780...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 3,780...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: DaSimsGamer...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 3,612...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 3,612...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Pyramid Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Pyramid Mobs Killed...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Pyramid Mobs Killed' -> 'Pyramid Mobs Killed'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Soopraiise...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Soopraiise...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 8,502...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 8,502...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Roexoe...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Roexoe...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,963...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,963...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ZeroVoids...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ZeroVoids...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,262...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,262...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: maxappleimac1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: maxappleimac1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 5,153...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 5,153...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Mr_Gooberville...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Mr_Gooberville...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 3,083...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 3,083...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Tazah_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Tazah_...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 3,074...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 3,074...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,967...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,967...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: BluePantherYT...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: BluePantherYT...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,254...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,254...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Christian359...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Christian359...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,213...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,213...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2,149...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2,149...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Gold Shards Collected...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Gold Shards Collected...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Gold Shards Collected' -> 'Gold Shards Collected'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 6...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 6...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: m0mmyymilk3rs...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 3...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 3...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Yuri2l...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Yuri2l...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: lorenab81...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: lorenab81...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: PsykkoAU...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: PsykkoAU...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jimbob123007...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jimbob123007...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: NoWillToLive1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: NoWillToLive1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Mr_T0Jl...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Mr_T0Jl...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Treasure Chests Opened ◆ DOUBLE PO...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Treasure Chests Opened ◆ DOUBL...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Treasure Chests Opened ◆ DOUBLE POINTS ◆' -> 'Treasure Chests Opened ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Trener1s...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Trener1s...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 292,821...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 292,821...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KeysFish...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 290,931...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 290,931...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Jaeger1000...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 271,504...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 271,504...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: LegendVFX...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 239,406...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 239,406...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Huge4LitreMilk...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Huge4LitreMilk...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 231,888...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 231,888...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xSisqoo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xSisqoo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 212,461...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 212,461...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: _Syvix...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: _Syvix...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 176,762...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 176,762...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: _ColdYeti...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: _ColdYeti...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 172,660...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 172,660...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 172,469...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 172,469...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Scryia...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Scryia...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 169,823...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 169,823...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Blocks Traveled On Foot...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Blocks Traveled On Foot...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Blocks Traveled On Foot' -> 'Blocks Traveled On Foot'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: pilot12345...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: pilot12345...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: KnownRandom...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: KnownRandom...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Syfexi...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Syfexi...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 6...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 6...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Egestino...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Egestino...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 4...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 4...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Wh0_Wh0...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Wh0_Wh0...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 2...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Kingly_gaoler3...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Kingly_gaoler3...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: kircaaa...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: kircaaa...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Odzin__...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Odzin__...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Amusin...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Amusin...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Nincompoopz...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Minigames Won...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Minigames Won...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Minigames Won' -> 'Minigames Won'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Soopraiise...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Soopraiise...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 42,077...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 42,077...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: minidomo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: minidomo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 19,676...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 19,676...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: old_nanna...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: old_nanna...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 14,180...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 14,180...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: maxappleimac1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: maxappleimac1...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12,365...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12,365...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: kenzie194...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: kenzie194...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 12,263...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 12,263...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11,531...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11,531...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xamid...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xamid...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 11,324...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 11,324...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: FlactioON...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: FlactioON...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 10,334...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 10,334...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: seabarrel007...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: seabarrel007...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 9,533...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 9,533...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: JpGoneCrazy...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: JpGoneCrazy...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 6,848...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 6,848...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Logs Chopped...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Logs Chopped...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Logs Chopped' -> 'Logs Chopped'
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: xSisqoo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: xSisqoo...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 116...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 116...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: MostlyMissing...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 106...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 106...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Lil_Bouncy21...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Lil_Bouncy21...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 103...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 103...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Ketchup47...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Ketchup47...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 93...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 93...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: THCPineapple...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 51...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 51...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ruined_123...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ruined_123...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 49...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 49...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Scryia...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Scryia...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 43...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 43...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: ZeroVoids...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: ZeroVoids...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 38...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 38...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Sup3rSweZy...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Sup3rSweZy...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 34...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 34...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: wzne...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: wzne...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: 33...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: 33...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes called with: Bosses Killed ◆ DOUBLE POINTS ...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] No control characters found in: Bosses Killed ◆ DOUBLE POINTS ...
[MCC] [MultiPhaseBot] [ENHANCED DEBUG] RemoveMinecraftColorCodes result: 'Bosses Killed ◆ DOUBLE POINTS ◆' -> 'Bosses Killed ◆ DOUBLE POINTS ◆'
[MCC] [MultiPhaseBot] [Phase 4] Converting message data to JSON for message ID: aaeda853...
[MCC] [MultiPhaseBot] [Phase 4] Final JSON length: 21518 characters
[MCC] [MultiPhaseBot] [Phase 4] Leaderboard embed written to queue: UPDATE - Message ID: aaeda853-5c3d-44c9-9264-9ed9b7378356
[MCC] [MultiPhaseBot] [Phase 4] ✅ Leaderboard embed written to file successfully
[MCC] [MultiPhaseBot] [Phase 3] Transitioning to close leaderboard inventory
[MCC] [MultiPhaseBot] [State] ExtractingLeaderboardData -> ClosingLeaderboardInventory
[MCC] [MultiPhaseBot] [Phase 3] Closing leaderboard inventory and preparing for cycle completion...
[MCC] [MultiPhaseBot] [Phase 3] Found 2 open inventories
[MCC] [MultiPhaseBot] [Phase 3] Closing leaderboard inventory #3 using MCC API CloseInventory() method
[MCC] [MultiPhaseBot] [Phase 3] Successfully closed leaderboard inventory #3
[MCC] [MultiPhaseBot] [Phase 3] Leaderboard inventory closure completed, returning to idle state
[MCC] [MultiPhaseBot] [State] ClosingLeaderboardInventory -> Idle
[MCC] [MultiPhaseBot] [Phase 1] Next task scheduled for 13:55:38
[MCC] [MultiPhaseBot] ✅ === SCHEDULED 10-MINUTE CYCLE #1 COMPLETE at 13:45:38 ===
[MCC] [MultiPhaseBot] [Cycle 1] Duration: 16.0 seconds
[MCC] [MultiPhaseBot] [Cycle 1] ⏰ Next cycle scheduled for 13:55:38