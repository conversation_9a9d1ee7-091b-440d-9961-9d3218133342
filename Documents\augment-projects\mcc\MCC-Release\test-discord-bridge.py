#!/usr/bin/env python3
"""
Test script for MCC Discord Bridge Bot
Creates sample log entries to test the bridge functionality
"""

import json
import time
from pathlib import Path
from datetime import datetime

# Test configuration
TEST_LOG_FILE = Path("test_inventory.txt")
SAMPLE_EMBEDS = [
    {
        "channel": "1401451296711507999",
        "embed": {
            "embeds": [{
                "title": "🤖 Multi-Phase Bot Started",
                "description": "<PERSON><PERSON> has been initialized and is ready for operation",
                "color": 65280,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "footer": {
                    "text": "Multi-Phase Bot Test",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/bot-icon.png"
                },
                "fields": [
                    {
                        "name": "🔄 Current State",
                        "value": "Idle",
                        "inline": True
                    },
                    {
                        "name": "📊 Active Phases",
                        "value": "Phase 1: ✅\nPhase 2: ✅\nPhase 3: ✅\nPhase 4: ✅",
                        "inline": True
                    }
                ]
            }]
        }
    },
    {
        "channel": "1401451313216094383",
        "embed": {
            "embeds": [{
                "title": "🏝️ Top Islands Data",
                "description": "Island rankings extracted at " + datetime.now().strftime("%H:%M:%S"),
                "color": 39423,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "fields": [
                    {
                        "name": "🏝️ #1: Revenants",
                        "value": "**All Time:** $2.96B\n**Weekly:** $1.2B\n**Today:** +$31.34M (+1.5%)",
                        "inline": True
                    },
                    {
                        "name": "🏝️ #2: ████NIP████",
                        "value": "**All Time:** $1.4B\n**Weekly:** $800M\n**Today:** +$15.2M (+1.1%)",
                        "inline": True
                    }
                ],
                "footer": {
                    "text": "Phase 2 • 5 islands tracked",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/island-icon.png"
                }
            }]
        }
    },
    {
        "channel": "1401451313216094383",
        "embed": {
            "embeds": [{
                "title": "🏆 Leaderboard Rankings",
                "description": "Player rankings extracted at " + datetime.now().strftime("%H:%M:%S"),
                "color": 16766720,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "fields": [
                    {
                        "name": "🏆 Mobs Killed",
                        "value": "#1: minidomo (1,777,730 mobs)\n#2: DaSimsGamer (141,602 mobs)\n#3: JustABanana777 (130,897 mobs)",
                        "inline": True
                    },
                    {
                        "name": "🏆 Scrolls Completed",
                        "value": "#1: Farrnado (31 scrolls)\n#2: Jaxair (31 scrolls)\n#3: Roleeb (25 scrolls)",
                        "inline": True
                    },
                    {
                        "name": "🏆 Gold Shards Collected",
                        "value": "#1: FlactioON (13,210 shards)\n#2: MostlyMissing (8,945 shards)\n#3: LastRez (7,234 shards)",
                        "inline": True
                    }
                ],
                "footer": {
                    "text": "Phase 3 • 24 categories tracked",
                    "icon_url": "https://cdn.discordapp.com/attachments/123456789/leaderboard-icon.png"
                }
            }]
        }
    }
]

def create_test_log():
    """Create a test log file with sample Discord embed entries"""
    print("🧪 Creating test log file...")
    
    with open(TEST_LOG_FILE, 'w', encoding='utf-8') as f:
        # Write initial log entries
        f.write("[MCC] [MultiPhaseBot] === MULTI-PHASE BOT INITIALIZATION ===\n")
        f.write("[MCC] [MultiPhaseBot] Multi-Phase Bot initialized successfully\n")
        f.write("[MCC] [MultiPhaseBot] Phase 4 enabled: True\n")
        f.write("[MCC] [MultiPhaseBot] [Phase 4] Discord integration initialized successfully\n")
        f.write("\n")
        
        # Write sample Discord messages
        for i, sample in enumerate(SAMPLE_EMBEDS):
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # Write channel line
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Sending Discord embed to channel {sample['channel']}\n")
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Channel: {sample['channel']}\n")
            
            # Write content line
            embed_json = json.dumps(sample['embed'], separators=(',', ':'))
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Content: {embed_json}\n")
            f.write(f"[MCC] [MultiPhaseBot] [Phase 4] ✅ Message delivered successfully\n")
            f.write("\n")
            
            print(f"✅ Added test message {i+1}: {sample['embed']['embeds'][0]['title']}")
    
    print(f"📁 Test log file created: {TEST_LOG_FILE}")
    return TEST_LOG_FILE

def simulate_real_time_updates():
    """Simulate real-time log updates by appending new entries"""
    print("\n🔄 Simulating real-time log updates...")
    print("Press Ctrl+C to stop simulation")
    
    try:
        counter = 1
        while True:
            time.sleep(10)  # Wait 10 seconds between updates
            
            # Create a new test message
            test_embed = {
                "channel": "1401451296711507999",
                "embed": {
                    "embeds": [{
                        "title": f"🔄 Test Update #{counter}",
                        "description": f"Real-time test message generated at {datetime.now().strftime('%H:%M:%S')}",
                        "color": 3447003,
                        "timestamp": datetime.utcnow().isoformat() + "Z",
                        "fields": [
                            {
                                "name": "📊 Test Counter",
                                "value": str(counter),
                                "inline": True
                            },
                            {
                                "name": "⏰ Generated At",
                                "value": datetime.now().strftime("%H:%M:%S"),
                                "inline": True
                            }
                        ]
                    }]
                }
            }
            
            # Append to log file
            with open(TEST_LOG_FILE, 'a', encoding='utf-8') as f:
                f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Sending Discord embed to channel {test_embed['channel']}\n")
                f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Channel: {test_embed['channel']}\n")
                embed_json = json.dumps(test_embed['embed'], separators=(',', ':'))
                f.write(f"[MCC] [MultiPhaseBot] [Phase 4] Content: {embed_json}\n")
                f.write(f"[MCC] [MultiPhaseBot] [Phase 4] ✅ Message delivered successfully\n")
                f.write("\n")
            
            print(f"📤 Added real-time test message #{counter}")
            counter += 1
            
    except KeyboardInterrupt:
        print("\n🛑 Real-time simulation stopped")

def main():
    """Main test function"""
    print("🧪 MCC Discord Bridge Bot - Test Script")
    print("=" * 50)
    
    # Create test log file
    test_file = create_test_log()
    
    print(f"\n📋 Test Instructions:")
    print(f"1. Update discord-bridge-config.json:")
    print(f"   Set 'mcc_log_file' to '{test_file}'")
    print(f"2. Start the Discord bridge bot:")
    print(f"   python discord-bridge-bot.py")
    print(f"3. Check Discord channels for test messages")
    print(f"4. Monitor bridge bot logs for processing")
    
    # Ask if user wants real-time simulation
    print(f"\n🔄 Options:")
    print(f"A. Exit now and test with static log file")
    print(f"B. Continue with real-time log simulation")
    
    choice = input("\nEnter choice (A/B): ").strip().upper()
    
    if choice == 'B':
        simulate_real_time_updates()
    else:
        print(f"\n✅ Test setup complete!")
        print(f"📁 Test log file: {test_file}")
        print(f"🚀 Start the Discord bridge bot to test functionality")

if __name__ == "__main__":
    main()
