#!/usr/bin/env python3
"""
Test JSON Fix - Verify the JSON parsing issues are resolved
"""

import json
import os

def test_current_queue():
    """Test the current discord_queue.json file"""
    print("🧪 Testing Current JSON Queue File")
    print("=" * 40)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} lines in queue file")
        
        success_count = 0
        error_count = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # Test JSON parsing
                message_data = json.loads(line)
                
                # Check if it's an LB channel message
                channel_name = message_data.get('channel_name', 'Unknown')
                message_id = message_data.get('id', 'Unknown')[:8]
                
                if channel_name == 'LB':
                    print(f"✅ Line {i}: Valid LB channel JSON - ID {message_id}...")
                else:
                    print(f"✅ Line {i}: Valid {channel_name} channel JSON - ID {message_id}...")
                
                success_count += 1
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
                print(f"   Content preview: {line[:100]}...")
                error_count += 1
        
        print()
        print("📊 Test Results:")
        print(f"   ✅ Valid JSON: {success_count}")
        print(f"   ❌ Invalid JSON: {error_count}")
        
        if error_count == 0:
            print("🎉 All JSON is valid! The parsing errors should be resolved.")
        else:
            print("⚠️ Some JSON is still invalid. The MCC bot needs to be restarted.")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    print("🔧 JSON Fix Verification")
    print("=" * 50)
    print()
    
    test_current_queue()
    
    print()
    print("Next steps:")
    print("1. If JSON is valid: The fix worked!")
    print("2. If JSON is invalid: Restart MCC bot with: /script multi-phase-bot")
    print("3. Clear queue file and restart if needed")

if __name__ == "__main__":
    main()
