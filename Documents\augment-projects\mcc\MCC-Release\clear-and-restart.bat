@echo off
echo ========================================
echo Clear Queue and Restart System
echo ========================================
echo.

echo Step 1: Backing up current queue file...
if exist "discord_queue.json" (
    copy discord_queue.json discord_queue_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.json >nul 2>&1
    echo ✅ Backup created
) else (
    echo ⚠️ No queue file found
)

echo.
echo Step 2: Clearing corrupted queue file...
echo. > discord_queue.json
echo ✅ Queue file cleared

echo.
echo Step 3: Clearing processed messages log...
if exist "processed_discord_messages.json" (
    del processed_discord_messages.json
    echo ✅ Processed messages log cleared
) else (
    echo ⚠️ No processed messages log found
)

echo.
echo ========================================
echo Queue cleared successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Start Discord Bridge Bot: start-discord-bridge-v2.bat
echo 2. Start MCC Bot: /script multi-phase-bot
echo 3. Wait for first cycle (10 seconds)
echo 4. Check Discord channels for messages
echo.
echo The system should now work without JSON parsing errors.
echo.
pause
