#!/usr/bin/env python3
"""
Deep character-by-character analysis of the problematic message
"""

import json

def deep_analyze_message():
    """Perform character-by-character analysis of message a69a7ebe"""
    
    target_id = "ab872802"
    
    print("🔬 DEEP CHARACTER ANALYSIS OF MESSAGE ab872802")
    print("=" * 70)
    
    try:
        with open("discord_queue.json", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        target_message = None
        target_line = None
        for line in lines:
            if line.strip() and target_id in line:
                target_line = line.strip()
                try:
                    target_message = json.loads(line.strip())
                    break
                except json.JSONDecodeError:
                    continue
        
        if not target_message or not target_line:
            print(f"❌ Message {target_id} not found")
            return
        
        print(f"✅ Found message in queue")
        print(f"📏 Full line length: {len(target_line)} characters")
        
        # Analyze the FULL JSON line (not just embed_data_raw)
        print(f"\n🔍 ANALYZING FULL JSON LINE:")
        
        # Find ALL control characters in the full line
        all_control_chars = []
        for i, char in enumerate(target_line):
            char_code = ord(char)
            if char_code < 32 and char not in ['\n', '\r', '\t']:
                all_control_chars.append({
                    'position': i,
                    'char': repr(char),
                    'ord': char_code,
                    'hex': f"0x{char_code:02X}",
                    'description': get_control_char_description(char_code)
                })
        
        if all_control_chars:
            print(f"❌ FOUND {len(all_control_chars)} CONTROL CHARACTERS IN FULL JSON:")
            for ctrl in all_control_chars:
                print(f"   Position {ctrl['position']}: {ctrl['char']} (ord: {ctrl['ord']}, hex: {ctrl['hex']}) - {ctrl['description']}")
        else:
            print(f"✅ No control characters found in full JSON line")
        
        # Focus on position 91 specifically
        print(f"\n📍 DETAILED ANALYSIS OF POSITION 91:")
        if len(target_line) > 90:
            char_91 = target_line[90]
            char_code = ord(char_91)
            print(f"   Character: {repr(char_91)}")
            print(f"   Ord: {char_code}")
            print(f"   Hex: 0x{char_code:02X}")
            print(f"   Binary: {bin(char_code)}")
            print(f"   Is control char: {char_code < 32 and char_91 not in ['\n', '\r', '\t']}")
            print(f"   Is printable: {char_91.isprintable()}")
            print(f"   Description: {get_control_char_description(char_code)}")
        
        # Show detailed context around position 91
        print(f"\n📍 CONTEXT AROUND POSITION 91:")
        start = max(0, 80)
        end = min(len(target_line), 105)
        context = target_line[start:end]
        
        print(f"   Range: {start}-{end}")
        print(f"   Text: {repr(context)}")
        
        # Character-by-character breakdown
        print(f"\n   CHARACTER-BY-CHARACTER BREAKDOWN:")
        for i, char in enumerate(context):
            pos = start + i
            char_code = ord(char)
            is_control = char_code < 32 and char not in ['\n', '\r', '\t']
            marker = " <<<< CONTROL CHAR" if is_control else ""
            print(f"     Pos {pos:3d}: {repr(char):>6} (ord: {char_code:3d}, hex: 0x{char_code:02X}){marker}")
        
        # Try to parse with different methods
        print(f"\n🧪 PARSING TESTS:")
        
        # Test 1: Standard JSON parsing
        try:
            parsed = json.loads(target_line)
            print(f"   ✅ Standard JSON parsing: SUCCESS")
        except json.JSONDecodeError as e:
            print(f"   ❌ Standard JSON parsing: FAILED")
            print(f"      Error: {e}")
            print(f"      Position: {getattr(e, 'pos', 'unknown')}")
            print(f"      Line: {getattr(e, 'lineno', 'unknown')}")
            print(f"      Column: {getattr(e, 'colno', 'unknown')}")
        
        # Test 2: Try to isolate the embed_data_raw field
        embed_start = target_line.find('"embed_data_raw":"')
        if embed_start != -1:
            embed_start += len('"embed_data_raw":"')
            # Find the end of the embed_data_raw field (look for the closing quote before the next field)
            embed_end = target_line.find('","status":', embed_start)
            if embed_end != -1:
                embed_data_raw = target_line[embed_start:embed_end]
                print(f"\n📊 ISOLATED EMBED_DATA_RAW:")
                print(f"   Length: {len(embed_data_raw)}")
                print(f"   Start position in full JSON: {embed_start}")
                print(f"   End position in full JSON: {embed_end}")
                
                # Check if position 91 falls within embed_data_raw
                if embed_start <= 91 <= embed_end:
                    relative_pos = 91 - embed_start
                    print(f"   Position 91 is INSIDE embed_data_raw at relative position {relative_pos}")
                    if relative_pos < len(embed_data_raw):
                        char_in_embed = embed_data_raw[relative_pos]
                        print(f"   Character at relative pos {relative_pos}: {repr(char_in_embed)} (ord: {ord(char_in_embed)})")
                else:
                    print(f"   Position 91 is OUTSIDE embed_data_raw")
                
                # Test parsing the isolated embed_data_raw
                try:
                    # Unescape the JSON string
                    unescaped = embed_data_raw.encode().decode('unicode_escape')
                    parsed_embed = json.loads(unescaped)
                    print(f"   ✅ Isolated embed_data_raw parsing: SUCCESS")
                except Exception as e:
                    print(f"   ❌ Isolated embed_data_raw parsing: FAILED")
                    print(f"      Error: {e}")
        
        # Test 3: Check for Unicode issues
        print(f"\n🔤 UNICODE ANALYSIS:")
        try:
            # Try encoding/decoding
            encoded = target_line.encode('utf-8')
            decoded = encoded.decode('utf-8')
            print(f"   ✅ UTF-8 encoding/decoding: SUCCESS")
        except Exception as e:
            print(f"   ❌ UTF-8 encoding/decoding: FAILED - {e}")
        
        # Check for byte order marks or other hidden characters
        if target_line.startswith('\ufeff'):
            print(f"   ⚠️ Found BOM (Byte Order Mark) at start")
        
        # Look for non-printable characters that might not be detected as control chars
        non_printable = []
        for i, char in enumerate(target_line):
            if not char.isprintable() and char not in ['\n', '\r', '\t']:
                non_printable.append((i, char, ord(char)))
        
        if non_printable:
            print(f"   ❌ Found {len(non_printable)} non-printable characters:")
            for pos, char, code in non_printable[:10]:  # Show first 10
                print(f"      Position {pos}: {repr(char)} (ord: {code})")
        else:
            print(f"   ✅ All characters are printable")
        
    except Exception as e:
        print(f"❌ Error in deep analysis: {e}")
        import traceback
        traceback.print_exc()

def get_control_char_description(char_code):
    """Get description of control character"""
    descriptions = {
        0: "NULL",
        1: "SOH (Start of Heading)",
        2: "STX (Start of Text)",
        3: "ETX (End of Text)",
        4: "EOT (End of Transmission)",
        5: "ENQ (Enquiry)",
        6: "ACK (Acknowledge)",
        7: "BEL (Bell)",
        8: "BS (Backspace)",
        9: "TAB (Horizontal Tab)",
        10: "LF (Line Feed)",
        11: "VT (Vertical Tab)",
        12: "FF (Form Feed)",
        13: "CR (Carriage Return)",
        14: "SO (Shift Out)",
        15: "SI (Shift In)",
        16: "DLE (Data Link Escape)",
        17: "DC1 (Device Control 1)",
        18: "DC2 (Device Control 2)",
        19: "DC3 (Device Control 3)",
        20: "DC4 (Device Control 4)",
        21: "NAK (Negative Acknowledge)",
        22: "SYN (Synchronous Idle)",
        23: "ETB (End of Transmission Block)",
        24: "CAN (Cancel)",
        25: "EM (End of Medium)",
        26: "SUB (Substitute)",
        27: "ESC (Escape)",
        28: "FS (File Separator)",
        29: "GS (Group Separator)",
        30: "RS (Record Separator)",
        31: "US (Unit Separator)",
        127: "DEL (Delete)"
    }
    return descriptions.get(char_code, f"Unknown control character")

if __name__ == "__main__":
    deep_analyze_message()
