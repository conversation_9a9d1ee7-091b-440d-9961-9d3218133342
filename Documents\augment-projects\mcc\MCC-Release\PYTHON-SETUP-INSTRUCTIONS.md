# Python Setup Instructions for Discord Bridge Bot

## 🐍 **Python Installation Required**

The Discord bridge bot requires Python 3.8+ to run. Here's how to install and set it up:

## 📥 **Step 1: Install Python**

### **Method 1: Official Python Installer (Recommended)**
1. **Download Python**: Go to https://python.org/downloads/
2. **Download latest version** (3.11 or 3.12 recommended)
3. **Run installer** with these important settings:
   - ✅ **Check "Add Python to PATH"** (CRITICAL!)
   - ✅ **Check "Install for all users"**
   - ✅ **Choose "Customize installation"**
   - ✅ **Check all optional features**
   - ✅ **Check "Add Python to environment variables"**

### **Method 2: Microsoft Store (Alternative)**
1. **Open Microsoft Store**
2. **Search for "Python"**
3. **Install "Python 3.11" or "Python 3.12"**
4. **This automatically adds Python to PATH**

## 🔧 **Step 2: Verify Python Installation**

Open **Command Prompt** or **PowerShell** and run:
```bash
python --version
# Should show: Python 3.11.x or similar

pip --version
# Should show: pip 23.x.x or similar
```

If these commands don't work, try:
```bash
py --version
python3 --version
```

## 🚀 **Step 3: Start Discord Bridge Bot**

### **Method 1: Automatic Setup (Easiest)**
1. **Navigate to MCC folder**: `Documents\augment-projects\mcc\MCC-Release`
2. **Double-click**: `start-discord-bridge.bat`
3. **Wait for setup**: The script will automatically:
   - Create virtual environment
   - Install dependencies (aiohttp, aiofiles, watchdog)
   - Start the Discord bridge bot

### **Method 2: Manual Setup**
Open **Command Prompt** in the MCC folder and run:
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment (Windows)
venv\Scripts\activate.bat

# Install dependencies
pip install -r requirements.txt

# Start Discord bridge bot
python discord-bridge-bot.py
```

## 📊 **Expected Output When Working**

### **Successful Startup:**
```
🚀 Starting MCC Discord Bridge Bot
Monitoring log file: inventory2.txt
Target channels: ISTOP=1401451296711507999, LB=1401451313216094383
Discord API session initialized
Processing log file: inventory2.txt
Processed 6 Discord messages from log file
Starting to monitor log file: inventory2.txt
```

### **Message Processing:**
```
📤 Sending Discord message to channel 1401451296711507999
✅ Successfully sent message 1234567890 to channel 1401451296711507999
📤 Sending Discord message to channel 1401451313216094383
✅ Successfully sent message 1234567891 to channel 1401451313216094383
```

## 🔄 **Step 4: Integration with MCC Multi-Phase Bot**

### **Complete Workflow:**
1. **Start Discord Bridge Bot** (this script)
2. **Start MCC** with multi-phase bot: `/script multi-phase-bot`
3. **Monitor both logs**:
   - **MCC Console**: Shows embed generation in simulation mode
   - **Bridge Bot Console**: Shows actual Discord message delivery

### **Expected Integration:**
- **MCC generates embeds** → `inventory2.txt` log file
- **Bridge bot detects embeds** → Parses JSON content
- **Bridge bot sends to Discord** → HTTP POST to Discord API
- **Discord channels receive messages** → Full integration achieved!

## 🛠️ **Troubleshooting**

### **"Python not found" Error:**
- **Reinstall Python** with "Add to PATH" checked
- **Restart Command Prompt** after installation
- **Try `py` instead of `python`**

### **"pip not found" Error:**
- **Python installation incomplete**
- **Reinstall Python** with all options checked

### **"Module not found" Error:**
- **Run**: `pip install -r requirements.txt`
- **Ensure virtual environment** is activated

### **"Discord API error 401":**
- **Check bot token** in `discord-bridge-config.json`
- **Verify bot permissions** in Discord server

### **"Log file not found":**
- **Ensure `inventory2.txt` exists** in the same folder
- **Run MCC multi-phase bot** to generate log entries
- **Update `mcc_log_file` path** in config if needed

## 📁 **File Structure Check**

Ensure these files are in `Documents\augment-projects\mcc\MCC-Release`:
```
✅ discord-bridge-bot.py          # Main bridge bot script
✅ discord-bridge-config.json     # Configuration file
✅ requirements.txt                # Python dependencies
✅ start-discord-bridge.bat       # Windows startup script
✅ inventory2.txt                  # MCC log file (generated by multi-phase bot)
```

## 🎯 **Success Indicators**

### **✅ Python Setup Complete:**
- `python --version` shows Python 3.8+
- `pip --version` shows pip version
- No "command not found" errors

### **✅ Bridge Bot Working:**
- Discord API session initialized
- Log file monitoring active
- No error messages in console

### **✅ Full Integration Working:**
- MCC bot generates embeds in simulation mode
- Bridge bot detects and processes embed content
- Actual Discord messages appear in target channels
- Message content matches MCC-generated embeds

## 🚀 **Quick Test**

### **Test Discord Bridge Bot:**
1. **Start bridge bot**: `python discord-bridge-bot.py`
2. **Check startup messages**: Should show monitoring status
3. **Verify Discord API**: Should show session initialized
4. **Check existing log**: Should process existing messages from `inventory2.txt`

### **Test with MCC:**
1. **Keep bridge bot running**
2. **Start MCC**: Run multi-phase bot with `/script multi-phase-bot`
3. **Monitor both consoles**: 
   - MCC: Shows simulation mode embeds
   - Bridge: Shows actual Discord delivery
4. **Check Discord channels**: Verify messages appear

## 📞 **Next Steps After Python Installation**

1. **Install Python** using instructions above
2. **Restart Command Prompt** to refresh PATH
3. **Navigate to MCC folder**: `cd Documents\augment-projects\mcc\MCC-Release`
4. **Run startup script**: `start-discord-bridge.bat`
5. **Verify bridge bot startup**: Look for success messages
6. **Start MCC multi-phase bot**: `/script multi-phase-bot`
7. **Check Discord channels**: Verify actual message delivery

**Once Python is installed, the Discord bridge bot will provide full Discord integration for your MCC multi-phase bot!** 🎯✅
