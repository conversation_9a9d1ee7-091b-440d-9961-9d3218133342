#!/usr/bin/env python3
"""
Test script to identify and fix control character issues in JSON messages
"""

import json
import re
import uuid
from datetime import datetime

def identify_control_characters(text):
    """Identify control characters in text"""
    control_chars = []
    for i, char in enumerate(text):
        if ord(char) < 32 and char not in ['\n', '\r', '\t']:
            control_chars.append({
                'position': i,
                'character': repr(char),
                'unicode': ord(char),
                'hex': hex(ord(char))
            })
    return control_chars

def create_problematic_message():
    """Create a message that might contain control characters like those from MCC"""
    
    # Simulate problematic characters that might come from Minecraft formatting
    problematic_text = "������ Spartan Chestplate ������"  # Unicode replacement characters
    problematic_text2 = "#2: ������NIP������ (9 points)"  # From console logs
    
    # Create embed with potentially problematic content
    embed_data = {
        "embeds": [{
            "title": "🏝️ Top Islands Data",
            "description": "Island rankings with special characters",
            "color": 39423,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏝️ #1: Revenants (10 points)",
                    "value": f"**All Time:** $1.04B\n**Weekly:** $242.3M\n**Growth:** {problematic_text}",
                    "inline": False
                },
                {
                    "name": problematic_text2,
                    "value": "**All Time:** $670.37M\n**Weekly:** $143.43M\n**Growth:** Insufficient data",
                    "inline": False
                }
            ]
        }]
    }
    
    # Convert to JSON string (this is what the MCC bot does)
    embed_json_str = json.dumps(embed_data, separators=(',', ':'))
    
    # Create message data
    message_data = {
        "id": "886ce2d9-test-control-chars",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": embed_json_str,
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create"
    }
    
    return message_data, embed_json_str

def test_json_with_control_characters():
    """Test JSON parsing with various control character scenarios"""
    
    print("🔧 Testing Control Character Issues")
    print("=" * 60)
    
    # Test 1: Create message with problematic characters
    print("\n📊 Test 1: Message with Unicode Replacement Characters")
    print("-" * 50)
    
    message_data, embed_json = create_problematic_message()
    
    try:
        # Serialize the entire message
        message_json = json.dumps(message_data, separators=(',', ':'))
        print("✅ Message serialization successful")
        
        # Check for control characters in the JSON
        control_chars = identify_control_characters(message_json)
        if control_chars:
            print(f"⚠️ Found {len(control_chars)} control characters:")
            for char_info in control_chars[:5]:  # Show first 5
                print(f"   Position {char_info['position']}: {char_info['character']} (U+{char_info['hex'][2:].upper().zfill(4)})")
        else:
            print("✅ No control characters found in serialized JSON")
        
        # Test parsing
        parsed_message = json.loads(message_json)
        print("✅ Message parsing successful")
        
        # Test parsing embed_data_raw
        embed_data = json.loads(parsed_message['embed_data_raw'])
        print("✅ embed_data_raw parsing successful")
        
        # Check character at position 91-92 (mentioned in error)
        if len(message_json) > 92:
            char_91 = message_json[90] if len(message_json) > 90 else 'N/A'
            char_92 = message_json[91] if len(message_json) > 91 else 'N/A'
            print(f"📍 Characters at position 91-92: '{char_91}' '{char_92}' (ord: {ord(char_91) if char_91 != 'N/A' else 'N/A'}, {ord(char_92) if char_92 != 'N/A' else 'N/A'})")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON parsing error: {e}")
        print(f"   Error at character {e.pos}")
        
        # Show context around error position
        if hasattr(e, 'pos') and e.pos < len(message_json):
            start = max(0, e.pos - 10)
            end = min(len(message_json), e.pos + 10)
            context = message_json[start:end]
            print(f"   Context: {repr(context)}")
            
            # Check for control characters around error position
            error_char = message_json[e.pos] if e.pos < len(message_json) else 'EOF'
            if error_char != 'EOF':
                print(f"   Error character: {repr(error_char)} (ord: {ord(error_char)})")
                if ord(error_char) < 32:
                    print(f"   ⚠️ This is a control character!")
        
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_common_control_characters():
    """Test various control characters that might cause issues"""
    
    print("\n🔍 Test 2: Common Control Character Scenarios")
    print("-" * 50)
    
    # Test different control characters
    test_cases = [
        ("Null character", "Test\x00String"),
        ("Bell character", "Test\x07String"),
        ("Backspace", "Test\x08String"),
        ("Vertical tab", "Test\x0BString"),
        ("Form feed", "Test\x0CString"),
        ("Escape", "Test\x1BString"),
        ("Delete", "Test\x7FString"),
        ("Unicode replacement", "Test������String"),
    ]
    
    for name, test_string in test_cases:
        print(f"\n🧪 Testing {name}: {repr(test_string)}")
        
        try:
            # Try to create JSON with this string
            test_data = {"test_field": test_string}
            json_str = json.dumps(test_data)
            
            # Try to parse it back
            parsed = json.loads(json_str)
            print(f"   ✅ {name}: JSON serialization/parsing successful")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ {name}: JSON error at position {e.pos}")
            if hasattr(e, 'pos') and e.pos < len(json_str):
                error_char = json_str[e.pos]
                print(f"   Error character: {repr(error_char)} (ord: {ord(error_char)})")
        except Exception as e:
            print(f"   ❌ {name}: Unexpected error: {e}")

def test_minecraft_formatting_characters():
    """Test Minecraft-specific formatting characters that might cause issues"""
    
    print("\n🎮 Test 3: Minecraft Formatting Characters")
    print("-" * 50)
    
    # Common Minecraft formatting codes and problematic characters
    minecraft_chars = [
        ("Section sign", "§cRed Text§r"),
        ("Unicode replacement", "������"),
        ("Minecraft color codes", "§4§lBold Red§r"),
        ("Mixed formatting", "§6Gold §l§nBold Underline§r"),
        ("Null in name", "Player\x00Name"),
        ("Control in lore", "Lore\x0BLine"),
    ]
    
    for name, test_string in minecraft_chars:
        print(f"\n🎯 Testing {name}: {repr(test_string)}")
        
        # Create a realistic embed field
        embed_field = {
            "name": f"🏝️ #{name}",
            "value": f"**Player:** {test_string}\n**Score:** 100 points",
            "inline": False
        }
        
        try:
            json_str = json.dumps(embed_field, separators=(',', ':'))
            parsed = json.loads(json_str)
            print(f"   ✅ {name}: Embed field JSON successful")
            
            # Check for control characters
            control_chars = identify_control_characters(json_str)
            if control_chars:
                print(f"   ⚠️ Found {len(control_chars)} control characters")
                for char_info in control_chars[:3]:
                    print(f"      Position {char_info['position']}: {char_info['character']}")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ {name}: JSON error at position {e.pos}")
        except Exception as e:
            print(f"   ❌ {name}: Error: {e}")

def suggest_fixes():
    """Suggest fixes for control character issues"""
    
    print("\n🔧 Suggested Fixes for Control Character Issues")
    print("=" * 60)
    
    print("1. **Enhanced JSON String Escaping in C#:**")
    print("   - Add control character filtering to SimpleJsonSerializer")
    print("   - Remove or replace characters with ord < 32 (except \\n, \\r, \\t)")
    print("   - Handle Unicode replacement characters (U+FFFD)")
    
    print("\n2. **Minecraft Text Cleaning:**")
    print("   - Strip Minecraft formatting codes (§ characters)")
    print("   - Replace Unicode replacement characters with safe alternatives")
    print("   - Sanitize player names and item descriptions")
    
    print("\n3. **Discord Bridge Improvements:**")
    print("   - Add JSON validation before parsing")
    print("   - Implement fallback for malformed JSON")
    print("   - Log specific character causing issues")

if __name__ == "__main__":
    print("🔍 CONTROL CHARACTER INVESTIGATION")
    print("=" * 70)
    
    # Run all tests
    test1_passed = test_json_with_control_characters()
    test_common_control_characters()
    test_minecraft_formatting_characters()
    suggest_fixes()
    
    print("\n" + "=" * 70)
    print("📋 INVESTIGATION SUMMARY:")
    
    if test1_passed:
        print("✅ Basic message creation works")
        print("🔍 Issue likely caused by specific control characters from Minecraft")
    else:
        print("❌ Found JSON parsing issues")
        print("🔧 Control character filtering needed")
    
    print("\n💡 NEXT STEPS:")
    print("1. Update SimpleJsonSerializer to filter control characters")
    print("2. Add Minecraft text cleaning functions")
    print("3. Improve Discord bridge error handling")
    print("4. Test with real MCC bot output")
