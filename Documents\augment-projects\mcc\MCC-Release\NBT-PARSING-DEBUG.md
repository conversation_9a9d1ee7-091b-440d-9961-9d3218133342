# NBT Parsing Debug - Troubleshooting Missing Lore Extraction

## Issue Analysis

The MCC Multi-Phase Bot is successfully detecting island items in slots 13, 21, 23, 29, and 31, and shows "NBT: 2 entries" for each item, but the enhanced NBT parsing logic is not executing or logging the detailed NBT structure analysis.

### **Expected vs Actual Behavior**

**Expected Output:**
```
[Phase 2] *** PARSING NBT DATA WITH 2 ENTRIES ***
[Phase 2] NBT Key: 'display' = Type: Dictionary`2
[Phase 2] *** FOUND DISPLAY COMPOUND ***
[Phase 2] *** FOUND LORE IN DISPLAY COMPOUND ***
[Phase 2] *** PROCESSING LORE AS LIST WITH 3 ENTRIES ***
[Phase 2] Lore line 1: 'All-Time Value: $1.78B'
[Phase 2] Lore line 2: 'Weekly Value: $979.33M'
[Phase 2] Lore line 3: 'Today: +$50.69M (+5.5%)'
```

**Actual Output:**
```
[Phase 2] Slot 13: PlayerHead x1
[Phase 2]   Display: '#1: Revenants (0 points)'
[Phase 2]   NBT: 2 entries
[Phase 2] *** ISLAND DATA SLOT 13 ***
[Phase 2] Potential Island: Revenants
```

## Debugging Enhancements Added

### **1. Method Call Tracking**

Added comprehensive logging to track method execution:

```csharp
// Track ParseIslandDataFromItem calls
LogToConsole($"[Phase 2] *** CALLING ParseIslandDataFromItem FOR SLOT {slot} ***");
bool success = ParseIslandDataFromItem(item, slot);
LogToConsole($"[Phase 2] *** ParseIslandDataFromItem RETURNED: {success} FOR SLOT {slot} ***");
```

### **2. Enhanced ParseIslandDataFromItem Debugging**

```csharp
private bool ParseIslandDataFromItem(Item item, int slot)
{
    LogToConsole($"[Phase 2] *** STARTING ParseIslandDataFromItem FOR SLOT {slot} ***");
    LogToConsole($"[Phase 2] Item type: {item.Type}");
    LogToConsole($"[Phase 2] Item display name: '{item.DisplayName}'");
    LogToConsole($"[Phase 2] Item NBT null check: {item.NBT == null}");
    LogToConsole($"[Phase 2] Item NBT count: {item.NBT?.Count ?? 0}");
    
    // Detailed NBT analysis
    if (item.NBT != null && item.NBT.Count > 0)
    {
        LogToConsole($"[Phase 2] *** NBT DATA AVAILABLE - STARTING DETAILED PARSING ***");
        
        foreach (var nbtKey in item.NBT.Keys)
        {
            var nbtValue = item.NBT[nbtKey];
            LogToConsole($"[Phase 2] NBT Key: '{nbtKey}' = Type: {nbtValue?.GetType().Name ?? "null"}");
            LogToConsole($"[Phase 2] NBT Value: {nbtValue?.ToString() ?? "null"}");
        }
        
        LogToConsole($"[Phase 2] *** CALLING ParseValuesFromNBT ***");
        var parsedValues = ParseValuesFromNBT(item.NBT);
        LogToConsole($"[Phase 2] *** ParseValuesFromNBT COMPLETED ***");
    }
}
```

### **3. ParseValuesFromNBT Method Debugging**

```csharp
private NBTParseResult ParseValuesFromNBT(Dictionary<string, object> nbt)
{
    LogToConsole($"[Phase 2] *** ENTERED ParseValuesFromNBT METHOD ***");
    
    // Detailed NBT structure analysis
    foreach (var kvp in nbt)
    {
        LogToConsole($"[Phase 2] NBT Key: '{kvp.Key}' = Type: {kvp.Value?.GetType().Name ?? "null"}");
        
        // Special handling for complex types
        if (kvp.Value is Dictionary<string, object> dict)
        {
            LogToConsole($"[Phase 2] Dictionary contains {dict.Count} entries");
            foreach (var subKvp in dict)
            {
                LogToConsole($"[Phase 2]   Sub-key: '{subKvp.Key}' = Type: {subKvp.Value?.GetType().Name ?? "null"}");
            }
        }
        else if (kvp.Value is List<object> list)
        {
            LogToConsole($"[Phase 2] List contains {list.Count} entries");
            for (int i = 0; i < Math.Min(list.Count, 5); i++)
            {
                LogToConsole($"[Phase 2]   List[{i}]: {list[i]?.ToString() ?? "null"}");
            }
        }
    }
}
```

### **4. Display Compound Analysis**

```csharp
private NBTParseResult ParseDisplayCompound(Dictionary<string, object> displayCompound)
{
    LogToConsole($"[Phase 2] *** ENTERED ParseDisplayCompound METHOD ***");
    
    // Check for Lore key
    LogToConsole($"[Phase 2] Display compound contains 'Lore': {displayCompound.ContainsKey("Lore")}");
    
    if (displayCompound.ContainsKey("Lore"))
    {
        var loreData = displayCompound["Lore"];
        LogToConsole($"[Phase 2] Lore type: {loreData?.GetType().Name ?? "null"}");
        LogToConsole($"[Phase 2] Lore value: {loreData}");
    }
    else
    {
        // Check for alternative lore keys
        string[] alternativeKeys = { "lore", "description", "Description", "tooltip", "Tooltip" };
        foreach (string altKey in alternativeKeys)
        {
            if (displayCompound.ContainsKey(altKey))
            {
                LogToConsole($"[Phase 2] *** FOUND ALTERNATIVE LORE KEY: '{altKey}' ***");
            }
        }
    }
}
```

### **5. Lore Data Extraction Debugging**

```csharp
private NBTParseResult ExtractValuesFromLoreData(object loreData)
{
    LogToConsole($"[Phase 2] *** ENTERED ExtractValuesFromLoreData METHOD ***");
    LogToConsole($"[Phase 2] Lore data type: {loreData?.GetType().Name ?? "null"}");
    LogToConsole($"[Phase 2] Lore data value: {loreData}");
    
    if (loreData is List<object> loreList)
    {
        LogToConsole($"[Phase 2] *** PROCESSING LORE AS LIST WITH {loreList.Count} ENTRIES ***");
        
        for (int i = 0; i < loreList.Count; i++)
        {
            var item = loreList[i];
            string itemText = item.ToString();
            LogToConsole($"[Phase 2] Lore line {i + 1}: '{itemText}'");
        }
    }
}
```

## Debug Script

### **Comprehensive NBT Structure Analysis**

Created `debug-nbt-parsing.cs` to provide detailed NBT structure analysis:

```csharp
private void DebugNBTStructure(Dictionary<string, object> nbt, int depth)
{
    string indent = new string(' ', depth * 2);
    
    foreach (var kvp in nbt)
    {
        var key = kvp.Key;
        var value = kvp.Value;
        
        LogToConsole($"{indent}Key: '{key}' = Type: {value?.GetType().Name ?? "null"}");
        
        if (value is Dictionary<string, object> dict)
        {
            LogToConsole($"{indent}  Dictionary with {dict.Count} entries:");
            DebugNBTStructure(dict, depth + 1);
        }
        else if (value is List<object> list)
        {
            LogToConsole($"{indent}  List with {list.Count} entries:");
            for (int i = 0; i < list.Count; i++)
            {
                LogToConsole($"{indent}    [{i}]: '{list[i]?.ToString() ?? "null"}'");
            }
        }
    }
}
```

## Testing Strategy

### **1. Enhanced Production Bot**
```bash
/script multi-phase-bot
```

The enhanced bot now includes comprehensive debugging that will show:
- ✅ Method entry/exit logging
- ✅ NBT structure analysis
- ✅ Display compound parsing
- ✅ Lore data extraction attempts
- ✅ Value parsing results

### **2. Debug-Only Script**
```bash
/script debug-nbt-parsing
```

This specialized script focuses solely on NBT structure analysis and will show the complete NBT hierarchy.

## Expected Debug Output

### **1. Method Execution Tracking**
```
[Phase 2] *** CALLING ParseIslandDataFromItem FOR SLOT 13 ***
[Phase 2] *** STARTING ParseIslandDataFromItem FOR SLOT 13 ***
[Phase 2] Item type: PlayerHead
[Phase 2] Item NBT count: 2
[Phase 2] *** NBT DATA AVAILABLE - STARTING DETAILED PARSING ***
```

### **2. NBT Structure Analysis**
```
[Phase 2] *** ENTERED ParseValuesFromNBT METHOD ***
[Phase 2] *** PARSING NBT DATA WITH 2 ENTRIES ***
[Phase 2] NBT Key: 'display' = Type: Dictionary`2
[Phase 2] Dictionary contains 2 entries
[Phase 2]   Sub-key: 'Name' = Type: String
[Phase 2]   Sub-key: 'Lore' = Type: List`1
```

### **3. Display Compound Processing**
```
[Phase 2] *** ENTERED ParseDisplayCompound METHOD ***
[Phase 2] Display compound contains 'Lore': True
[Phase 2] *** FOUND LORE IN DISPLAY COMPOUND ***
[Phase 2] Lore type: List`1
```

### **4. Lore Line Extraction**
```
[Phase 2] *** ENTERED ExtractValuesFromLoreData METHOD ***
[Phase 2] *** PROCESSING LORE AS LIST WITH 3 ENTRIES ***
[Phase 2] Lore line 1: 'All-Time Value: $1.78B'
[Phase 2] Lore line 2: 'Weekly Value: $979.33M'
[Phase 2] Lore line 3: 'Today: +$50.69M (+5.5%)'
```

## Potential Issues to Investigate

### **1. Method Not Being Called**
- Check if `ParseIslandDataFromItem` is actually being executed
- Verify no exceptions are preventing method execution
- Confirm the method call is reaching the NBT parsing section

### **2. NBT Structure Different Than Expected**
- The NBT might not contain a 'display' key
- Lore might be stored under a different key name
- NBT structure might be nested differently

### **3. Type Casting Issues**
- NBT values might not be the expected Dictionary or List types
- Type checking might be failing silently
- MCC's NBT representation might be different than expected

### **4. Exception Handling**
- Exceptions might be caught and logged but not visible
- Silent failures in NBT parsing methods
- Type conversion errors

## Files Updated

- ✅ **`multi-phase-bot.cs`** - Enhanced with comprehensive debugging
- ✅ **`debug-nbt-parsing.cs`** - Specialized NBT structure analysis script
- ✅ **`NBT-PARSING-DEBUG.md`** - This debugging documentation

## Next Steps

1. **Run Enhanced Bot**: Execute the enhanced multi-phase-bot to see detailed debugging output
2. **Analyze NBT Structure**: Use the debug output to understand the actual NBT structure
3. **Identify Missing Elements**: Determine what's preventing the NBT parsing from executing
4. **Adjust Parsing Logic**: Modify the NBT parsing based on the actual structure found
5. **Test Value Extraction**: Verify that lore lines are being extracted and parsed correctly

The enhanced debugging should reveal exactly why the NBT parsing is not executing and what the actual NBT structure looks like! 🔍
