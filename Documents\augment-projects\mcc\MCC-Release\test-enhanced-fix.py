#!/usr/bin/env python3
"""
Test the enhanced control character fix
Simulates the MCC bot's enhanced CleanControlCharactersFromSourceData method
"""

import json
import uuid
from datetime import datetime

def clean_control_characters_from_source_data(input_str):
    """
    Simulate the enhanced C# CleanControlCharactersFromSourceData method
    """
    if not input_str:
        return input_str
    
    cleaned = []
    for char in input_str:
        char_code = ord(char)
        if char_code < 32 and char not in ['\n', '\r', '\t']:  # Control characters except newline, carriage return, tab
            # Replace control characters with safe alternatives or remove them
            if char_code == 0:  # Null
                # Skip null characters
                pass
            elif char_code == 7:  # Bell
                # Replace with safe alternative
                cleaned.append("[BELL]")
            elif char_code == 8:  # Backspace
                # Replace with safe alternative
                cleaned.append("[BS]")
            elif char_code == 11:  # Vertical tab
                cleaned.append(" ")  # Replace with space
            elif char_code == 12:  # Form feed
                cleaned.append(" ")  # Replace with space
            elif char_code == 27:  # Escape
                # Skip escape characters
                pass
            else:
                # For other control characters, skip them
                pass
        elif char_code == 127:  # DEL character
            # Skip DEL character
            pass
        else:
            cleaned.append(char)
    
    return ''.join(cleaned)

def simulate_enhanced_mcc_json_serializer(obj):
    """
    Simulate the enhanced MCC bot SimpleJsonSerializer with control character cleaning
    """
    
    def escape_string(text):
        """Apply the enhanced string escaping logic"""
        
        # CRITICAL FIX: Pre-clean control characters from source data
        text = clean_control_characters_from_source_data(text)
        
        # Handle backslashes first
        text = text.replace("\\", "\\\\")
        
        # Handle quotes
        text = text.replace('"', '\\"')
        
        # Handle remaining control characters (should be minimal after pre-cleaning)
        result = []
        for char in text:
            if ord(char) < 32:  # Control character
                if char == '\n':  # Newline
                    result.append(char)
                elif char == '\r':  # Carriage return
                    result.append('\\r')
                elif char == '\t':  # Tab
                    result.append('\\t')
                elif char == '\b':  # Backspace
                    result.append('\\b')
                elif char == '\f':  # Form feed
                    result.append('\\f')
                else:  # Other control characters - escape as unicode
                    result.append(f'\\u{ord(char):04X}')
            elif ord(char) == 127:  # DEL character
                result.append('\\u007F')
            else:
                result.append(char)
        
        text = ''.join(result)
        
        # Special newline handling for Discord
        if "\\\\n" in text:
            text = text.replace("\\\\n", "\\n")
        else:
            text = text.replace("\n", "\\n")
        
        return f'"{text}"'
    
    def serialize_object(obj):
        """Recursively serialize objects"""
        if obj is None:
            return "null"
        elif isinstance(obj, bool):
            return "true" if obj else "false"
        elif isinstance(obj, (int, float)):
            return str(obj)
        elif isinstance(obj, str):
            return escape_string(obj)
        elif isinstance(obj, list):
            items = [serialize_object(item) for item in obj]
            return f"[{','.join(items)}]"
        elif isinstance(obj, dict):
            items = []
            for key, value in obj.items():
                key_str = escape_string(str(key))
                value_str = serialize_object(value)
                items.append(f"{key_str}:{value_str}")
            return f"{{{','.join(items)}}}"
        else:
            return escape_string(str(obj))
    
    return serialize_object(obj)

def create_enhanced_test_message():
    """Create a test message using the enhanced control character cleaning"""
    
    # Create embed data with control characters that should be cleaned
    embed_data = {
        "embeds": [{
            "title": "🏆 Enhanced Fix Test - Control Characters Cleaned",
            "description": "Testing enhanced control character cleaning\\nMultiple lines supported",
            "color": 16766720,
            "timestamp": datetime.now().isoformat() + "Z",
            "fields": [
                {
                    "name": "🏆 Player with Backspace (CLEANED)",
                    "value": f"Player{chr(8)}Name: 100 points\\nRank: #1",  # Backspace should become [BS]
                    "inline": False
                },
                {
                    "name": "🏆 Item with Bell (CLEANED)",
                    "value": f"Item{chr(7)}Alert\\nValue: $1000",  # Bell should become [BELL]
                    "inline": False
                },
                {
                    "name": "🏆 Mixed Control Characters (CLEANED)",
                    "value": f"Test{chr(0)}{chr(8)}{chr(11)}{chr(27)}String\\nNext Line",  # Various control chars
                    "inline": False
                },
                {
                    "name": "🏆 Form Feed and DEL (CLEANED)",
                    "value": f"Text{chr(12)}Page{chr(127)}End\\nComplete",  # Form feed and DEL
                    "inline": False
                }
            ]
        }]
    }
    
    # Use enhanced serializer to create JSON string
    embed_json_str = simulate_enhanced_mcc_json_serializer(embed_data)
    
    # Create message structure
    message_data = {
        "id": "enhanced-fix-test-" + str(uuid.uuid4())[:8],
        "timestamp": datetime.now().isoformat() + "Z",
        "cycle_number": 1,
        "channel_id": "1401451313216094383",
        "channel_name": "LB",
        "embed_data_raw": embed_json_str,
        "status": "pending",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "message_type": "leaderboard",
        "action": "create"
    }
    
    return message_data

def test_enhanced_fix():
    """Test the enhanced control character fix"""
    
    print("🔧 TESTING ENHANCED CONTROL CHARACTER FIX")
    print("=" * 70)
    
    # Test the cleaning function
    test_cases = [
        ("Null character", f"Player{chr(0)}Name"),
        ("Bell character", f"Item{chr(7)}Name"),
        ("Backspace", f"Text{chr(8)}Error"),
        ("Vertical tab", f"Line{chr(11)}Break"),
        ("Form feed", f"Page{chr(12)}Break"),
        ("Escape character", f"Color{chr(27)}Code"),
        ("Delete character", f"Text{chr(127)}Char"),
        ("Mixed control chars", f"Test{chr(0)}{chr(7)}{chr(8)}{chr(11)}{chr(12)}{chr(27)}{chr(127)}End"),
    ]
    
    print("\n🧪 Testing Control Character Cleaning")
    print("-" * 50)
    
    for name, test_string in test_cases:
        print(f"\n🎯 Testing {name}: {repr(test_string)}")
        
        # Apply enhanced cleaning
        cleaned = clean_control_characters_from_source_data(test_string)
        print(f"   Cleaned: {repr(cleaned)}")
        
        # Test JSON serialization
        try:
            serialized = simulate_enhanced_mcc_json_serializer(cleaned)
            print(f"   Serialized: {serialized}")
            
            # Test JSON parsing
            parsed = json.loads(serialized)
            print(f"   ✅ {name}: Success - {repr(parsed)}")
            
        except json.JSONDecodeError as e:
            print(f"   ❌ {name}: JSON error - {e}")
        except Exception as e:
            print(f"   ❌ {name}: Unexpected error - {e}")

def write_enhanced_test_message():
    """Write enhanced test message to queue"""
    
    print("\n📝 Creating Enhanced Test Message")
    print("-" * 40)
    
    try:
        # Create test message with enhanced cleaning
        message = create_enhanced_test_message()
        
        # Write to queue file
        with open("discord_queue.json", "a", encoding='utf-8') as f:
            message_json = json.dumps(message, separators=(',', ':'))
            f.write(message_json + "\n")
        
        print(f"✅ Enhanced test message created and written to queue")
        print(f"Message ID: {message['id']}")
        print(f"JSON length: {len(message_json)}")
        
        # Test parsing the message
        parsed_message = json.loads(message_json)
        embed_data_parsed = json.loads(parsed_message['embed_data_raw'])
        
        print(f"✅ Message parsing successful")
        print(f"✅ Enhanced embed_data_raw parsing successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create enhanced test message: {e}")
        return False

if __name__ == "__main__":
    print("🔧 ENHANCED CONTROL CHARACTER FIX TEST")
    print("=" * 70)
    
    # Test the enhanced cleaning
    test_enhanced_fix()
    
    # Create and write test message
    success = write_enhanced_test_message()
    
    print("\n" + "=" * 70)
    print("📋 ENHANCED FIX TEST SUMMARY:")
    
    if success:
        print("✅ Enhanced control character cleaning working perfectly")
        print("✅ Control characters replaced with safe alternatives")
        print("✅ JSON serialization successful")
        print("✅ Test message ready for Discord bridge processing")
        
        print("\n🚀 ENHANCED CLEANING RESULTS:")
        print("• Null (\\x00) → Removed")
        print("• Bell (\\x07) → [BELL]")
        print("• Backspace (\\x08) → [BS]")
        print("• Vertical tab (\\x0B) → Space")
        print("• Form feed (\\x0C) → Space")
        print("• Escape (\\x1B) → Removed")
        print("• DEL (\\x7F) → Removed")
        
        print("\n📝 NEXT STEPS:")
        print("1. Monitor Discord bridge for successful processing")
        print("2. Verify no control character errors in logs")
        print("3. Confirm enhanced fix resolves the issue")
    else:
        print("❌ Enhanced fix test failed")
        print("🔧 Additional debugging may be needed")
