#!/usr/bin/env python3
"""
Test Interactive Leaderboard - Verify the leaderboard displays player rankings with interactive buttons
"""

import json
import os

def test_leaderboard_format():
    """Test the current discord_queue.json file for interactive leaderboard format"""
    print("🧪 Testing Interactive Leaderboard Format")
    print("=" * 60)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} lines in queue file")
        
        leaderboard_messages = 0
        interactive_messages = 0
        player_ranking_messages = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                # Test JSON parsing
                message_data = json.loads(line)
                
                channel_name = message_data.get('channel_name', 'Unknown')
                
                if channel_name == 'LB':
                    # Check if it's leaderboard data
                    embed_data = message_data.get('embed_data', {})
                    embeds = embed_data.get('embeds', [])
                    
                    if embeds:
                        title = embeds[0].get('title', '')
                        
                        if 'Leaderboard' in title or 'Top' in title:
                            leaderboard_messages += 1
                            print(f"✅ Line {i}: Leaderboard Message - '{title}'")
                            
                            # Check for interactive components (buttons)
                            components = embed_data.get('components', [])
                            if components:
                                interactive_messages += 1
                                print(f"   🔘 INTERACTIVE: Found {len(components)} button rows")
                                
                                # Analyze buttons
                                button_count = 0
                                for component in components:
                                    if component.get('type') == 1:  # Action Row
                                        buttons = component.get('components', [])
                                        for button in buttons:
                                            if button.get('type') == 2:  # Button
                                                button_count += 1
                                                label = button.get('label', 'Unknown')
                                                custom_id = button.get('custom_id', 'Unknown')
                                                print(f"     - Button: '{label}' (ID: {custom_id})")
                                
                                print(f"   📊 Total buttons: {button_count}")
                            else:
                                print(f"   ❌ NO BUTTONS: Missing interactive components")
                            
                            # Check for player rankings format
                            fields = embeds[0].get('fields', [])
                            has_player_rankings = False
                            
                            for field in fields:
                                value = field.get('value', '')
                                
                                # Look for player ranking patterns like "#1: PlayerName - Score"
                                if '#1:' in value and '#2:' in value:
                                    has_player_rankings = True
                                    print(f"   ✅ PLAYER RANKINGS: Found ranking format")
                                    
                                    # Extract sample rankings
                                    lines_in_field = value.split('\\n')
                                    for rank_line in lines_in_field[:3]:  # Show first 3
                                        if rank_line.strip():
                                            print(f"     {rank_line}")
                                    break
                            
                            if has_player_rankings:
                                player_ranking_messages += 1
                            else:
                                print(f"   ❌ NO PLAYER RANKINGS: Missing #1: PlayerName format")
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
        
        print()
        print("📊 Analysis Results:")
        print(f"   🏆 Leaderboard messages: {leaderboard_messages}")
        print(f"   🔘 Interactive messages (with buttons): {interactive_messages}")
        print(f"   👥 Player ranking messages: {player_ranking_messages}")
        
        if interactive_messages > 0 and player_ranking_messages > 0:
            print()
            print("✅ INTERACTIVE LEADERBOARD WORKING!")
            print("   - Found leaderboard messages with interactive buttons")
            print("   - Found player ranking format (#1: PlayerName - Score)")
        elif leaderboard_messages > 0:
            print()
            print("⚠️ PARTIAL IMPLEMENTATION:")
            if interactive_messages == 0:
                print("   - Missing interactive buttons")
            if player_ranking_messages == 0:
                print("   - Missing player ranking format")
        else:
            print()
            print("❌ NO LEADERBOARD DATA FOUND")
            print("   - Wait for Phase 3 to extract leaderboard data")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    print("🔧 Interactive Leaderboard Test")
    print("=" * 70)
    print()
    
    test_leaderboard_format()
    
    print()
    print("Expected features:")
    print("1. ✅ Title: 'Top 10 Players' (or 'Top 12 Players')")
    print("2. ✅ Player rankings: '#1: PlayerName - Score/Value'")
    print("3. ✅ Interactive buttons for each category")
    print("4. ✅ Button labels: 'Mobs', 'Runes', 'Pouches', 'Votes', etc.")
    print("5. ✅ Button interactions show category-specific top 10")
    print()
    print("Expected format:")
    print("🏆 Top 10 Players")
    print("#1: PlayerName - 1,234 mobs")
    print("#2: PlayerName - 1,156 mobs")
    print("#3: PlayerName - 1,089 mobs")
    print("...")
    print("[Mobs] [Runes] [Pouches] [Votes] [Quests] [XP]")
    print()
    print("If you don't see interactive features:")
    print("- Restart the MCC bot to apply the leaderboard changes")
    print("- Wait for Phase 3 to extract leaderboard data")
    print("- Check that leaderboard data extraction captures player names")
    print("- Verify Discord bridge supports button components")

if __name__ == "__main__":
    main()
