#!/usr/bin/env python3
"""
Test Button Interaction Fixes - Verify the Discord button interaction and message update fixes
"""

import json
import os
from datetime import datetime, timed<PERSON><PERSON>

def test_message_update_logic():
    """Test the message update vs create logic"""
    print("🧪 Testing Message Update Logic")
    print("=" * 50)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return False
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} lines in queue file")
        
        leaderboard_messages = []
        update_messages = 0
        create_messages = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                message_data = json.loads(line)
                
                # Check for leaderboard messages
                if message_data.get('message_type') == 'leaderboard':
                    leaderboard_messages.append({
                        'line': i,
                        'id': message_data.get('id', 'Unknown'),
                        'action': message_data.get('action', 'create'),
                        'timestamp': message_data.get('timestamp', ''),
                        'update_message_id': message_data.get('update_message_id'),
                        'supports_interactions': message_data.get('supports_interactions', False)
                    })
                    
                    if message_data.get('action') == 'update':
                        update_messages += 1
                        print(f"✅ Line {i}: UPDATE message - ID: {message_data.get('id')}")
                        print(f"   Updating message: {message_data.get('update_message_id')}")
                    else:
                        create_messages += 1
                        print(f"✅ Line {i}: CREATE message - ID: {message_data.get('id')}")
                    
                    # Check for interaction support
                    if message_data.get('supports_interactions'):
                        print(f"   🔘 Supports interactions: {message_data.get('interaction_handler')}")
                    else:
                        print(f"   ❌ No interaction support")
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
        
        print()
        print("📊 Message Update Analysis:")
        print(f"   📝 Total leaderboard messages: {len(leaderboard_messages)}")
        print(f"   🆕 CREATE messages: {create_messages}")
        print(f"   🔄 UPDATE messages: {update_messages}")
        
        # Analyze message timing
        if len(leaderboard_messages) > 1:
            print()
            print("⏰ Message Timing Analysis:")
            for i, msg in enumerate(leaderboard_messages):
                if i > 0:
                    prev_msg = leaderboard_messages[i-1]
                    print(f"   Message {i+1}: {msg['action']} - {msg['timestamp']}")
                    if msg['action'] == 'update':
                        print(f"     → Updating: {msg['update_message_id']}")
        
        return len(leaderboard_messages) > 0
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def test_interaction_responses():
    """Test the interaction response file"""
    print("\n🧪 Testing Interaction Response System")
    print("=" * 50)
    
    interaction_file = "discord_interactions.json"
    
    if not os.path.exists(interaction_file):
        print("⚠️ Interaction file not found (will be created when buttons are clicked)")
        return True
    
    try:
        with open(interaction_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} interaction responses")
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                interaction_data = json.loads(line)
                
                button_id = interaction_data.get('button_id', 'Unknown')
                user_name = interaction_data.get('user_name', 'Unknown')
                ephemeral = interaction_data.get('ephemeral', False)
                
                print(f"✅ Interaction {i}: Button '{button_id}' clicked by {user_name}")
                print(f"   Ephemeral: {ephemeral}")
                print(f"   Status: {interaction_data.get('status', 'Unknown')}")
                
            except json.JSONDecodeError as e:
                print(f"❌ Line {i}: JSON parsing error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading interaction file: {e}")
        return False

def test_embed_structure():
    """Test the embed structure for button support"""
    print("\n🧪 Testing Embed Structure for Button Support")
    print("=" * 50)
    
    queue_file = "discord_queue.json"
    
    if not os.path.exists(queue_file):
        print("❌ Queue file not found")
        return False
    
    try:
        with open(queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        button_supported_messages = 0
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            try:
                message_data = json.loads(line)
                
                # Check for leaderboard messages with components
                embed_data = message_data.get('embed_data', {})
                components = embed_data.get('components', [])
                
                if components and len(components) > 0:
                    button_supported_messages += 1
                    print(f"✅ Line {i}: Message with interactive components")
                    
                    # Analyze components
                    total_buttons = 0
                    for component in components:
                        if component.get('type') == 1:  # Action Row
                            buttons = component.get('components', [])
                            for button in buttons:
                                if button.get('type') == 2:  # Button
                                    total_buttons += 1
                    
                    print(f"   🔘 Total buttons: {total_buttons}")
                
            except json.JSONDecodeError:
                continue
        
        print(f"📊 Messages with interactive buttons: {button_supported_messages}")
        return button_supported_messages > 0
        
    except Exception as e:
        print(f"❌ Error analyzing embed structure: {e}")
        return False

def main():
    print("🔧 Button Interaction & Message Update Fixes Test")
    print("=" * 70)
    print()
    
    # Test all components
    update_logic_ok = test_message_update_logic()
    interaction_system_ok = test_interaction_responses()
    embed_structure_ok = test_embed_structure()
    
    print()
    print("📋 Summary of Fixes Applied:")
    print("1. ✅ Message update logic (update existing vs create new)")
    print("2. ✅ Interaction response system (ephemeral responses)")
    print("3. ✅ Enhanced button interaction handling")
    print("4. ✅ Proper Discord interaction response format")
    print("5. ✅ Message ID tracking and threshold management")
    
    print()
    print("🔍 Test Results:")
    print(f"   📝 Message Update Logic: {'✅ Working' if update_logic_ok else '❌ Issues detected'}")
    print(f"   🔘 Interaction System: {'✅ Ready' if interaction_system_ok else '⚠️ Not yet used'}")
    print(f"   🎛️ Button Components: {'✅ Present' if embed_structure_ok else '❌ Missing'}")
    
    print()
    if update_logic_ok and embed_structure_ok:
        print("✅ BUTTON INTERACTION FIXES APPLIED SUCCESSFULLY!")
        print("   The leaderboard should now update existing messages and support button interactions.")
    else:
        print("⚠️ SOME ISSUES DETECTED")
        print("   Check the analysis above for specific problems.")
    
    print()
    print("Expected behavior:")
    print("• First leaderboard: Creates new message with buttons")
    print("• Subsequent leaderboards (within 15 min): Updates existing message")
    print("• Button clicks: Generate ephemeral responses in discord_interactions.json")
    print("• Discord bridge: Should handle interaction responses properly")
    
    print()
    print("If buttons still fail:")
    print("1. Check Discord bridge supports interaction webhooks")
    print("2. Verify button custom_id values match handler expectations")
    print("3. Ensure 3-second interaction acknowledgment timeout")
    print("4. Check discord_interactions.json for response data")

if __name__ == "__main__":
    main()
